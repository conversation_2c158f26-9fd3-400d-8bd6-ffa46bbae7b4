(()=>{"use strict";var e={998:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.default=joplin},143:(e,t)=>{var i,n,a,o,s,r,l,d,u,c;Object.defineProperty(t,"__esModule",{value:!0}),t.ContentScriptType=t.SettingStorage=t.AppType=t.SettingItemSubType=t.SettingItemType=t.ToolbarButtonLocation=t.isContextMenuItemLocation=t.MenuItemLocation=t.ModelType=t.ImportModuleOutputFormat=t.FileSystemItem=void 0,function(e){e.File="file",e.Directory="directory"}(i||(t.FileSystemItem=i={})),function(e){e.Markdown="md",e.Html="html"}(n||(t.ImportModuleOutputFormat=n={})),function(e){e[e.Note=1]="Note",e[e.Folder=2]="Folder",e[e.Setting=3]="Setting",e[e.Resource=4]="Resource",e[e.Tag=5]="Tag",e[e.NoteTag=6]="NoteTag",e[e.Search=7]="Search",e[e.Alarm=8]="Alarm",e[e.MasterKey=9]="MasterKey",e[e.ItemChange=10]="ItemChange",e[e.NoteResource=11]="NoteResource",e[e.ResourceLocalState=12]="ResourceLocalState",e[e.Revision=13]="Revision",e[e.Migration=14]="Migration",e[e.SmartFilter=15]="SmartFilter",e[e.Command=16]="Command"}(a||(t.ModelType=a={})),function(e){e.File="file",e.Edit="edit",e.View="view",e.Note="note",e.Tools="tools",e.Help="help",e.Context="context",e.NoteListContextMenu="noteListContextMenu",e.EditorContextMenu="editorContextMenu",e.FolderContextMenu="folderContextMenu",e.TagContextMenu="tagContextMenu"}(o||(t.MenuItemLocation=o={})),t.isContextMenuItemLocation=function(e){return[o.Context,o.NoteListContextMenu,o.EditorContextMenu,o.FolderContextMenu,o.TagContextMenu].includes(e)},function(e){e.NoteToolbar="noteToolbar",e.EditorToolbar="editorToolbar"}(s||(t.ToolbarButtonLocation=s={})),function(e){e[e.Int=1]="Int",e[e.String=2]="String",e[e.Bool=3]="Bool",e[e.Array=4]="Array",e[e.Object=5]="Object",e[e.Button=6]="Button"}(r||(t.SettingItemType=r={})),function(e){e.FilePathAndArgs="file_path_and_args",e.FilePath="file_path",e.DirectoryPath="directory_path"}(l||(t.SettingItemSubType=l={})),function(e){e.Desktop="desktop",e.Mobile="mobile",e.Cli="cli"}(d||(t.AppType=d={})),function(e){e[e.Database=1]="Database",e[e.File=2]="File"}(u||(t.SettingStorage=u={})),function(e){e.MarkdownItPlugin="markdownItPlugin",e.CodeMirrorPlugin="codeMirrorPlugin"}(c||(t.ContentScriptType=c={}))},923:function(e,t,i){var n=this&&this.__awaiter||function(e,t,i,n){return new(i||(i=Promise))((function(a,o){function s(e){try{l(n.next(e))}catch(e){o(e)}}function r(e){try{l(n.throw(e))}catch(e){o(e)}}function l(e){var t;e.done?a(e.value):(t=e.value,t instanceof i?t:new i((function(e){e(t)}))).then(s,r)}l((n=n.apply(e,t||[])).next())}))};Object.defineProperty(t,"__esModule",{value:!0});const a=i(998),o=i(31),s=i(258),r=i(613);t.default=class{constructor(e,t,i){this.temporaryDirectory_=e,this.dialogFactory_=t,this.updateDialogSettings_=i,this.allDialogs_=[]}getClosedDialog_(){for(const e of this.allDialogs_)if(!e.isOpen())return e;const e=this.dialogFactory_();return this.allDialogs_.push(e),e}insertNewDrawing(e){return n(this,void 0,void 0,(function*(){const t=yield s.default.ofData(this.temporaryDirectory_,e,o.default.defaultImageTitle,".svg"),i=`![${t.htmlSafeTitle()}](:/${t.resourceId})`;return yield(e=>n(void 0,void 0,void 0,(function*(){yield a.default.commands.execute("insertText",e)})))(i),t}))}editDrawing(e,{allowSaveAsCopy:t=!0}){return n(this,void 0,void 0,(function*(){const i="image/svg+xml",a=yield s.default.fromURL(this.temporaryDirectory_,e,".svg",i);if(!a)throw new Error("Invalid resource URL!");if(a.mime!==i)return alert(o.default.notAnEditableImage(e,a.mime)),null;let r=a;const l=this.getClosedDialog_();return this.updateDialogSettings_(l),(yield l.promptForDrawing({initialData:yield r.getDataAsString(),saveCallbacks:{overwrite:e=>n(this,void 0,void 0,(function*(){console.log("Image editor: Overwriting resource..."),yield r.updateData(e)})),saveAsNew:t?e=>n(this,void 0,void 0,(function*(){console.log("Image editor: Inserting new drawing..."),r=yield this.insertNewDrawing(e)})):null}}))?r:null}))}editOrInsertDrawing(){return n(this,void 0,void 0,(function*(){const e=yield a.default.commands.execute("selectedText");if(e&&(/^:\/[a-zA-Z0-9]+$/.exec(e)||/^[a-z0-9]{32}$/.exec(e)))console.log("Attempting to edit selected resource,",e),yield this.editDrawing(e,{allowSaveAsCopy:!1});else{const e=this.getClosedDialog_();this.updateDialogSettings_(e);let t=null;yield e.promptForDrawing({initialData:void 0,saveCallbacks:{saveAsNew:e=>n(this,void 0,void 0,(function*(){t=yield this.insertNewDrawing(e)})),overwrite:e=>n(this,void 0,void 0,(function*(){if(!t)throw new Error("A new drawing must be saved once before it can be overwritten");yield t.updateData(e)}))},initialSaveMethod:r.SaveMethod.SaveAsNew})}}))}}},258:function(e,t,i){var n=this&&this.__awaiter||function(e,t,i,n){return new(i||(i=Promise))((function(a,o){function s(e){try{l(n.next(e))}catch(e){o(e)}}function r(e){try{l(n.throw(e))}catch(e){o(e)}}function l(e){var t;e.done?a(e.value):(t=e.value,t instanceof i?t:new i((function(e){e(t)}))).then(s,r)}l((n=n.apply(e,t||[])).next())}))};Object.defineProperty(t,"__esModule",{value:!0}),t.Resource=void 0;const a=i(998),o=a.default.require("fs-extra");class s{constructor(e){var t;this.tmpdir=e.tmpdir,this.resourceId=e.resourceId,this.mime=e.mime,this.title=e.title,this.fileExt=e.fileExt,this.tempfilePath=null!==(t=e.tempfilePath)&&void 0!==t?t:null}getDataAsString(e="utf8"){return n(this,void 0,void 0,(function*(){const t=yield a.default.data.get(["resources",this.resourceId,"file"]);return Buffer.from(t.body).toString(e)}))}updateData(e){return n(this,void 0,void 0,(function*(){this.tempfilePath&&(yield o.rm(this.tempfilePath),this.tempfilePath=null);const t=yield this.tmpdir.newFile(e,this.fileExt),i=[{path:t}],n={mime:this.mime,title:this.title,updated_time:Date.now(),user_updated_time:Date.now(),file_extension:this.fileExt?/^[.]?(.*)$/.exec(this.fileExt)[1]:null};yield a.default.data.put(["resources",this.resourceId],null,n,i),this.tempfilePath=t}))}htmlSafeTitle(){return this.title.replace(/[&]/g,"&amp;").replace(/[<]/g,"&lt;").replace(/[>]/g,"&gt;").replace(/["]/g,"&quot;").replace(/[']/g,"&#39;")}static fromURL(e,t,i,o){var r;return n(this,void 0,void 0,(function*(){const n=/^(?:file|joplin[-a-z]+):\/\/.*\/([a-zA-Z0-9]+)[.]\w+(?:[?#]|$)/.exec(t),l=/^:\/([a-zA-Z0-9]+)$/.exec(t);let d=null;if(n?d=n[1]:l?d=l[1]:/^[a-z0-9]{32}$/.exec(t)&&(d=t),null===d)return null;const u=yield a.default.data.get(["resources",d]);return u?new s({tmpdir:e,resourceId:u.id,mime:null!==(r=u.mime)&&void 0!==r?r:o,title:u.title,fileExt:i,tempfilePath:void 0}):null}))}static ofData(e,t,i,o){return n(this,void 0,void 0,(function*(){const n={title:i.endsWith(o)?i:`${i}${o}`,created_time:Date.now(),updated_time:Date.now(),file_extension:o},r=[{path:yield e.newFile(t,o)}],l=yield a.default.data.post(["resources"],null,n,r);return new s({tmpdir:e,resourceId:l.id,mime:l.mime,title:l.title,fileExt:o})}))}}t.Resource=s,t.default=s},108:function(e,t,i){var n=this&&this.__awaiter||function(e,t,i,n){return new(i||(i=Promise))((function(a,o){function s(e){try{l(n.next(e))}catch(e){o(e)}}function r(e){try{l(n.throw(e))}catch(e){o(e)}}function l(e){var t;e.done?a(e.value):(t=e.value,t instanceof i?t:new i((function(e){e(t)}))).then(s,r)}l((n=n.apply(e,t||[])).next())}))};Object.defineProperty(t,"__esModule",{value:!0});const a=i(998),o=i(857),s=i(928),r=a.default.require("fs-extra"),l=[];class d{constructor(e){this.path=e,this.fileIdCounter=0,l.push(this)}nextFilepath(e=""){if(null===this.path)throw new Error("Temporary directory does not exist. Possible use after destroySync.");return this.fileIdCounter++,s.join(this.path,`tmp${this.fileIdCounter}${null!=e?e:""}`)}newFile(e,t=""){return n(this,void 0,void 0,(function*(){const i=this.nextFilepath(t),n=yield r.open(i,"w");return yield r.writeFile(n,e),yield r.close(n),i}))}destroySync(){this.path&&(r.rmSync(this.path,{recursive:!0}),this.path=null)}static create(){return n(this,void 0,void 0,(function*(){const e=yield r.mkdtemp(s.join((0,o.tmpdir)(),"joplin-js-draw"));return new d(e)}))}}t.default=d;const u=()=>{for(const e of l)e.destroySync()};"undefined"!=typeof window?window.addEventListener("beforeunload",(()=>{u()})):process.on("exit",(()=>{u()}))},480:function(e,t,i){var n=this&&this.__awaiter||function(e,t,i,n){return new(i||(i=Promise))((function(a,o){function s(e){try{l(n.next(e))}catch(e){o(e)}}function r(e){try{l(n.throw(e))}catch(e){o(e)}}function l(e){var t;e.done?a(e.value):(t=e.value,t instanceof i?t:new i((function(e){e(t)}))).then(s,r)}l((n=n.apply(e,t||[])).next())}))};Object.defineProperty(t,"__esModule",{value:!0}),t.getAutosave=t.hasAutosave=t.clearAutosave=t.autosave=void 0;const a=i(998),o=i(928),s=a.default.require("fs-extra"),r=()=>n(void 0,void 0,void 0,(function*(){const e=yield a.default.plugins.dataDir();return o.join(e,"autosaves")})),l="autosave.svg";t.autosave=e=>n(void 0,void 0,void 0,(function*(){const t=yield n(void 0,void 0,void 0,(function*(){const e=yield r();return(yield s.pathExists(e))||(yield s.mkdir(e)),e}));yield s.writeFile(o.join(t,l),e)})),t.clearAutosave=()=>n(void 0,void 0,void 0,(function*(){const e=yield r();(yield s.pathExists(e))&&(yield s.remove(e))})),t.hasAutosave=()=>n(void 0,void 0,void 0,(function*(){const e=yield r();return!!(yield s.pathExists(o.join(e,l)))})),t.getAutosave=()=>n(void 0,void 0,void 0,(function*(){if(yield(0,t.hasAutosave)()){const e=yield r();return yield s.readFile(o.join(e,l),"utf-8")}return null}))},921:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.pluginPrefix=void 0,t.pluginPrefix="jop-freehand-drawing-jsdraw-plugin-"},764:function(e,t,i){var n,a,o,s,r,l=this&&this.__awaiter||function(e,t,i,n){return new(i||(i=Promise))((function(a,o){function s(e){try{l(n.next(e))}catch(e){o(e)}}function r(e){try{l(n.throw(e))}catch(e){o(e)}}function l(e){var t;e.done?a(e.value):(t=e.value,t instanceof i?t:new i((function(e){e(t)}))).then(s,r)}l((n=n.apply(e,t||[])).next())}))},d=this&&this.__classPrivateFieldGet||function(e,t,i,n){if("a"===i&&!n)throw new TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!n:!t.has(e))throw new TypeError("Cannot read private member from an object whose class did not declare it");return"m"===i?n:"a"===i?n.call(e):n?n.value:t.get(e)},u=this&&this.__classPrivateFieldSet||function(e,t,i,n,a){if("m"===n)throw new TypeError("Private method is not writable");if("a"===n&&!a)throw new TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!a:!t.has(e))throw new TypeError("Cannot write private member to an object whose class did not declare it");return"a"===n?a.call(e,i):a?a.value=i:t.set(e,i),i};Object.defineProperty(t,"__esModule",{value:!0});const c=i(480),f=i(31),g=i(613),v=i(134),p=i(668),h=i(998);n=new WeakMap,a=new WeakMap,o=new WeakMap,s=new WeakMap,r=new WeakMap,t.default=class{constructor(e){this.tempDir=e,n.set(this,12e4),a.set(this,g.ToolbarType.Default),o.set(this,g.EditorStyle.MatchJoplin),s.set(this,Object.create(null)),r.set(this,!1)}isOpen(){return d(this,r,"f")}initializeDialog(){return l(this,void 0,void 0,(function*(){const e=yield h.default.settings.globalValue("locale");yield this.setHtml([`<input type='hidden' value='${(0,p.escapeHtml)(e)}' id='default-locale-data'/>`].join("\n")),yield this.setDialogButtons([{id:"cancel"}]),yield this.addScript("./dialog/webview/webview.js"),yield this.addScript("./dialog/webview/webview.css")}))}setAutosaveInterval(e){u(this,n,e,"f")}setToolbarType(e){u(this,a,e,"f")}setStyleMode(e){u(this,o,e,"f")}setKeyboardShortcuts(e){for(const t in e)d(this,s,"f")[t]=[...e[t]]}setCanFullscreen(e){}promptForDrawing(e){var t;return l(this,void 0,void 0,(function*(){yield this.initializeDialog();let i=null!==(t=e.initialSaveMethod)&&void 0!==t?t:null,p=!1;e.saveCallbacks.saveAsNew||(i=g.SaveMethod.Overwrite);const h=t=>l(this,void 0,void 0,(function*(){try{if(i===g.SaveMethod.SaveAsNew){if(!e.saveCallbacks.saveAsNew)throw new Error("saveAsNew save callback not defined");yield e.saveCallbacks.saveAsNew(t)}else{if(i!==g.SaveMethod.Overwrite)throw new Error("saveOption must be either saveAsNew or overwrite");yield e.saveCallbacks.overwrite(t)}this.postMessage({type:g.MessageType.SaveCompleted}),p=!0}catch(e){console.error("js-draw",e),alert("Not saved: "+e)}})),y=new Promise(((t,y)=>{let m=null;this.onMessage((t=>l(this,void 0,void 0,(function*(){if("saveSVG"===t.type&&!i)return m=t.data,this.setDialogButtons([{id:"ok",title:f.default.saveAndClose}]),{type:g.ResponseType.SaveResponse,waitingForSaveType:!0};if("saveSVG"===t.type&&i)return h(t.data),m=null,{type:g.ResponseType.SaveResponse,waitingForSaveType:!1};if(t.type===g.MessageType.SetSaveMethod)i=t.method;else{if(t.type===g.MessageType.GetInitialData)return this.setDialogButtons([]),{type:g.ResponseType.InitialDataResponse,autosaveIntervalMS:d(this,n,"f"),toolbarType:d(this,a,"f"),initialData:e.initialData,styleMode:d(this,o,"f"),keyboardShortcuts:d(this,s,"f")};if(t.type===g.MessageType.ShowCloseButton)this.setDialogButtons([{id:t.isSaved?"ok":"cancel",title:t.isSaved?f.default.close:f.default.discardChanges}]);else if(t.type===g.MessageType.HideButtons)this.setDialogButtons([]),m=null;else if(t.type===g.MessageType.AutosaveSVG)(0,c.clearAutosave)().then((()=>{(0,c.autosave)(t.data)}));else{if(t.type===g.MessageType.ShowImagePicker){const e=(0,v.default)(this.tempDir);return{type:g.ResponseType.ImagePickerTaskResponse,taskId:e.id}}if(t.type===g.MessageType.CancelImagePicker){const e=(0,v.taskById)(t.taskId);return e&&e.cancel(),!0}if(t.type===g.MessageType.GetImagePickerResult){const e=(0,v.taskById)(t.taskId);if(e){const t=yield e.task;return{type:g.ResponseType.ImagePickerResponse,images:t}}throw new Error(`No such task: ${t.taskId}`)}if(t.type===g.MessageType.CleanUpImagePickerResult)return(0,v.cleanUpTaskResult)(t.taskId),!0}}return!0})))),u(this,r,!0,"f"),this.showDialog().then((e=>l(this,void 0,void 0,(function*(){var n,a,o;u(this,r,!1,"f"),m&&"ok"===e.id?(null!=i||(i=null!==(o=null===(a=null===(n=e.formData)||void 0===n?void 0:n.saveOptions)||void 0===a?void 0:a.saveOption)&&void 0!==o?o:g.SaveMethod.SaveAsNew),yield h(m),t(!0)):m&&"cancel"!==e.id?y(`Unknown button ID ${e.id}`):t(p)}))))}));return yield y}))}}},931:function(e,t,i){var n=this&&this.__awaiter||function(e,t,i,n){return new(i||(i=Promise))((function(a,o){function s(e){try{l(n.next(e))}catch(e){o(e)}}function r(e){try{l(n.throw(e))}catch(e){o(e)}}function l(e){var t;e.done?a(e.value):(t=e.value,t instanceof i?t:new i((function(e){e(t)}))).then(s,r)}l((n=n.apply(e,t||[])).next())}))};Object.defineProperty(t,"__esModule",{value:!0});const a=i(998),o=i(921),s=i(764),r=a.default.views.dialogs;let l=0;class d extends s.default{constructor(e,t){super(t),this.canFullscreen=!1,this.handle=e}static create(e){const t=r.create(`${o.pluginPrefix}jsDrawDialog-${l++}`);return new d(t,e)}initializeDialog(){const e=Object.create(null,{initializeDialog:{get:()=>super.initializeDialog}});return n(this,void 0,void 0,(function*(){yield e.initializeDialog.call(this);const t=yield this.handle;yield r.setFitToContent(t,!1)}))}setCanFullscreen(e){return n(this,void 0,void 0,(function*(){if(this.canFullscreen===e)return;this.canFullscreen=e;const t=yield a.default.plugins.installationDir(),i=e?"dialogFullscreen.css":"dialogNonfullscreen.css";yield a.default.window.loadChromeCssFile(t+"/dialog/userchromeStyles/"+i)}))}setDialogButtons(e){return n(this,void 0,void 0,(function*(){const t=yield this.handle;yield r.setButtons(t,e)}))}setHtml(e){return n(this,void 0,void 0,(function*(){yield r.setHtml(yield this.handle,e)}))}addScript(e){return n(this,void 0,void 0,(function*(){yield r.addScript(yield this.handle,e)}))}postMessage(e){return n(this,void 0,void 0,(function*(){a.default.views.panels.postMessage(yield this.handle,e)}))}onMessage(e){return n(this,void 0,void 0,(function*(){a.default.views.panels.onMessage(yield this.handle,e)}))}showDialog(){return n(this,void 0,void 0,(function*(){return r.open(yield this.handle)}))}}t.default=d},751:function(e,t,i){var n=this&&this.__awaiter||function(e,t,i,n){return new(i||(i=Promise))((function(a,o){function s(e){try{l(n.next(e))}catch(e){o(e)}}function r(e){try{l(n.throw(e))}catch(e){o(e)}}function l(e){var t;e.done?a(e.value):(t=e.value,t instanceof i?t:new i((function(e){e(t)}))).then(s,r)}l((n=n.apply(e,t||[])).next())}))};Object.defineProperty(t,"__esModule",{value:!0});const a=i(764),o=i(928),s=i(998);class r extends a.default{constructor(){super(...arguments),this.win=void 0,this.onCloseListener=e=>{},this.messageOrigin=void 0}getBaseURL(){return n(this,void 0,void 0,(function*(){const e=yield s.default.plugins.installationDir();return`file://${o.posix.normalize(e)}/dialog/window/index.html`}))}getMessageOrigin(){var e;return n(this,void 0,void 0,(function*(){return null!==(e=this.messageOrigin)&&void 0!==e||(this.messageOrigin=new URL(yield this.getBaseURL()).origin),this.messageOrigin}))}addScript(e){var t;return n(this,void 0,void 0,(function*(){const i=(0,o.resolve)(yield s.default.plugins.installationDir(),e);null===(t=this.win)||void 0===t||t.postMessage({kind:"addScript",src:i},yield this.getMessageOrigin())}))}setHtml(e){var t;return n(this,void 0,void 0,(function*(){null===(t=this.win)||void 0===t||t.postMessage({kind:"setHtml",html:e},yield this.getMessageOrigin())}))}setDialogButtons(e){var t;return n(this,void 0,void 0,(function*(){null===(t=this.win)||void 0===t||t.postMessage({kind:"setButtons",buttons:e},yield this.getMessageOrigin())}))}postMessage(e){return n(this,void 0,void 0,(function*(){this.win&&this.win.postMessage({message:e},yield this.getMessageOrigin())}))}onMessage(e){var t;this.win?null===(t=this.win)||void 0===t||t.addEventListener("message",(t=>n(this,void 0,void 0,(function*(){var i;if(t.origin!==(yield this.getMessageOrigin()))return;const n=t.data.id;if(n){const a=yield e(t.data.message);console.log(t.data.message,a),null===(i=this.win)||void 0===i||i.postMessage({responseId:n,response:a})}else"dialogResult"===t.data.kind&&this.onCloseListener(t.data.result)})))):this.eventListener=e}showDialog(){const e=Object.create(null,{initializeDialog:{get:()=>super.initializeDialog}});return n(this,void 0,void 0,(function*(){const t=yield s.default.plugins.installationDir(),i=window.open(`file://${o.posix.normalize(t)}/dialog/window/index.html`,"_blank","autoHideMenuBar=true");return this.win=i,yield new Promise((e=>{i.addEventListener("load",(()=>e()))})),this.eventListener&&this.onMessage(this.eventListener),yield e.initializeDialog.call(this),new Promise((e=>{this.onCloseListener=t=>e(t)}))}))}}t.default=r},156:function(e,t,i){var n=this&&this.__awaiter||function(e,t,i,n){return new(i||(i=Promise))((function(a,o){function s(e){try{l(n.next(e))}catch(e){o(e)}}function r(e){try{l(n.throw(e))}catch(e){o(e)}}function l(e){var t;e.done?a(e.value):(t=e.value,t instanceof i?t:new i((function(e){e(t)}))).then(s,r)}l((n=n.apply(e,t||[])).next())}))};Object.defineProperty(t,"__esModule",{value:!0});const a=i(998),o=i(143),s=i(480),r=i(31),l=i(108),d=i(931),u=i(921),c=i(751),f=i(451),g=i(923);a.default.plugins.register({onStart:function(){return n(this,void 0,void 0,(function*(){(0,r.setLocale)(yield a.default.settings.globalValue("locale"));const e=yield l.default.create(),t=yield(0,f.registerSettings)(),i=new g.default(e,(()=>d.default.create(e)),t.applySettingsTo),v=new g.default(e,(()=>new c.default(e)),t.applySettingsTo),p=`${u.pluginPrefix}insertDrawing`;yield a.default.commands.register({name:p,label:r.default.insertDrawing,enabledCondition:"oneNoteSelected && !noteIsReadOnly",iconName:"fas fa-pen-alt",execute:()=>n(this,void 0,void 0,(function*(){yield i.editOrInsertDrawing()}))});const h=`${u.pluginPrefix}insertDrawing__newWindow`;yield a.default.commands.register({name:h,label:r.default.insertDrawingInNewWindow,iconName:"fas fa-pen-alt",execute:()=>n(this,void 0,void 0,(function*(){yield v.editOrInsertDrawing()}))}),yield a.default.views.toolbarButtons.create(p,p,o.ToolbarButtonLocation.EditorToolbar);const y=`${u.pluginPrefix}insertDrawingToolMenuBtn`;yield a.default.views.menuItems.create(y,p,o.MenuItemLocation.Edit);const m=`${u.pluginPrefix}restoreAutosave`,w=`${u.pluginPrefix}deleteAutosave`;yield a.default.commands.register({name:m,label:r.default.restoreFromAutosave,iconName:"fas fa-floppy-disk",execute:()=>n(this,void 0,void 0,(function*(){const e=yield(0,s.getAutosave)();e?yield i.insertNewDrawing(e):yield a.default.views.dialogs.showMessageBox(r.default.noSuchAutosaveExists)}))}),yield a.default.commands.register({name:w,label:r.default.deleteAutosave,iconName:"fas fa-trash-can",execute:()=>n(this,void 0,void 0,(function*(){yield(0,s.clearAutosave)()}))});const b="jsdraw__markdownIt_editDrawingButton";yield a.default.contentScripts.register(o.ContentScriptType.MarkdownItPlugin,b,"./contentScripts/markdownIt.js"),yield a.default.contentScripts.register(o.ContentScriptType.CodeMirrorPlugin,"jsdraw__codeMirrorContentScriptId","./contentScripts/codeMirror.js"),yield a.default.contentScripts.onMessage(b,(e=>n(this,void 0,void 0,(function*(){var t;return null===(t=yield i.editDrawing(e,{allowSaveAsCopy:!0}))||void 0===t?void 0:t.resourceId}))))}))}})},31:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.getLocales=t.setLocale=void 0;const i={insertDrawing:"Insert Drawing",insertDrawingInNewWindow:"Insert drawing in new window",restoreFromAutosave:"Restore from autosaved drawing",deleteAutosave:"Delete all autosaved drawings",noSuchAutosaveExists:"No autosave exists",discardChanges:"Discard changes",defaultImageTitle:"Freehand Drawing",edit:"Edit",close:"Close",saveAndClose:"Save and close",overwriteExisting:"Overwrite existing",saveAsNewDrawing:"Save as a new drawing",clickBelowToContinue:"Done! Click below to continue.",discardUnsavedChanges:"Discard unsaved changes?",resumeEditing:"Resume editing",saveAndResumeEditing:"Save and resume editing",saveChanges:"Save changes",exitInstructions:"All changes saved! Click below to exit.",settingsPaneDescription:"Settings for the Freehand Drawing image editor.",setting__disableFullScreen:"Dialog mode",setting__disableFullScreenDescription:"Enabling this setting causes the editor to only partially fill the Joplin window.",setting__autosaveIntervalSettingLabel:"Autosave interval (minutes)",setting__autosaveIntervalSettingDescription:'Adjusts how often a backup copy of the current drawing is created. The most recent autosave can be restored by searching for ":restore autosave" in the command palette (ctrl+shift+p or cmd+shift+p on MacOS) and clicking "Restore from autosaved drawing". If this setting is set to zero, autosaves are created every two minutes.',setting__themeLabel:"Theme",setting__toolbarTypeLabel:"Toolbar type",setting__toolbarTypeDescription:"This setting switches between possible toolbar user interfaces for the image editor.",setting__keyboardShortcuts:"Keyboard shortcuts",toolbarTypeDefault:"Default",toolbarTypeSidebar:"Sidebar",toolbarTypeDropdown:"Dropdown",styleMatchJoplin:"Match Joplin",styleJsDrawLight:"Light",styleJsDrawDark:"Dark",images:"Images",pdfs:"PDFs",allFiles:"All Files",loadLargePdf:e=>`A selected file is a large PDF (${e} pages). Loading it may take some time and increase the size of the current drawing. Continue?`,notAnEditableImage:(e,t)=>`Resource ${e} is not an editable image. Unable to edit resource of type ${t}.`},n={de:{insertDrawing:"Zeichnung einfügen",restoreFromAutosave:"Automatische Sicherung wiederherstellen",deleteAutosave:"Alle automatischen Sicherungen löschen",noSuchAutosaveExists:"Keine automatischen Sicherungen vorhanden",discardChanges:"Änderungen verwerfen",defaultImageTitle:"Freihand-Zeichnen",edit:"Bearbeiten",close:"Schließen",overwriteExisting:"Existierende Zeichnung überschreiben",saveAsNewDrawing:"Als neue Zeichnung speichern",clickBelowToContinue:"Fertig! Klicke auf „Ok“ um fortzufahen.",discardUnsavedChanges:"Ungespeicherte Änderungen verwerfen?",resumeEditing:"Bearbeiten fortfahren",notAnEditableImage:(e,t)=>`Die Ressource ${e} ist kein bearbeitbares Bild. Ressource vom Typ ${t} kann nicht bearbeitet werden.`},en:i,es:{insertDrawing:"Añada dibujo",restoreFromAutosave:"Resturar al autoguardado",deleteAutosave:"Borrar el autoguardado",noSuchAutosaveExists:"No autoguardado existe",discardChanges:"Descartar cambios",defaultImageTitle:"Dibujo",edit:"Editar",close:"Cerrar",saveAndClose:"Guardar y cerrar",overwriteExisting:"Sobrescribir existente",saveAsNewDrawing:"Guardar como dibujo nuevo",clickBelowToContinue:"Guardado. Ponga «ok» para continuar.",discardUnsavedChanges:"¿Descartar cambios no guardados?",resumeEditing:"Continuar editando",saveAndResumeEditing:"Guardar y continuar editando"},ro:{insertDrawing:"Inserează un desen",insertDrawingInNewWindow:"Inserează un desen într-o fereastră nouă",restoreFromAutosave:"Restaurează dintr-un desen salvat automat",deleteAutosave:"Șterge toate desenele salvate automat",noSuchAutosaveExists:"Nicio salvare automată nu există",discardChanges:"Anulează modificările",defaultImageTitle:"Desen liber",edit:"Editează",close:"Închide",saveAndClose:"Salvează și închide",overwriteExisting:"Suprascrie existent",saveAsNewDrawing:"Salvează ca desen nou",clickBelowToContinue:"Gata! Fă clic mai jos pentru a continua.",discardUnsavedChanges:"Anulezi modificările nesalvate?",resumeEditing:"Continuă editarea",saveAndResumeEditing:"Salvează și continuă editarea",saveChanges:"Salvează modificările",exitInstructions:"Toate modificările au fost salvate! Fă clic mai jos pentru a ieși.",settingsPaneDescription:"Setări pentru editorul de imagine liber.",setting__disableFullScreen:"Mod dialog",setting__disableFullScreenDescription:"Activarea acestei opțiuni face ca editorul să acopere doar parțial fereastra Joplin.",setting__autosaveIntervalSettingLabel:"Interval salvare automată (minute)",setting__autosaveIntervalSettingDescription:'Ajustează cât de des se face o copie de siguranță a desenului curent. Cea mai recentă versiune salvată automat poate fi restaurată căutând după ":restore autosave" în paleta de comenzi (Ctrl+Shift+P sau Cmd+Shift+P pe MacOS) și făcând clic pe „Restaurează dintr-un desen salvat automat”. Dacă acestă setare este 0, salvările automate sunt create la fiecare 2 minute.',setting__themeLabel:"Temă",setting__toolbarTypeLabel:"Tip bară de instrumente",setting__toolbarTypeDescription:"Această setare comută între posibilele interfețe pentru editorul de imagine.",setting__keyboardShortcuts:"Scurtături de la tastatură",toolbarTypeDefault:"Implicit",toolbarTypeSidebar:"Bară laterală",toolbarTypeDropdown:"Casete derulante",styleMatchJoplin:"La fel ca Joplin",styleJsDrawLight:"Luminoasă",styleJsDrawDark:"Întunecată",images:"Imagini",pdfs:"PDF-uri",allFiles:"Toate fișierele",loadLargePdf:e=>`Un fișier PDF selectat este un fișier mare (${e} de pagini). Încărcarea lui ar putea dura ceva timp și să crească dimensiunea desenului curent. Continui?`,notAnEditableImage:(e,t)=>`Resursa ${e} nu este o imagine editabilă. Nu se poate edita resursa de tipul ${t}.`}};let a,o=[],s=!1;t.setLocale=e=>{"string"==typeof e&&(e=[e]),(e=>{const t=[...e];for(let i of e){i=i.replace("_","-");const e=i.indexOf("-");-1!==e&&t.push(i.substring(0,e))}for(const e of t)if(e in n){a=n[e];break}o=t})(e),s=!0},(0,t.setLocale)(navigator.languages),t.getLocales=()=>[...o],t.default=new Proxy(i,{get(e,t){var n;s||console.warn("Accessing language data without a localization set. The default Electron locale will be used.");const o=t;return null!==(n=null==a?void 0:a[o])&&void 0!==n?n:i[o]}})},451:function(e,t,i){var n=this&&this.__awaiter||function(e,t,i,n){return new(i||(i=Promise))((function(a,o){function s(e){try{l(n.next(e))}catch(e){o(e)}}function r(e){try{l(n.throw(e))}catch(e){o(e)}}function l(e){var t;e.done?a(e.value):(t=e.value,t instanceof i?t:new i((function(e){e(t)}))).then(s,r)}l((n=n.apply(e,t||[])).next())}))};Object.defineProperty(t,"__esModule",{value:!0}),t.registerSettings=void 0;const a=i(998),o=i(31),s=i(143),r=i(613),l="disable-editor-fills-window",d="autosave-interval-minutes",u="toolbar-type",c="style-mode",f="keyboard-shortcuts",g=()=>n(void 0,void 0,void 0,(function*(){return{disableFullscreen:yield a.default.settings.value(l),autosaveInterval:yield a.default.settings.value(d),toolbarType:yield a.default.settings.value(u),styleMode:yield a.default.settings.value(c),keyboardShortcuts:yield a.default.settings.value(f)}}));t.registerSettings=()=>n(void 0,void 0,void 0,(function*(){const e="js-draw";yield a.default.settings.registerSection(e,{label:"Freehand Drawing",iconName:"fas fa-pen-alt",description:o.default.settingsPaneDescription}),yield a.default.settings.registerSettings({[u]:{public:!0,section:e,advanced:!0,label:o.default.setting__toolbarTypeLabel,description:o.default.setting__toolbarTypeDescription,isEnum:!0,type:s.SettingItemType.Int,value:0,options:{[r.ToolbarType.Default]:o.default.toolbarTypeDefault,[r.ToolbarType.Sidebar]:o.default.toolbarTypeSidebar,[r.ToolbarType.Dropdown]:o.default.toolbarTypeDropdown}},[c]:{public:!0,section:e,label:o.default.setting__themeLabel,isEnum:!0,type:s.SettingItemType.String,value:r.EditorStyle.MatchJoplin,options:{[r.EditorStyle.MatchJoplin]:o.default.styleMatchJoplin,[r.EditorStyle.JsDrawLight]:o.default.styleJsDrawLight,[r.EditorStyle.JsDrawDark]:o.default.styleJsDrawDark}},[l]:{public:!0,section:e,advanced:!0,label:o.default.setting__disableFullScreen,description:o.default.setting__disableFullScreenDescription,storage:s.SettingStorage.File,type:s.SettingItemType.Bool,value:!1},[d]:{public:!0,section:e,advanced:!0,label:o.default.setting__autosaveIntervalSettingLabel,description:o.default.setting__autosaveIntervalSettingDescription,storage:s.SettingStorage.File,type:s.SettingItemType.Int,value:2},[f]:{public:!1,section:e,label:o.default.setting__keyboardShortcuts,storage:s.SettingStorage.File,type:s.SettingItemType.Object,value:{}},locale:{public:!1,label:"Locale (Internal setting)",description:"Mirrors the global joplin.locale setting. This is done to allow the current app locale to be accessed from the renderer.",type:s.SettingItemType.String,value:""}});let t=yield g();return yield a.default.settings.onChange((e=>n(void 0,void 0,void 0,(function*(){t=yield g()})))),yield n(void 0,void 0,void 0,(function*(){const e=["locale"];for(const t of e)yield a.default.settings.setValue(t,yield a.default.settings.globalValue(t))})),{applySettingsTo:e=>((e,t)=>{let i=e.autosaveInterval;i||(i=2),t.setAutosaveInterval(60*i*1e3),t.setToolbarType(e.toolbarType),t.setStyleMode(e.styleMode),t.setKeyboardShortcuts(e.keyboardShortcuts),t.setCanFullscreen(!e.disableFullscreen)})(t,e)}}))},613:(e,t)=>{var i,n,a,o,s;Object.defineProperty(t,"__esModule",{value:!0}),t.EditorStyle=t.ToolbarType=t.ResponseType=t.SaveMethod=t.MessageType=void 0,function(e){e.GetInitialData="getInitialData",e.SaveSVG="saveSVG",e.SaveCompleted="saveCompleted",e.AutosaveSVG="autosaveSVG",e.SetSaveMethod="setSaveMethod",e.ResumeEditing="resumeEditing",e.ShowSaveAndCloseButton="showSaveAndCloseButton",e.ShowCloseButton="showCloseButton",e.HideButtons="removeButtons",e.ShowImagePicker="showImagePicker",e.GetImagePickerResult="getImagePicker",e.CancelImagePicker="cancelImagePicker",e.CleanUpImagePickerResult="cleanUpImagePicker"}(i||(t.MessageType=i={})),function(e){e.SaveAsNew="saveAsNew",e.Overwrite="overwrite"}(n||(t.SaveMethod=n={})),function(e){e.InitialDataResponse="initialDataResponse",e.SaveResponse="saveResponse",e.ImagePickerTaskResponse="imagePickerStartedResponse",e.ImagePickerResponse="imagePickerResponse"}(a||(t.ResponseType=a={})),function(e){e[e.Default=0]="Default",e[e.Sidebar=1]="Sidebar",e[e.Dropdown=2]="Dropdown"}(o||(t.ToolbarType=o={})),function(e){e.MatchJoplin="match-joplin-theme",e.JsDrawLight="js-draw-default-light",e.JsDrawDark="js-draw-default-dark"}(s||(t.EditorStyle=s={}))},668:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.escapeHtml=void 0,t.escapeHtml=e=>e.replace(/[&]/g,"&amp;").replace(/[<]/g,"&lt;").replace(/[>]/g,"&gt;").replace(/["]/g,"&quot;").replace(/[']/g,"&#39;")},927:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.default=(e,t)=>{const i=/^(\d+)\.(\d+)\.(\d+)(-.*)?$/,n=i.exec(e),a=i.exec(t);if(!n||!a)return console.warn(`Invalid version, ${n} or ${a} (expected number.number.number).`),!1;const o=parseInt(n[1]),s=parseInt(n[2]),r=parseInt(n[3]),l=parseInt(a[1]),d=parseInt(a[2]),u=parseInt(a[3]);return o>l||o===l&&(s>d||s===d&&r>u)}},134:function(e,t,i){var n=this&&this.__awaiter||function(e,t,i,n){return new(i||(i=Promise))((function(a,o){function s(e){try{l(n.next(e))}catch(e){o(e)}}function r(e){try{l(n.throw(e))}catch(e){o(e)}}function l(e){var t;e.done?a(e.value):(t=e.value,t instanceof i?t:new i((function(e){e(t)}))).then(s,r)}l((n=n.apply(e,t||[])).next())}))};Object.defineProperty(t,"__esModule",{value:!0}),t.cleanUpTaskResult=t.taskById=t.promptForImages=void 0;const a=i(998),o=i(928),s=i(31),r=i(927),{remove:l}=a.default.require("fs-extra");let d=0;const u=new Map,c=new Map,f=e=>n(void 0,void 0,void 0,(function*(){return e<20||0===(yield a.default.views.dialogs.showMessageBox(s.default.loadLargePdf(e)))}));t.promptForImages=e=>{const t=d++;let i=!1;const g={id:t,cancel:()=>{i=!0},task:n(void 0,void 0,void 0,(function*(){const d=[];try{const t=yield a.default.views.dialogs.showOpenDialog({properties:["openFile","multiSelections"],filters:yield n(void 0,void 0,void 0,(function*(){var e;const t=[{name:s.default.images,extensions:["jpeg","jpg","png","gif"]}];return(0,r.default)(null===(e=yield a.default.versionInfo())||void 0===e?void 0:e.version,"3.0.2")&&t.push({name:s.default.pdfs,extensions:["pdf"]}),t.push({name:s.default.allFiles,extensions:["*"]}),t}))});if(!t)return null;const u=[];for(const s of t){if(i)return null;if(".pdf"===(0,o.extname)(s).toLowerCase()){const t=yield a.default.imaging.getPdfInfoFromPath(s);if(!(yield f(t.pageCount)))return i=!0,null;const r=[],c=30;for(let e=0;e<=t.pageCount;e+=c){const n=e+1,o=Math.min(t.pageCount,e+c),l=yield a.default.imaging.createFromPdfPath(s,{minPage:n,maxPage:o});if(r.push(...l),i)return null}for(const t of r){if(i)return null;const s=e.nextFilepath(".jpg");yield a.default.imaging.toJpgFile(t,s),u.push({path:s,name:(0,o.basename)(s),mime:"image/jpeg"}),d.push((()=>n(void 0,void 0,void 0,(function*(){yield l(s),console.info("clean up: removed",s)}))))}}else u.push({path:s,name:(0,o.basename)(s)})}return u}finally{u.delete(t),c.set(t,(()=>{for(const e of d)e()}))}}))};return u.set(t,g),g},t.taskById=e=>u.get(e),t.cleanUpTaskResult=e=>{const t=c.get(e);t&&(t(),c.delete(e))},t.default=t.promptForImages},857:e=>{e.exports=require("os")},928:e=>{e.exports=require("path")}},t={};!function i(n){var a=t[n];if(void 0!==a)return a.exports;var o=t[n]={exports:{}};return e[n].call(o.exports,o,o.exports,i),o.exports}(156)})();