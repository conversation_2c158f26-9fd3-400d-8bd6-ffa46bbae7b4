// Generated from csvAnalyze.g4 by ANTLR 4.8
// jshint ignore: start
var antlr4 = require('antlr4/index');



var serializedATN = ["\u0003\u608b\ua72a\u8133\ub9ed\u417c\u3be7\u7786\u5964",
    "\u0002\t3\b\u0001\u0004\u0002\t\u0002\u0004\u0003\t\u0003\u0004\u0004",
    "\t\u0004\u0004\u0005\t\u0005\u0004\u0006\t\u0006\u0004\u0007\t\u0007",
    "\u0004\b\t\b\u0004\t\t\t\u0004\n\t\n\u0003\u0002\u0006\u0002\u0017\n",
    "\u0002\r\u0002\u000e\u0002\u0018\u0003\u0003\u0003\u0003\u0003\u0003",
    "\u0007\u0003\u001e\n\u0003\f\u0003\u000e\u0003!\u000b\u0003\u0003\u0003",
    "\u0003\u0003\u0003\u0004\u0003\u0004\u0003\u0005\u0003\u0005\u0003\u0006",
    "\u0003\u0006\u0003\u0007\u0003\u0007\u0003\b\u0003\b\u0003\t\u0003\t",
    "\u0003\t\u0003\n\u0003\n\u0002\u0002\u000b\u0003\u0003\u0005\u0004\u0007",
    "\u0005\t\u0006\u000b\u0007\r\b\u000f\t\u0011\u0002\u0013\u0002\u0003",
    "\u0002\u0004\u0006\u0002\u000b\f\u000f\u000f..==\u0003\u0002$$\u0002",
    "3\u0002\u0003\u0003\u0002\u0002\u0002\u0002\u0005\u0003\u0002\u0002",
    "\u0002\u0002\u0007\u0003\u0002\u0002\u0002\u0002\t\u0003\u0002\u0002",
    "\u0002\u0002\u000b\u0003\u0002\u0002\u0002\u0002\r\u0003\u0002\u0002",
    "\u0002\u0002\u000f\u0003\u0002\u0002\u0002\u0003\u0016\u0003\u0002\u0002",
    "\u0002\u0005\u001a\u0003\u0002\u0002\u0002\u0007$\u0003\u0002\u0002",
    "\u0002\t&\u0003\u0002\u0002\u0002\u000b(\u0003\u0002\u0002\u0002\r*",
    "\u0003\u0002\u0002\u0002\u000f,\u0003\u0002\u0002\u0002\u0011.\u0003",
    "\u0002\u0002\u0002\u00131\u0003\u0002\u0002\u0002\u0015\u0017\n\u0002",
    "\u0002\u0002\u0016\u0015\u0003\u0002\u0002\u0002\u0017\u0018\u0003\u0002",
    "\u0002\u0002\u0018\u0016\u0003\u0002\u0002\u0002\u0018\u0019\u0003\u0002",
    "\u0002\u0002\u0019\u0004\u0003\u0002\u0002\u0002\u001a\u001f\u0005\u0013",
    "\n\u0002\u001b\u001e\u0005\u0011\t\u0002\u001c\u001e\n\u0003\u0002\u0002",
    "\u001d\u001b\u0003\u0002\u0002\u0002\u001d\u001c\u0003\u0002\u0002\u0002",
    "\u001e!\u0003\u0002\u0002\u0002\u001f\u001d\u0003\u0002\u0002\u0002",
    "\u001f \u0003\u0002\u0002\u0002 \"\u0003\u0002\u0002\u0002!\u001f\u0003",
    "\u0002\u0002\u0002\"#\u0005\u0013\n\u0002#\u0006\u0003\u0002\u0002\u0002",
    "$%\u0007\u000b\u0002\u0002%\b\u0003\u0002\u0002\u0002&\'\u0007=\u0002",
    "\u0002\'\n\u0003\u0002\u0002\u0002()\u0007.\u0002\u0002)\f\u0003\u0002",
    "\u0002\u0002*+\u0007\u000f\u0002\u0002+\u000e\u0003\u0002\u0002\u0002",
    ",-\u0007\f\u0002\u0002-\u0010\u0003\u0002\u0002\u0002./\u0007$\u0002",
    "\u0002/0\u0007$\u0002\u00020\u0012\u0003\u0002\u0002\u000212\u0007$",
    "\u0002\u00022\u0014\u0003\u0002\u0002\u0002\u0006\u0002\u0018\u001d",
    "\u001f\u0002"].join("");


var atn = new antlr4.atn.ATNDeserializer().deserialize(serializedATN);

var decisionsToDFA = atn.decisionToState.map( function(ds, index) { return new antlr4.dfa.DFA(ds, index); });

function csvAnalyzeLexer(input) {
	antlr4.Lexer.call(this, input);
    this._interp = new antlr4.atn.LexerATNSimulator(this, atn, decisionsToDFA, new antlr4.PredictionContextCache());
    return this;
}

csvAnalyzeLexer.prototype = Object.create(antlr4.Lexer.prototype);
csvAnalyzeLexer.prototype.constructor = csvAnalyzeLexer;

Object.defineProperty(csvAnalyzeLexer.prototype, "atn", {
        get : function() {
                return atn;
        }
});

csvAnalyzeLexer.EOF = antlr4.Token.EOF;
csvAnalyzeLexer.TEXT = 1;
csvAnalyzeLexer.STRING = 2;
csvAnalyzeLexer.TAB = 3;
csvAnalyzeLexer.SEMI = 4;
csvAnalyzeLexer.COMMA = 5;
csvAnalyzeLexer.CR = 6;
csvAnalyzeLexer.LF = 7;

csvAnalyzeLexer.prototype.channelNames = [ "DEFAULT_TOKEN_CHANNEL", "HIDDEN" ];

csvAnalyzeLexer.prototype.modeNames = [ "DEFAULT_MODE" ];

csvAnalyzeLexer.prototype.literalNames = [ null, null, null, "'\t'", "';'", 
                                           "','", "'\r'", "'\n'" ];

csvAnalyzeLexer.prototype.symbolicNames = [ null, "TEXT", "STRING", "TAB", 
                                            "SEMI", "COMMA", "CR", "LF" ];

csvAnalyzeLexer.prototype.ruleNames = [ "TEXT", "STRING", "TAB", "SEMI", 
                                        "COMMA", "CR", "LF", "DOUBLEQUOTE", 
                                        "QUOTE" ];

csvAnalyzeLexer.prototype.grammarFileName = "csvAnalyze.g4";


exports.csvAnalyzeLexer = csvAnalyzeLexer;

