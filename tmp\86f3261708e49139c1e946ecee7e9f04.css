
				/*
					FOR THE MARKDOWN EDITOR
				*/

				/* Remove the indentation from the checkboxes at the root of the document
				   (otherwise they are too far right), but keep it for their children to allow
				   nested lists. Make sure this value matches the UL margin. */

				li.md-checkbox {
					list-style-type: none;
				}

				li.md-checkbox input[type=checkbox] {
					margin-left: -1.71em;
					margin-right: 0.7em;
					position: relative;
					top: 1px;
				}
				
				ul.joplin-checklist {
					list-style:none;
				}

				/*
					FOR THE RICH TEXT EDITOR
				*/

				ul.joplin-checklist li::before {
					content:"\f14a";
					font-family:"Font Awesome 5 Free";
					background-size: 16px 16px;
					pointer-events: all;
					cursor: pointer;
					width: 1em;
					height: 1em;
					margin-left: -1.3em;
					position: absolute;
					color: #32373F;
				}

				.joplin-checklist li:not(.checked)::before {
					content:"\f0c8";
				}