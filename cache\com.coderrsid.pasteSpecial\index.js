!function(e){var t={};function r(n){if(t[n])return t[n].exports;var i=t[n]={i:n,l:!1,exports:{}};return e[n].call(i.exports,i,i.exports,r),i.l=!0,i.exports}r.m=e,r.c=t,r.d=function(e,t,n){r.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:n})},r.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},r.t=function(e,t){if(1&t&&(e=r(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var n=Object.create(null);if(r.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var i in e)r.d(n,i,function(t){return e[t]}.bind(null,i));return n},r.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return r.d(t,"a",t),t},r.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r.p="",r(r.s=43)}([function(e,t,r){"use strict";var n=r(10),i=n,a=r(11).isApiWritable;t.NAMESPACE={HTML:"http://www.w3.org/1999/xhtml",XML:"http://www.w3.org/XML/1998/namespace",XMLNS:"http://www.w3.org/2000/xmlns/",MATHML:"http://www.w3.org/1998/Math/MathML",SVG:"http://www.w3.org/2000/svg",XLINK:"http://www.w3.org/1999/xlink"},t.IndexSizeError=function(){throw new n(i.INDEX_SIZE_ERR)},t.HierarchyRequestError=function(){throw new n(i.HIERARCHY_REQUEST_ERR)},t.WrongDocumentError=function(){throw new n(i.WRONG_DOCUMENT_ERR)},t.InvalidCharacterError=function(){throw new n(i.INVALID_CHARACTER_ERR)},t.NoModificationAllowedError=function(){throw new n(i.NO_MODIFICATION_ALLOWED_ERR)},t.NotFoundError=function(){throw new n(i.NOT_FOUND_ERR)},t.NotSupportedError=function(){throw new n(i.NOT_SUPPORTED_ERR)},t.InvalidStateError=function(){throw new n(i.INVALID_STATE_ERR)},t.SyntaxError=function(){throw new n(i.SYNTAX_ERR)},t.InvalidModificationError=function(){throw new n(i.INVALID_MODIFICATION_ERR)},t.NamespaceError=function(){throw new n(i.NAMESPACE_ERR)},t.InvalidAccessError=function(){throw new n(i.INVALID_ACCESS_ERR)},t.TypeMismatchError=function(){throw new n(i.TYPE_MISMATCH_ERR)},t.SecurityError=function(){throw new n(i.SECURITY_ERR)},t.NetworkError=function(){throw new n(i.NETWORK_ERR)},t.AbortError=function(){throw new n(i.ABORT_ERR)},t.UrlMismatchError=function(){throw new n(i.URL_MISMATCH_ERR)},t.QuotaExceededError=function(){throw new n(i.QUOTA_EXCEEDED_ERR)},t.TimeoutError=function(){throw new n(i.TIMEOUT_ERR)},t.InvalidNodeTypeError=function(){throw new n(i.INVALID_NODE_TYPE_ERR)},t.DataCloneError=function(){throw new n(i.DATA_CLONE_ERR)},t.nyi=function(){throw new Error("NotYetImplemented")},t.shouldOverride=function(){throw new Error("Abstract function; should be overriding in subclass.")},t.assert=function(e,t){if(!e)throw new Error("Assertion failed: "+(t||"")+"\n"+(new Error).stack)},t.expose=function(e,t){for(var r in e)Object.defineProperty(t.prototype,r,{value:e[r],writable:a})},t.merge=function(e,t){for(var r in t)e[r]=t[r]},t.documentOrder=function(e,t){return 3-(6&e.compareDocumentPosition(t))},t.toASCIILowerCase=function(e){return e.replace(/[A-Z]+/g,(function(e){return e.toLowerCase()}))},t.toASCIIUpperCase=function(e){return e.replace(/[a-z]+/g,(function(e){return e.toUpperCase()}))}},function(e,t,r){"use strict";e.exports=s;var n=r(22),i=r(25),a=r(26),o=r(0);function s(){n.call(this),this.parentNode=null,this._nextSibling=this._previousSibling=this,this._index=void 0}var c=s.ELEMENT_NODE=1,l=s.ATTRIBUTE_NODE=2,u=s.TEXT_NODE=3,h=s.CDATA_SECTION_NODE=4,d=s.ENTITY_REFERENCE_NODE=5,p=s.ENTITY_NODE=6,f=s.PROCESSING_INSTRUCTION_NODE=7,m=s.COMMENT_NODE=8,g=s.DOCUMENT_NODE=9,b=s.DOCUMENT_TYPE_NODE=10,v=s.DOCUMENT_FRAGMENT_NODE=11,_=s.NOTATION_NODE=12,y=s.DOCUMENT_POSITION_DISCONNECTED=1,w=s.DOCUMENT_POSITION_PRECEDING=2,E=s.DOCUMENT_POSITION_FOLLOWING=4,k=s.DOCUMENT_POSITION_CONTAINS=8,T=s.DOCUMENT_POSITION_CONTAINED_BY=16,S=s.DOCUMENT_POSITION_IMPLEMENTATION_SPECIFIC=32;s.prototype=Object.create(n.prototype,{baseURI:{get:o.nyi},parentElement:{get:function(){return this.parentNode&&this.parentNode.nodeType===c?this.parentNode:null}},hasChildNodes:{value:o.shouldOverride},firstChild:{get:o.shouldOverride},lastChild:{get:o.shouldOverride},previousSibling:{get:function(){var e=this.parentNode;return e?this===e.firstChild?null:this._previousSibling:null}},nextSibling:{get:function(){var e=this.parentNode,t=this._nextSibling;return e?t===e.firstChild?null:t:null}},textContent:{get:function(){return null},set:function(e){}},_countChildrenOfType:{value:function(e){for(var t=0,r=this.firstChild;null!==r;r=r.nextSibling)r.nodeType===e&&t++;return t}},_ensureInsertValid:{value:function(e,t,r){var n,i;if(!e.nodeType)throw new TypeError("not a node");switch(this.nodeType){case g:case v:case c:break;default:o.HierarchyRequestError()}switch(e.isAncestor(this)&&o.HierarchyRequestError(),null===t&&r||t.parentNode!==this&&o.NotFoundError(),e.nodeType){case v:case b:case c:case u:case f:case m:break;default:o.HierarchyRequestError()}if(this.nodeType===g)switch(e.nodeType){case u:o.HierarchyRequestError();break;case v:switch(e._countChildrenOfType(u)>0&&o.HierarchyRequestError(),e._countChildrenOfType(c)){case 0:break;case 1:if(null!==t)for(r&&t.nodeType===b&&o.HierarchyRequestError(),i=t.nextSibling;null!==i;i=i.nextSibling)i.nodeType===b&&o.HierarchyRequestError();n=this._countChildrenOfType(c),r?n>0&&o.HierarchyRequestError():(n>1||1===n&&t.nodeType!==c)&&o.HierarchyRequestError();break;default:o.HierarchyRequestError()}break;case c:if(null!==t)for(r&&t.nodeType===b&&o.HierarchyRequestError(),i=t.nextSibling;null!==i;i=i.nextSibling)i.nodeType===b&&o.HierarchyRequestError();n=this._countChildrenOfType(c),r?n>0&&o.HierarchyRequestError():(n>1||1===n&&t.nodeType!==c)&&o.HierarchyRequestError();break;case b:if(null===t)this._countChildrenOfType(c)&&o.HierarchyRequestError();else for(i=this.firstChild;null!==i&&i!==t;i=i.nextSibling)i.nodeType===c&&o.HierarchyRequestError();n=this._countChildrenOfType(b),r?n>0&&o.HierarchyRequestError():(n>1||1===n&&t.nodeType!==b)&&o.HierarchyRequestError()}else e.nodeType===b&&o.HierarchyRequestError()}},insertBefore:{value:function(e,t){this._ensureInsertValid(e,t,!0);var r=t;return r===e&&(r=e.nextSibling),this.doc.adoptNode(e),e._insertOrReplace(this,r,!1),e}},appendChild:{value:function(e){return this.insertBefore(e,null)}},_appendChild:{value:function(e){e._insertOrReplace(this,null,!1)}},removeChild:{value:function(e){if(!e.nodeType)throw new TypeError("not a node");return e.parentNode!==this&&o.NotFoundError(),e.remove(),e}},replaceChild:{value:function(e,t){return this._ensureInsertValid(e,t,!1),e.doc!==this.doc&&this.doc.adoptNode(e),e._insertOrReplace(this,t,!0),t}},contains:{value:function(e){return null!==e&&(this===e||0!=(this.compareDocumentPosition(e)&T))}},compareDocumentPosition:{value:function(e){if(this===e)return 0;if(this.doc!==e.doc||this.rooted!==e.rooted)return y+S;for(var t=[],r=[],n=this;null!==n;n=n.parentNode)t.push(n);for(n=e;null!==n;n=n.parentNode)r.push(n);if(t.reverse(),r.reverse(),t[0]!==r[0])return y+S;n=Math.min(t.length,r.length);for(var i=1;i<n;i++)if(t[i]!==r[i])return t[i].index<r[i].index?E:w;return t.length<r.length?E+T:w+k}},isSameNode:{value:function(e){return this===e}},isEqualNode:{value:function(e){if(!e)return!1;if(e.nodeType!==this.nodeType)return!1;if(!this.isEqual(e))return!1;for(var t=this.firstChild,r=e.firstChild;t&&r;t=t.nextSibling,r=r.nextSibling)if(!t.isEqualNode(r))return!1;return null===t&&null===r}},cloneNode:{value:function(e){var t=this.clone();if(e)for(var r=this.firstChild;null!==r;r=r.nextSibling)t._appendChild(r.cloneNode(!0));return t}},lookupPrefix:{value:function(e){var t;if(""===e||null==e)return null;switch(this.nodeType){case c:return this._lookupNamespacePrefix(e,this);case g:return(t=this.documentElement)?t.lookupPrefix(e):null;case p:case _:case v:case b:return null;case l:return(t=this.ownerElement)?t.lookupPrefix(e):null;default:return(t=this.parentElement)?t.lookupPrefix(e):null}}},lookupNamespaceURI:{value:function(e){var t;switch(""!==e&&void 0!==e||(e=null),this.nodeType){case c:return o.shouldOverride();case g:return(t=this.documentElement)?t.lookupNamespaceURI(e):null;case p:case _:case b:case v:return null;case l:return(t=this.ownerElement)?t.lookupNamespaceURI(e):null;default:return(t=this.parentElement)?t.lookupNamespaceURI(e):null}}},isDefaultNamespace:{value:function(e){return""!==e&&void 0!==e||(e=null),this.lookupNamespaceURI(null)===e}},index:{get:function(){var e=this.parentNode;if(this===e.firstChild)return 0;var t=e.childNodes;if(void 0===this._index||t[this._index]!==this){for(var r=0;r<t.length;r++)t[r]._index=r;o.assert(t[this._index]===this)}return this._index}},isAncestor:{value:function(e){if(this.doc!==e.doc)return!1;if(this.rooted!==e.rooted)return!1;for(var t=e;t;t=t.parentNode)if(t===this)return!0;return!1}},ensureSameDoc:{value:function(e){null===e.ownerDocument?e.ownerDocument=this.doc:e.ownerDocument!==this.doc&&o.WrongDocumentError()}},removeChildren:{value:o.shouldOverride},_insertOrReplace:{value:function(e,t,r){var n,a;(this.nodeType===v&&this.rooted&&o.HierarchyRequestError(),e._childNodes)&&(n=null===t?e._childNodes.length:t.index,this.parentNode===e&&this.index<n&&n--);r&&(t.rooted&&t.doc.mutateRemove(t),t.parentNode=null);var s=t;null===s&&(s=e.firstChild);var c=this.rooted&&e.rooted;if(this.nodeType===v){for(var l,u=[0,r?1:0],h=this.firstChild;null!==h;h=l)l=h.nextSibling,u.push(h),h.parentNode=e;var d=u.length;if(r?i.replace(s,d>2?u[2]:null):d>2&&null!==s&&i.insertBefore(u[2],s),e._childNodes)for(u[0]=null===t?e._childNodes.length:t._index,e._childNodes.splice.apply(e._childNodes,u),a=2;a<d;a++)u[a]._index=u[0]+(a-2);else e._firstChild===t&&(d>2?e._firstChild=u[2]:r&&(e._firstChild=null));if(this._childNodes?this._childNodes.length=0:this._firstChild=null,e.rooted)for(e.modify(),a=2;a<d;a++)e.doc.mutateInsert(u[a])}else{if(t===this)return;c?this._remove():this.parentNode&&this.remove(),this.parentNode=e,r?(i.replace(s,this),e._childNodes?(this._index=n,e._childNodes[n]=this):e._firstChild===t&&(e._firstChild=this)):(null!==s&&i.insertBefore(this,s),e._childNodes?(this._index=n,e._childNodes.splice(n,0,this)):e._firstChild===t&&(e._firstChild=this)),c?(e.modify(),e.doc.mutateMove(this)):e.rooted&&(e.modify(),e.doc.mutateInsert(this))}}},lastModTime:{get:function(){return this._lastModTime||(this._lastModTime=this.doc.modclock),this._lastModTime}},modify:{value:function(){if(this.doc.modclock)for(var e=++this.doc.modclock,t=this;t;t=t.parentElement)t._lastModTime&&(t._lastModTime=e)}},doc:{get:function(){return this.ownerDocument||this}},rooted:{get:function(){return!!this._nid}},normalize:{value:function(){for(var e,t=this.firstChild;null!==t;t=e)if(e=t.nextSibling,t.normalize&&t.normalize(),t.nodeType===s.TEXT_NODE)if(""!==t.nodeValue){var r=t.previousSibling;null!==r&&r.nodeType===s.TEXT_NODE&&(r.appendData(t.nodeValue),this.removeChild(t))}else this.removeChild(t)}},serialize:{value:function(){for(var e="",t=this.firstChild;null!==t;t=t.nextSibling)e+=a.serializeOne(t,this);return e}},outerHTML:{get:function(){return a.serializeOne(this,{nodeType:0})},set:o.nyi},ELEMENT_NODE:{value:c},ATTRIBUTE_NODE:{value:l},TEXT_NODE:{value:u},CDATA_SECTION_NODE:{value:h},ENTITY_REFERENCE_NODE:{value:d},ENTITY_NODE:{value:p},PROCESSING_INSTRUCTION_NODE:{value:f},COMMENT_NODE:{value:m},DOCUMENT_NODE:{value:g},DOCUMENT_TYPE_NODE:{value:b},DOCUMENT_FRAGMENT_NODE:{value:v},NOTATION_NODE:{value:_},DOCUMENT_POSITION_DISCONNECTED:{value:y},DOCUMENT_POSITION_PRECEDING:{value:w},DOCUMENT_POSITION_FOLLOWING:{value:E},DOCUMENT_POSITION_CONTAINS:{value:k},DOCUMENT_POSITION_CONTAINED_BY:{value:T},DOCUMENT_POSITION_IMPLEMENTATION_SPECIFIC:{value:S}})},function(e,t,r){"use strict";var n;try{n=r(52)}catch(e){n=r(53)}e.exports=n},function(e,t,r){"use strict";function n(e,t){if(this.type="",this.target=null,this.currentTarget=null,this.eventPhase=n.AT_TARGET,this.bubbles=!1,this.cancelable=!1,this.isTrusted=!1,this.defaultPrevented=!1,this.timeStamp=Date.now(),this._propagationStopped=!1,this._immediatePropagationStopped=!1,this._initialized=!0,this._dispatching=!1,e&&(this.type=e),t)for(var r in t)this[r]=t[r]}e.exports=n,n.CAPTURING_PHASE=1,n.AT_TARGET=2,n.BUBBLING_PHASE=3,n.prototype=Object.create(Object.prototype,{constructor:{value:n},stopPropagation:{value:function(){this._propagationStopped=!0}},stopImmediatePropagation:{value:function(){this._propagationStopped=!0,this._immediatePropagationStopped=!0}},preventDefault:{value:function(){this.cancelable&&(this.defaultPrevented=!0)}},initEvent:{value:function(e,t,r){this._initialized=!0,this._dispatching||(this._propagationStopped=!1,this._immediatePropagationStopped=!1,this.defaultPrevented=!1,this.isTrusted=!1,this.target=null,this.type=e,this.bubbles=t,this.cancelable=r)}}})},function(e,t,r){"use strict";e.exports=_;var n=r(13),i=r(0),a=i.NAMESPACE,o=r(27),s=r(1),c=r(2),l=r(26),u=r(54),h=r(10),d=r(28),p=r(14),f=r(12),m=r(15),g=r(29),b=r(30),v=Object.create(null);function _(e,t,r,n){f.call(this),this.nodeType=s.ELEMENT_NODE,this.ownerDocument=e,this.localName=t,this.namespaceURI=r,this.prefix=n,this._tagName=void 0,this._attrsByQName=Object.create(null),this._attrsByLName=Object.create(null),this._attrKeys=[]}function y(e,t,r,n,i){this.localName=t,this.prefix=null===r||""===r?null:""+r,this.namespaceURI=null===n||""===n?null:""+n,this.data=i,this._setOwnerElement(e)}function w(e){for(var t in b.call(this,e),e._attrsByQName)this[t]=e._attrsByQName[t];for(var r=0;r<e._attrKeys.length;r++)this[r]=e._attrsByLName[e._attrKeys[r]]}function E(e){this.element=e,this.updateCache()}function k(e){return function(t){return t.localName===e}}_.prototype=Object.create(f.prototype,{isHTML:{get:function(){return this.namespaceURI===a.HTML&&this.ownerDocument.isHTML}},tagName:{get:function(){if(void 0===this._tagName){var e;if(e=null===this.prefix?this.localName:this.prefix+":"+this.localName,this.isHTML){var t=v[e];t||(v[e]=t=i.toASCIIUpperCase(e)),e=t}this._tagName=e}return this._tagName}},nodeName:{get:function(){return this.tagName}},nodeValue:{get:function(){return null},set:function(){}},textContent:{get:function(){var e=[];return function e(t,r){if(t.nodeType===s.TEXT_NODE)r.push(t._data);else for(var n=0,i=t.childNodes.length;n<i;n++)e(t.childNodes[n],r)}(this,e),e.join("")},set:function(e){this.removeChildren(),null!=e&&""!==e&&this._appendChild(this.ownerDocument.createTextNode(e))}},innerHTML:{get:function(){return this.serialize()},set:i.nyi},outerHTML:{get:function(){return l.serializeOne(this,{nodeType:0})},set:function(e){var t=this.ownerDocument,r=this.parentNode;if(null!==r){r.nodeType===s.DOCUMENT_NODE&&i.NoModificationAllowedError(),r.nodeType===s.DOCUMENT_FRAGMENT_NODE&&(r=r.ownerDocument.createElement("body"));var n=t.implementation.mozHTMLParser(t._address,r);n.parse(null===e?"":String(e),!0),this.replaceWith(n._asDocumentFragment())}}},_insertAdjacent:{value:function(e,t){var r=!1;switch(e){case"beforebegin":r=!0;case"afterend":var n=this.parentNode;return null===n?null:n.insertBefore(t,r?this:this.nextSibling);case"afterbegin":r=!0;case"beforeend":return this.insertBefore(t,r?this.firstChild:null);default:return i.SyntaxError()}}},insertAdjacentElement:{value:function(e,t){if(t.nodeType!==s.ELEMENT_NODE)throw new TypeError("not an element");return e=i.toASCIILowerCase(String(e)),this._insertAdjacent(e,t)}},insertAdjacentText:{value:function(e,t){var r=this.ownerDocument.createTextNode(t);e=i.toASCIILowerCase(String(e)),this._insertAdjacent(e,r)}},insertAdjacentHTML:{value:function(e,t){var r;switch(e=i.toASCIILowerCase(String(e)),t=String(t),e){case"beforebegin":case"afterend":null!==(r=this.parentNode)&&r.nodeType!==s.DOCUMENT_NODE||i.NoModificationAllowedError();break;case"afterbegin":case"beforeend":r=this;break;default:i.SyntaxError()}r instanceof _&&(!r.ownerDocument.isHTML||"html"!==r.localName||r.namespaceURI!==a.HTML)||(r=r.ownerDocument.createElementNS(a.HTML,"body"));var n=this.ownerDocument.implementation.mozHTMLParser(this.ownerDocument._address,r);n.parse(t,!0),this._insertAdjacent(e,n._asDocumentFragment())}},children:{get:function(){return this._children||(this._children=new E(this)),this._children}},attributes:{get:function(){return this._attributes||(this._attributes=new w(this)),this._attributes}},firstElementChild:{get:function(){for(var e=this.firstChild;null!==e;e=e.nextSibling)if(e.nodeType===s.ELEMENT_NODE)return e;return null}},lastElementChild:{get:function(){for(var e=this.lastChild;null!==e;e=e.previousSibling)if(e.nodeType===s.ELEMENT_NODE)return e;return null}},childElementCount:{get:function(){return this.children.length}},nextElement:{value:function(e){e||(e=this.ownerDocument.documentElement);var t=this.firstElementChild;if(!t){if(this===e)return null;t=this.nextElementSibling}if(t)return t;for(var r=this.parentElement;r&&r!==e;r=r.parentElement)if(t=r.nextElementSibling)return t;return null}},getElementsByTagName:{value:function(e){var t;return e?(t="*"===e?function(){return!0}:this.isHTML?function(e){var t=i.toASCIILowerCase(e);return t===e?k(e):function(r){return r.isHTML?r.localName===t:r.localName===e}}(e):k(e),new u(this,t)):new c}},getElementsByTagNameNS:{value:function(e,t){var r;return r="*"===e&&"*"===t?function(){return!0}:"*"===e?k(t):"*"===t?function(e){return function(t){return t.namespaceURI===e}}(e):function(e,t){return function(r){return r.namespaceURI===e&&r.localName===t}}(e,t),new u(this,r)}},getElementsByClassName:{value:function(e){return""===(e=String(e).trim())?new c:(e=e.split(/[ \t\r\n\f]+/),new u(this,function(e){return function(t){return e.every((function(e){return t.classList.contains(e)}))}}(e)))}},getElementsByName:{value:function(e){return new u(this,function(e){return function(t){return t.namespaceURI===a.HTML&&t.getAttribute("name")===e}}(String(e)))}},clone:{value:function(){var e;e=this.namespaceURI!==a.HTML||this.prefix||!this.ownerDocument.isHTML?this.ownerDocument.createElementNS(this.namespaceURI,null!==this.prefix?this.prefix+":"+this.localName:this.localName):this.ownerDocument.createElement(this.localName);for(var t=0,r=this._attrKeys.length;t<r;t++){var n=this._attrKeys[t],i=this._attrsByLName[n].cloneNode();i._setOwnerElement(e),e._attrsByLName[n]=i,e._addQName(i)}return e._attrKeys=this._attrKeys.concat(),e}},isEqual:{value:function(e){if(this.localName!==e.localName||this.namespaceURI!==e.namespaceURI||this.prefix!==e.prefix||this._numattrs!==e._numattrs)return!1;for(var t=0,r=this._numattrs;t<r;t++){var n=this._attr(t);if(!e.hasAttributeNS(n.namespaceURI,n.localName))return!1;if(e.getAttributeNS(n.namespaceURI,n.localName)!==n.value)return!1}return!0}},_lookupNamespacePrefix:{value:function(e,t){if(this.namespaceURI&&this.namespaceURI===e&&null!==this.prefix&&t.lookupNamespaceURI(this.prefix)===e)return this.prefix;for(var r=0,n=this._numattrs;r<n;r++){var i=this._attr(r);if("xmlns"===i.prefix&&i.value===e&&t.lookupNamespaceURI(i.localName)===e)return i.localName}var a=this.parentElement;return a?a._lookupNamespacePrefix(e,t):null}},lookupNamespaceURI:{value:function(e){if(""!==e&&void 0!==e||(e=null),null!==this.namespaceURI&&this.prefix===e)return this.namespaceURI;for(var t=0,r=this._numattrs;t<r;t++){var n=this._attr(t);if(n.namespaceURI===a.XMLNS&&("xmlns"===n.prefix&&n.localName===e||null===e&&null===n.prefix&&"xmlns"===n.localName))return n.value||null}var i=this.parentElement;return i?i.lookupNamespaceURI(e):null}},getAttribute:{value:function(e){var t=this.getAttributeNode(e);return t?t.value:null}},getAttributeNS:{value:function(e,t){var r=this.getAttributeNodeNS(e,t);return r?r.value:null}},getAttributeNode:{value:function(e){e=String(e),/[A-Z]/.test(e)&&this.isHTML&&(e=i.toASCIILowerCase(e));var t=this._attrsByQName[e];return t?(Array.isArray(t)&&(t=t[0]),t):null}},getAttributeNodeNS:{value:function(e,t){e=null==e?"":String(e),t=String(t);var r=this._attrsByLName[e+"|"+t];return r||null}},hasAttribute:{value:function(e){return e=String(e),/[A-Z]/.test(e)&&this.isHTML&&(e=i.toASCIILowerCase(e)),void 0!==this._attrsByQName[e]}},hasAttributeNS:{value:function(e,t){var r=(e=null==e?"":String(e))+"|"+(t=String(t));return void 0!==this._attrsByLName[r]}},hasAttributes:{value:function(){return this._numattrs>0}},toggleAttribute:{value:function(e,t){return e=String(e),n.isValidName(e)||i.InvalidCharacterError(),/[A-Z]/.test(e)&&this.isHTML&&(e=i.toASCIILowerCase(e)),void 0===this._attrsByQName[e]?(void 0===t||!0===t)&&(this._setAttribute(e,""),!0):void 0!==t&&!1!==t||(this.removeAttribute(e),!1)}},_setAttribute:{value:function(e,t){var r,n=this._attrsByQName[e];n?Array.isArray(n)&&(n=n[0]):(n=this._newattr(e),r=!0),n.value=t,this._attributes&&(this._attributes[e]=n),r&&this._newattrhook&&this._newattrhook(e,t)}},setAttribute:{value:function(e,t){e=String(e),n.isValidName(e)||i.InvalidCharacterError(),/[A-Z]/.test(e)&&this.isHTML&&(e=i.toASCIILowerCase(e)),this._setAttribute(e,String(t))}},_setAttributeNS:{value:function(e,t,r){var n,i,a=t.indexOf(":");a<0?(n=null,i=t):(n=t.substring(0,a),i=t.substring(a+1)),""!==e&&void 0!==e||(e=null);var o,s=(null===e?"":e)+"|"+i,c=this._attrsByLName[s];c||(c=new y(this,i,n,e),o=!0,this._attrsByLName[s]=c,this._attributes&&(this._attributes[this._attrKeys.length]=c),this._attrKeys.push(s),this._addQName(c)),c.value=r,o&&this._newattrhook&&this._newattrhook(t,r)}},setAttributeNS:{value:function(e,t,r){e=null==e||""===e?null:String(e),t=String(t),n.isValidQName(t)||i.InvalidCharacterError();var o=t.indexOf(":"),s=o<0?null:t.substring(0,o);(null!==s&&null===e||"xml"===s&&e!==a.XML||("xmlns"===t||"xmlns"===s)&&e!==a.XMLNS||e===a.XMLNS&&"xmlns"!==t&&"xmlns"!==s)&&i.NamespaceError(),this._setAttributeNS(e,t,String(r))}},setAttributeNode:{value:function(e){if(null!==e.ownerElement&&e.ownerElement!==this)throw new h(h.INUSE_ATTRIBUTE_ERR);var t=null,r=this._attrsByQName[e.name];if(r){if(Array.isArray(r)||(r=[r]),r.some((function(t){return t===e})))return e;if(null!==e.ownerElement)throw new h(h.INUSE_ATTRIBUTE_ERR);r.forEach((function(e){this.removeAttributeNode(e)}),this),t=r[0]}return this.setAttributeNodeNS(e),t}},setAttributeNodeNS:{value:function(e){if(null!==e.ownerElement)throw new h(h.INUSE_ATTRIBUTE_ERR);var t=e.namespaceURI,r=(null===t?"":t)+"|"+e.localName,n=this._attrsByLName[r];return n&&this.removeAttributeNode(n),e._setOwnerElement(this),this._attrsByLName[r]=e,this._attributes&&(this._attributes[this._attrKeys.length]=e),this._attrKeys.push(r),this._addQName(e),this._newattrhook&&this._newattrhook(e.name,e.value),n||null}},removeAttribute:{value:function(e){e=String(e),/[A-Z]/.test(e)&&this.isHTML&&(e=i.toASCIILowerCase(e));var t=this._attrsByQName[e];if(t){Array.isArray(t)?t.length>2?t=t.shift():(this._attrsByQName[e]=t[1],t=t[0]):this._attrsByQName[e]=void 0;var r=t.namespaceURI,n=(null===r?"":r)+"|"+t.localName;this._attrsByLName[n]=void 0;var a=this._attrKeys.indexOf(n);this._attributes&&(Array.prototype.splice.call(this._attributes,a,1),this._attributes[e]=void 0),this._attrKeys.splice(a,1);var o=t.onchange;t._setOwnerElement(null),o&&o.call(t,this,t.localName,t.value,null),this.rooted&&this.ownerDocument.mutateRemoveAttr(t)}}},removeAttributeNS:{value:function(e,t){var r=(e=null==e?"":String(e))+"|"+(t=String(t)),n=this._attrsByLName[r];if(n){this._attrsByLName[r]=void 0;var i=this._attrKeys.indexOf(r);this._attributes&&Array.prototype.splice.call(this._attributes,i,1),this._attrKeys.splice(i,1),this._removeQName(n);var a=n.onchange;n._setOwnerElement(null),a&&a.call(n,this,n.localName,n.value,null),this.rooted&&this.ownerDocument.mutateRemoveAttr(n)}}},removeAttributeNode:{value:function(e){var t=e.namespaceURI,r=(null===t?"":t)+"|"+e.localName;return this._attrsByLName[r]!==e&&i.NotFoundError(),this.removeAttributeNS(t,e.localName),e}},getAttributeNames:{value:function(){var e=this;return this._attrKeys.map((function(t){return e._attrsByLName[t].name}))}},_getattr:{value:function(e){var t=this._attrsByQName[e];return t?t.value:null}},_setattr:{value:function(e,t){var r,n=this._attrsByQName[e];n||(n=this._newattr(e),r=!0),n.value=String(t),this._attributes&&(this._attributes[e]=n),r&&this._newattrhook&&this._newattrhook(e,t)}},_newattr:{value:function(e){var t=new y(this,e,null,null),r="|"+e;return this._attrsByQName[e]=t,this._attrsByLName[r]=t,this._attributes&&(this._attributes[this._attrKeys.length]=t),this._attrKeys.push(r),t}},_addQName:{value:function(e){var t=e.name,r=this._attrsByQName[t];r?Array.isArray(r)?r.push(e):this._attrsByQName[t]=[r,e]:this._attrsByQName[t]=e,this._attributes&&(this._attributes[t]=e)}},_removeQName:{value:function(e){var t=e.name,r=this._attrsByQName[t];if(Array.isArray(r)){var n=r.indexOf(e);i.assert(-1!==n),2===r.length?(this._attrsByQName[t]=r[1-n],this._attributes&&(this._attributes[t]=this._attrsByQName[t])):(r.splice(n,1),this._attributes&&this._attributes[t]===e&&(this._attributes[t]=r[0]))}else i.assert(r===e),this._attrsByQName[t]=void 0,this._attributes&&(this._attributes[t]=void 0)}},_numattrs:{get:function(){return this._attrKeys.length}},_attr:{value:function(e){return this._attrsByLName[this._attrKeys[e]]}},id:o.property({name:"id"}),className:o.property({name:"class"}),classList:{get:function(){var e=this;if(this._classList)return this._classList;var t=new d((function(){return e.className||""}),(function(t){e.className=t}));return this._classList=t,t},set:function(e){this.className=e}},matches:{value:function(e){return p.matches(this,e)}},closest:{value:function(e){var t=this;do{if(t.matches&&t.matches(e))return t;t=t.parentElement||t.parentNode}while(null!==t&&t.nodeType===s.ELEMENT_NODE);return null}},querySelector:{value:function(e){return p(e,this)[0]}},querySelectorAll:{value:function(e){var t=p(e,this);return t.item?t:new c(t)}}}),Object.defineProperties(_.prototype,m),Object.defineProperties(_.prototype,g),o.registerChangeHandler(_,"id",(function(e,t,r,n){e.rooted&&(r&&e.ownerDocument.delId(r,e),n&&e.ownerDocument.addId(n,e))})),o.registerChangeHandler(_,"class",(function(e,t,r,n){e._classList&&e._classList._update()})),y.prototype=Object.create(Object.prototype,{ownerElement:{get:function(){return this._ownerElement}},_setOwnerElement:{value:function(e){this._ownerElement=e,null===this.prefix&&null===this.namespaceURI&&e?this.onchange=e._attributeChangeHandlers[this.localName]:this.onchange=null}},name:{get:function(){return this.prefix?this.prefix+":"+this.localName:this.localName}},specified:{get:function(){return!0}},value:{get:function(){return this.data},set:function(e){var t=this.data;(e=void 0===e?"":e+"")!==t&&(this.data=e,this.ownerElement&&(this.onchange&&this.onchange(this.ownerElement,this.localName,t,e),this.ownerElement.rooted&&this.ownerElement.ownerDocument.mutateAttr(this,t)))}},cloneNode:{value:function(e){return new y(null,this.localName,this.prefix,this.namespaceURI,this.data)}},nodeType:{get:function(){return s.ATTRIBUTE_NODE}},nodeName:{get:function(){return this.name}},nodeValue:{get:function(){return this.value},set:function(e){this.value=e}},textContent:{get:function(){return this.value},set:function(e){null==e&&(e=""),this.value=e}}}),_._Attr=y,w.prototype=Object.create(b.prototype,{length:{get:function(){return this.element._attrKeys.length},set:function(){}},item:{value:function(e){return(e>>>=0)>=this.length?null:this.element._attrsByLName[this.element._attrKeys[e]]}}}),global.Symbol&&global.Symbol.iterator&&(w.prototype[global.Symbol.iterator]=function(){var e=0,t=this.length,r=this;return{next:function(){return e<t?{value:r.item(e++)}:{done:!0}}}}),E.prototype=Object.create(Object.prototype,{length:{get:function(){return this.updateCache(),this.childrenByNumber.length}},item:{value:function(e){return this.updateCache(),this.childrenByNumber[e]||null}},namedItem:{value:function(e){return this.updateCache(),this.childrenByName[e]||null}},namedItems:{get:function(){return this.updateCache(),this.childrenByName}},updateCache:{value:function(){var e=/^(a|applet|area|embed|form|frame|frameset|iframe|img|object)$/;if(this.lastModTime!==this.element.lastModTime){this.lastModTime=this.element.lastModTime;for(var t=this.childrenByNumber&&this.childrenByNumber.length||0,r=0;r<t;r++)this[r]=void 0;this.childrenByNumber=[],this.childrenByName=Object.create(null);for(var n=this.element.firstChild;null!==n;n=n.nextSibling)if(n.nodeType===s.ELEMENT_NODE){this[this.childrenByNumber.length]=n,this.childrenByNumber.push(n);var i=n.getAttribute("id");i&&!this.childrenByName[i]&&(this.childrenByName[i]=n);var o=n.getAttribute("name");o&&this.element.namespaceURI===a.HTML&&e.test(this.element.localName)&&!this.childrenByName[o]&&(this.childrenByName[i]=n)}}}}})},function(e,t,r){"use strict";e.exports=c;var n=r(9),i=r(20),a=r(21),o=r(0),s=r(13);function c(e){this.contextObject=e}var l={xml:{"":!0,"1.0":!0,"2.0":!0},core:{"":!0,"2.0":!0},html:{"":!0,"1.0":!0,"2.0":!0},xhtml:{"":!0,"1.0":!0,"2.0":!0}};c.prototype={hasFeature:function(e,t){var r=l[(e||"").toLowerCase()];return r&&r[t||""]||!1},createDocumentType:function(e,t,r){return s.isValidQName(e)||o.InvalidCharacterError(),new i(this.contextObject,e,t,r)},createDocument:function(e,t,r){var i,a=new n(!1,null);return i=t?a.createElementNS(e,t):null,r&&a.appendChild(r),i&&a.appendChild(i),e===o.NAMESPACE.HTML?a._contentType="application/xhtml+xml":e===o.NAMESPACE.SVG?a._contentType="image/svg+xml":a._contentType="application/xml",a},createHTMLDocument:function(e){var t=new n(!0,null);t.appendChild(new i(t,"html"));var r=t.createElement("html");t.appendChild(r);var a=t.createElement("head");if(r.appendChild(a),void 0!==e){var o=t.createElement("title");a.appendChild(o),o.appendChild(t.createTextNode(e))}return r.appendChild(t.createElement("body")),t.modclock=1,t},mozSetOutputMutationHandler:function(e,t){e.mutationHandler=t},mozGetInputMutationHandler:function(e){o.nyi()},mozHTMLParser:a}},function(e,t,r){"use strict";e.exports=s;var n=r(32),i=r(0),a=r(15),o=r(29);function s(){n.call(this)}s.prototype=Object.create(n.prototype,{substringData:{value:function(e,t){if(arguments.length<2)throw new TypeError("Not enough arguments");return t>>>=0,((e>>>=0)>this.data.length||e<0||t<0)&&i.IndexSizeError(),this.data.substring(e,e+t)}},appendData:{value:function(e){if(arguments.length<1)throw new TypeError("Not enough arguments");this.data+=String(e)}},insertData:{value:function(e,t){return this.replaceData(e,0,t)}},deleteData:{value:function(e,t){return this.replaceData(e,t,"")}},replaceData:{value:function(e,t,r){var n=this.data,a=n.length;e>>>=0,t>>>=0,r=String(r),(e>a||e<0)&&i.IndexSizeError(),e+t>a&&(t=a-e);var o=n.substring(0,e),s=n.substring(e+t);this.data=o+r+s}},isEqual:{value:function(e){return this._data===e._data}},length:{get:function(){return this.data.length}}}),Object.defineProperties(s.prototype,a),Object.defineProperties(s.prototype,o)},function(e,t,r){"use strict";var n={FILTER_ACCEPT:1,FILTER_REJECT:2,FILTER_SKIP:3,SHOW_ALL:4294967295,SHOW_ELEMENT:1,SHOW_ATTRIBUTE:2,SHOW_TEXT:4,SHOW_CDATA_SECTION:8,SHOW_ENTITY_REFERENCE:16,SHOW_ENTITY:32,SHOW_PROCESSING_INSTRUCTION:64,SHOW_COMMENT:128,SHOW_DOCUMENT:256,SHOW_DOCUMENT_TYPE:512,SHOW_DOCUMENT_FRAGMENT:1024,SHOW_NOTATION:2048};e.exports=n.constructor=n.prototype=n},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=joplin},function(e,t,r){"use strict";e.exports=x;var n=r(1),i=r(2),a=r(12),o=r(4),s=r(31),c=r(33),l=r(3),u=r(34),h=r(35),d=r(5),p=r(55),f=r(56),m=r(7),g=r(16),b=r(14),v=r(37),_=r(13),y=r(17),w=r(42),E=r(0),k=r(62),T=E.NAMESPACE,S=r(11).isApiWritable;function x(e,t){a.call(this),this.nodeType=n.DOCUMENT_NODE,this.isHTML=e,this._address=t||"about:blank",this.readyState="loading",this.implementation=new d(this),this.ownerDocument=null,this._contentType=e?"text/html":"application/xml",this.doctype=null,this.documentElement=null,this._templateDocCache=null,this._nodeIterators=null,this._nid=1,this._nextnid=2,this._nodes=[null,this],this.byId=Object.create(null),this.modclock=0}var N={event:"Event",customevent:"CustomEvent",uievent:"UIEvent",mouseevent:"MouseEvent"},C={events:"event",htmlevents:"event",mouseevents:"mouseevent",mutationevents:"mutationevent",uievents:"uievent"},A=function(e,t,r){return{get:function(){var n=e.call(this);return n?n[t]:r},set:function(r){var n=e.call(this);n&&(n[t]=r)}}};function R(e,t){var r,n,i;return""===e&&(e=null),_.isValidQName(t)||E.InvalidCharacterError(),r=null,n=t,(i=t.indexOf(":"))>=0&&(r=t.substring(0,i),n=t.substring(i+1)),null!==r&&null===e&&E.NamespaceError(),"xml"===r&&e!==T.XML&&E.NamespaceError(),"xmlns"!==r&&"xmlns"!==t||e===T.XMLNS||E.NamespaceError(),e===T.XMLNS&&"xmlns"!==r&&"xmlns"!==t&&E.NamespaceError(),{namespace:e,prefix:r,localName:n}}x.prototype=Object.create(a.prototype,{_setMutationHandler:{value:function(e){this.mutationHandler=e}},_dispatchRendererEvent:{value:function(e,t,r){var n=this._nodes[e];n&&n._dispatchEvent(new l(t,r),!0)}},nodeName:{value:"#document"},nodeValue:{get:function(){return null},set:function(){}},documentURI:{get:function(){return this._address},set:E.nyi},compatMode:{get:function(){return this._quirks?"BackCompat":"CSS1Compat"}},createTextNode:{value:function(e){return new s(this,String(e))}},createComment:{value:function(e){return new c(this,e)}},createDocumentFragment:{value:function(){return new u(this)}},createProcessingInstruction:{value:function(e,t){return _.isValidName(e)&&-1===t.indexOf("?>")||E.InvalidCharacterError(),new h(this,e,t)}},createAttribute:{value:function(e){return e=String(e),_.isValidName(e)||E.InvalidCharacterError(),this.isHTML&&(e=E.toASCIILowerCase(e)),new o._Attr(null,e,null,null,"")}},createAttributeNS:{value:function(e,t){var r=R(e=null==e||""===e?null:String(e),t=String(t));return new o._Attr(null,r.localName,r.prefix,r.namespace,"")}},createElement:{value:function(e){return e=String(e),_.isValidName(e)||E.InvalidCharacterError(),this.isHTML?(/[A-Z]/.test(e)&&(e=E.toASCIILowerCase(e)),y.createElement(this,e,null)):"application/xhtml+xml"===this.contentType?y.createElement(this,e,null):new o(this,e,null,null)},writable:S},createElementNS:{value:function(e,t){var r=R(e=null==e||""===e?null:String(e),t=String(t));return this._createElementNS(r.localName,r.namespace,r.prefix)},writable:S},_createElementNS:{value:function(e,t,r){return t===T.HTML?y.createElement(this,e,r):t===T.SVG?w.createElement(this,e,r):new o(this,e,t,r)}},createEvent:{value:function(e){e=e.toLowerCase();var t=v[N[C[e]||e]];if(t){var r=new t;return r._initialized=!1,r}E.NotSupportedError()}},createTreeWalker:{value:function(e,t,r){if(!e)throw new TypeError("root argument is required");if(!(e instanceof n))throw new TypeError("root not a node");return t=void 0===t?m.SHOW_ALL:+t,new p(e,t,r=void 0===r?null:r)}},createNodeIterator:{value:function(e,t,r){if(!e)throw new TypeError("root argument is required");if(!(e instanceof n))throw new TypeError("root not a node");return t=void 0===t?m.SHOW_ALL:+t,new f(e,t,r=void 0===r?null:r)}},_attachNodeIterator:{value:function(e){this._nodeIterators||(this._nodeIterators=[]),this._nodeIterators.push(e)}},_detachNodeIterator:{value:function(e){var t=this._nodeIterators.indexOf(e);this._nodeIterators.splice(t,1)}},_preremoveNodeIterators:{value:function(e){this._nodeIterators&&this._nodeIterators.forEach((function(t){t._preremove(e)}))}},_updateDocTypeElement:{value:function(){this.doctype=this.documentElement=null;for(var e=this.firstChild;null!==e;e=e.nextSibling)e.nodeType===n.DOCUMENT_TYPE_NODE?this.doctype=e:e.nodeType===n.ELEMENT_NODE&&(this.documentElement=e)}},insertBefore:{value:function(e,t){return n.prototype.insertBefore.call(this,e,t),this._updateDocTypeElement(),e}},replaceChild:{value:function(e,t){return n.prototype.replaceChild.call(this,e,t),this._updateDocTypeElement(),t}},removeChild:{value:function(e){return n.prototype.removeChild.call(this,e),this._updateDocTypeElement(),e}},getElementById:{value:function(e){var t=this.byId[e];return t?t instanceof O?t.getFirst():t:null}},_hasMultipleElementsWithId:{value:function(e){return this.byId[e]instanceof O}},getElementsByName:{value:o.prototype.getElementsByName},getElementsByTagName:{value:o.prototype.getElementsByTagName},getElementsByTagNameNS:{value:o.prototype.getElementsByTagNameNS},getElementsByClassName:{value:o.prototype.getElementsByClassName},adoptNode:{value:function(e){return e.nodeType===n.DOCUMENT_NODE&&E.NotSupportedError(),e.nodeType===n.ATTRIBUTE_NODE||(e.parentNode&&e.parentNode.removeChild(e),e.ownerDocument!==this&&function e(t,r){t.ownerDocument=r,t._lastModTime=void 0,Object.prototype.hasOwnProperty.call(t,"_tagName")&&(t._tagName=void 0);for(var n=t.firstChild;null!==n;n=n.nextSibling)e(n,r)}(e,this)),e}},importNode:{value:function(e,t){return this.adoptNode(e.cloneNode(t))},writable:S},origin:{get:function(){return null}},characterSet:{get:function(){return"UTF-8"}},contentType:{get:function(){return this._contentType}},URL:{get:function(){return this._address}},domain:{get:E.nyi,set:E.nyi},referrer:{get:E.nyi},cookie:{get:E.nyi,set:E.nyi},lastModified:{get:E.nyi},location:{get:function(){return this.defaultView?this.defaultView.location:null},set:E.nyi},_titleElement:{get:function(){return this.getElementsByTagName("title").item(0)||null}},title:{get:function(){var e=this._titleElement;return(e?e.textContent:"").replace(/[ \t\n\r\f]+/g," ").replace(/(^ )|( $)/g,"")},set:function(e){var t=this._titleElement,r=this.head;(t||r)&&(t||(t=this.createElement("title"),r.appendChild(t)),t.textContent=e)}},dir:A((function(){var e=this.documentElement;if(e&&"HTML"===e.tagName)return e}),"dir",""),fgColor:A((function(){return this.body}),"text",""),linkColor:A((function(){return this.body}),"link",""),vlinkColor:A((function(){return this.body}),"vLink",""),alinkColor:A((function(){return this.body}),"aLink",""),bgColor:A((function(){return this.body}),"bgColor",""),charset:{get:function(){return this.characterSet}},inputEncoding:{get:function(){return this.characterSet}},scrollingElement:{get:function(){return this._quirks?this.body:this.documentElement}},body:{get:function(){return L(this.documentElement,"body")},set:E.nyi},head:{get:function(){return L(this.documentElement,"head")}},images:{get:E.nyi},embeds:{get:E.nyi},plugins:{get:E.nyi},links:{get:E.nyi},forms:{get:E.nyi},scripts:{get:E.nyi},applets:{get:function(){return[]}},activeElement:{get:function(){return null}},innerHTML:{get:function(){return this.serialize()},set:E.nyi},outerHTML:{get:function(){return this.serialize()},set:E.nyi},write:{value:function(e){if(this.isHTML||E.InvalidStateError(),this._parser){this._parser;var t=arguments.join("");this._parser.parse(t)}}},writeln:{value:function(e){this.write(Array.prototype.join.call(arguments,"")+"\n")}},open:{value:function(){this.documentElement=null}},close:{value:function(){this.readyState="interactive",this._dispatchEvent(new l("readystatechange"),!0),this._dispatchEvent(new l("DOMContentLoaded"),!0),this.readyState="complete",this._dispatchEvent(new l("readystatechange"),!0),this.defaultView&&this.defaultView._dispatchEvent(new l("load"),!0)}},clone:{value:function(){var e=new x(this.isHTML,this._address);return e._quirks=this._quirks,e._contentType=this._contentType,e}},cloneNode:{value:function(e){var t=n.prototype.cloneNode.call(this,!1);if(e)for(var r=this.firstChild;null!==r;r=r.nextSibling)t._appendChild(t.importNode(r,!0));return t._updateDocTypeElement(),t}},isEqual:{value:function(e){return!0}},mutateValue:{value:function(e){this.mutationHandler&&this.mutationHandler({type:k.VALUE,target:e,data:e.data})}},mutateAttr:{value:function(e,t){this.mutationHandler&&this.mutationHandler({type:k.ATTR,target:e.ownerElement,attr:e})}},mutateRemoveAttr:{value:function(e){this.mutationHandler&&this.mutationHandler({type:k.REMOVE_ATTR,target:e.ownerElement,attr:e})}},mutateRemove:{value:function(e){this.mutationHandler&&this.mutationHandler({type:k.REMOVE,target:e.parentNode,node:e}),function e(t){!function(e){if(e.nodeType===n.ELEMENT_NODE){var t=e.getAttribute("id");t&&e.ownerDocument.delId(t,e)}e.ownerDocument._nodes[e._nid]=void 0,e._nid=void 0}(t);for(var r=t.firstChild;null!==r;r=r.nextSibling)e(r)}(e)}},mutateInsert:{value:function(e){!function e(t){if(function(e){if(e._nid=e.ownerDocument._nextnid++,e.ownerDocument._nodes[e._nid]=e,e.nodeType===n.ELEMENT_NODE){var t=e.getAttribute("id");t&&e.ownerDocument.addId(t,e),e._roothook&&e._roothook()}}(t),t.nodeType===n.ELEMENT_NODE)for(var r=t.firstChild;null!==r;r=r.nextSibling)e(r)}(e),this.mutationHandler&&this.mutationHandler({type:k.INSERT,target:e.parentNode,node:e})}},mutateMove:{value:function(e){this.mutationHandler&&this.mutationHandler({type:k.MOVE,target:e})}},addId:{value:function(e,t){var r=this.byId[e];r?(r instanceof O||(r=new O(r),this.byId[e]=r),r.add(t)):this.byId[e]=t}},delId:{value:function(e,t){var r=this.byId[e];E.assert(r),r instanceof O?(r.del(t),1===r.length&&(this.byId[e]=r.downgrade())):this.byId[e]=void 0}},_resolve:{value:function(e){return new g(this._documentBaseURL).resolve(e)}},_documentBaseURL:{get:function(){var e=this._address;"about:blank"===e&&(e="/");var t=this.querySelector("base[href]");return t?new g(e).resolve(t.getAttribute("href")):e}},_templateDoc:{get:function(){if(!this._templateDocCache){var e=new x(this.isHTML,this._address);this._templateDocCache=e._templateDocCache=e}return this._templateDocCache}},querySelector:{value:function(e){return b(e,this)[0]}},querySelectorAll:{value:function(e){var t=b(e,this);return t.item?t:new i(t)}}});function L(e,t){if(e&&e.isHTML)for(var r=e.firstChild;null!==r;r=r.nextSibling)if(r.nodeType===n.ELEMENT_NODE&&r.localName===t&&r.namespaceURI===T.HTML)return r;return null}function O(e){this.nodes=Object.create(null),this.nodes[e._nid]=e,this.length=1,this.firstNode=void 0}["abort","canplay","canplaythrough","change","click","contextmenu","cuechange","dblclick","drag","dragend","dragenter","dragleave","dragover","dragstart","drop","durationchange","emptied","ended","input","invalid","keydown","keypress","keyup","loadeddata","loadedmetadata","loadstart","mousedown","mousemove","mouseout","mouseover","mouseup","mousewheel","pause","play","playing","progress","ratechange","readystatechange","reset","seeked","seeking","select","show","stalled","submit","suspend","timeupdate","volumechange","waiting","blur","error","focus","load","scroll"].forEach((function(e){Object.defineProperty(x.prototype,"on"+e,{get:function(){return this._getEventHandler(e)},set:function(t){this._setEventHandler(e,t)}})})),O.prototype.add=function(e){this.nodes[e._nid]||(this.nodes[e._nid]=e,this.length++,this.firstNode=void 0)},O.prototype.del=function(e){this.nodes[e._nid]&&(delete this.nodes[e._nid],this.length--,this.firstNode=void 0)},O.prototype.getFirst=function(){var e;if(!this.firstNode)for(e in this.nodes)(void 0===this.firstNode||this.firstNode.compareDocumentPosition(this.nodes[e])&n.DOCUMENT_POSITION_PRECEDING)&&(this.firstNode=this.nodes[e]);return this.firstNode},O.prototype.downgrade=function(){var e;if(1===this.length)for(e in this.nodes)return this.nodes[e];return this}},function(e,t,r){"use strict";e.exports=o;var n=[null,"INDEX_SIZE_ERR",null,"HIERARCHY_REQUEST_ERR","WRONG_DOCUMENT_ERR","INVALID_CHARACTER_ERR",null,"NO_MODIFICATION_ALLOWED_ERR","NOT_FOUND_ERR","NOT_SUPPORTED_ERR","INUSE_ATTRIBUTE_ERR","INVALID_STATE_ERR","SYNTAX_ERR","INVALID_MODIFICATION_ERR","NAMESPACE_ERR","INVALID_ACCESS_ERR",null,"TYPE_MISMATCH_ERR","SECURITY_ERR","NETWORK_ERR","ABORT_ERR","URL_MISMATCH_ERR","QUOTA_EXCEEDED_ERR","TIMEOUT_ERR","INVALID_NODE_TYPE_ERR","DATA_CLONE_ERR"],i=[null,"INDEX_SIZE_ERR (1): the index is not in the allowed range",null,"HIERARCHY_REQUEST_ERR (3): the operation would yield an incorrect nodes model","WRONG_DOCUMENT_ERR (4): the object is in the wrong Document, a call to importNode is required","INVALID_CHARACTER_ERR (5): the string contains invalid characters",null,"NO_MODIFICATION_ALLOWED_ERR (7): the object can not be modified","NOT_FOUND_ERR (8): the object can not be found here","NOT_SUPPORTED_ERR (9): this operation is not supported","INUSE_ATTRIBUTE_ERR (10): setAttributeNode called on owned Attribute","INVALID_STATE_ERR (11): the object is in an invalid state","SYNTAX_ERR (12): the string did not match the expected pattern","INVALID_MODIFICATION_ERR (13): the object can not be modified in this way","NAMESPACE_ERR (14): the operation is not allowed by Namespaces in XML","INVALID_ACCESS_ERR (15): the object does not support the operation or argument",null,"TYPE_MISMATCH_ERR (17): the type of the object does not match the expected type","SECURITY_ERR (18): the operation is insecure","NETWORK_ERR (19): a network error occurred","ABORT_ERR (20): the user aborted an operation","URL_MISMATCH_ERR (21): the given URL does not match another URL","QUOTA_EXCEEDED_ERR (22): the quota has been exceeded","TIMEOUT_ERR (23): a timeout occurred","INVALID_NODE_TYPE_ERR (24): the supplied node is invalid or has an invalid ancestor for this operation","DATA_CLONE_ERR (25): the object can not be cloned."],a={INDEX_SIZE_ERR:1,DOMSTRING_SIZE_ERR:2,HIERARCHY_REQUEST_ERR:3,WRONG_DOCUMENT_ERR:4,INVALID_CHARACTER_ERR:5,NO_DATA_ALLOWED_ERR:6,NO_MODIFICATION_ALLOWED_ERR:7,NOT_FOUND_ERR:8,NOT_SUPPORTED_ERR:9,INUSE_ATTRIBUTE_ERR:10,INVALID_STATE_ERR:11,SYNTAX_ERR:12,INVALID_MODIFICATION_ERR:13,NAMESPACE_ERR:14,INVALID_ACCESS_ERR:15,VALIDATION_ERR:16,TYPE_MISMATCH_ERR:17,SECURITY_ERR:18,NETWORK_ERR:19,ABORT_ERR:20,URL_MISMATCH_ERR:21,QUOTA_EXCEEDED_ERR:22,TIMEOUT_ERR:23,INVALID_NODE_TYPE_ERR:24,DATA_CLONE_ERR:25};function o(e){Error.call(this),Error.captureStackTrace(this,this.constructor),this.code=e,this.message=i[e],this.name=n[e]}for(var s in o.prototype.__proto__=Error.prototype,a){var c={value:a[s]};Object.defineProperty(o,s,c),Object.defineProperty(o.prototype,s,c)}},function(e,t){t.isApiWritable=!global.__domino_frozen__},function(e,t,r){"use strict";e.exports=a;var n=r(1),i=r(2);function a(){n.call(this),this._firstChild=this._childNodes=null}a.prototype=Object.create(n.prototype,{hasChildNodes:{value:function(){return this._childNodes?this._childNodes.length>0:null!==this._firstChild}},childNodes:{get:function(){return this._ensureChildNodes(),this._childNodes}},firstChild:{get:function(){return this._childNodes?0===this._childNodes.length?null:this._childNodes[0]:this._firstChild}},lastChild:{get:function(){var e,t=this._childNodes;return t?0===t.length?null:t[t.length-1]:null===(e=this._firstChild)?null:e._previousSibling}},_ensureChildNodes:{value:function(){if(!this._childNodes){var e=this._firstChild,t=e,r=this._childNodes=new i;if(e)do{r.push(t),t=t._nextSibling}while(t!==e);this._firstChild=null}}},removeChildren:{value:function(){for(var e,t=this.rooted?this.ownerDocument:null,r=this.firstChild;null!==r;)r=(e=r).nextSibling,t&&t.mutateRemove(e),e.parentNode=null;this._childNodes?this._childNodes.length=0:this._firstChild=null,this.modify()}}})},function(e,t,r){"use strict";t.isValidName=function(e){if(n.test(e))return!0;if(u.test(e))return!0;if(!d.test(e))return!1;if(!m.test(e))return!1;var t=e.match(p),r=e.match(f);return null!==r&&2*r.length===t.length},t.isValidQName=function(e){if(i.test(e))return!0;if(h.test(e))return!0;if(!d.test(e))return!1;if(!g.test(e))return!1;var t=e.match(p),r=e.match(f);return null!==r&&2*r.length===t.length};var n=/^[_:A-Za-z][-.:\w]+$/,i=/^([_A-Za-z][-.\w]+|[_A-Za-z][-.\w]+:[_A-Za-z][-.\w]+)$/,a="_A-Za-zÀ-ÖØ-öø-˿Ͱ-ͽͿ-῿‌-‍⁰-↏Ⰰ-⿯、-퟿豈-﷏ﷰ-�",o="-._A-Za-z0-9·À-ÖØ-öø-˿̀-ͽͿ-῿‌‍‿⁀⁰-↏Ⰰ-⿯、-퟿豈-﷏ﷰ-�",s="["+a+"]["+o+"]*",c=a+":",l=o+":",u=new RegExp("^["+c+"]["+l+"]*$"),h=new RegExp("^("+s+"|"+s+":"+s+")$"),d=/[\uD800-\uDB7F\uDC00-\uDFFF]/,p=/[\uD800-\uDB7F\uDC00-\uDFFF]/g,f=/[\uD800-\uDB7F][\uDC00-\uDFFF]/g;s="["+(a+="\ud800-󯰀-\udfff")+"]["+(o+="\ud800-󯰀-\udfff")+"]*",c=a+":",l=o+":";var m=new RegExp("^["+c+"]["+l+"]*$"),g=new RegExp("^("+s+"|"+s+":"+s+")$")},function(e,t,r){"use strict";var n=Object.create(null,{location:{get:function(){throw new Error("window.location is not supported.")}}}),i=function(e,t){return 2&function(e,t){return e.compareDocumentPosition(t)}(e,t)?1:-1},a=function(e){for(;(e=e.nextSibling)&&1!==e.nodeType;);return e},o=function(e){for(;(e=e.previousSibling)&&1!==e.nodeType;);return e},s=function(e){if(e=e.firstChild)for(;1!==e.nodeType&&(e=e.nextSibling););return e},c=function(e){if(e=e.lastChild)for(;1!==e.nodeType&&(e=e.previousSibling););return e},l=function(e){if(!e.parentNode)return!1;var t=e.parentNode.nodeType;return 1===t||9===t},u=function(e){if(!e)return e;var t=e[0];return'"'===t||"'"===t?(e=e[e.length-1]===t?e.slice(1,-1):e.slice(1)).replace(y.str_escape,(function(e){var t=/^\\(?:([0-9A-Fa-f]+)|([\r\n\f]+))/.exec(e);if(!t)return e.slice(1);if(t[2])return"";var r=parseInt(t[1],16);return String.fromCodePoint?String.fromCodePoint(r):String.fromCharCode(r)})):y.ident.test(e)?h(e):e},h=function(e){return e.replace(y.escape,(function(e){var t=/^\\([0-9A-Fa-f]+)/.exec(e);if(!t)return e[1];var r=parseInt(t[1],16);return String.fromCodePoint?String.fromCodePoint(r):String.fromCharCode(r)}))},d=Array.prototype.indexOf?Array.prototype.indexOf:function(e,t){for(var r=this.length;r--;)if(this[r]===t)return r;return-1},p=function(e,t){var r=y.inside.source.replace(/</g,e).replace(/>/g,t);return new RegExp(r)},f=function(e,t,r){return e=(e=e.source).replace(t,r.source||r),new RegExp(e)},m=function(e,t){return e.replace(/^(?:\w+:\/\/|\/+)/,"").replace(/(?:\/+|\/*#.*?)$/,"").split("/",t).join("/")},g=function(e,t,r){var n=function(e,t){var r,n=e.replace(/\s+/g,"");return"even"===n?n="2n+0":"odd"===n?n="2n+1":-1===n.indexOf("n")&&(n="0n"+n),{group:"-"===(r=/^([+-])?(\d+)?n([+-])?(\d+)?$/.exec(n))[1]?-(r[2]||1):+(r[2]||1),offset:r[4]?"-"===r[3]?-r[4]:+r[4]:0}}(e),i=n.group,u=n.offset,h=r?c:s,d=r?o:a;return function(e){if(l(e))for(var r=h(e.parentNode),n=0;r;){if(t(r,e)&&n++,r===e)return n-=u,i&&n?n%i==0&&n<0==i<0:!n;r=d(r)}}},b={"*":function(){return!0},type:function(e){return e=e.toLowerCase(),function(t){return t.nodeName.toLowerCase()===e}},attr:function(e,t,r,n){return t=v[t],function(i){var a;switch(e){case"for":a=i.htmlFor;break;case"class":""===(a=i.className)&&null==i.getAttribute("class")&&(a=null);break;case"href":case"src":a=i.getAttribute(e,2);break;case"title":a=i.getAttribute("title")||null;break;case"id":case"lang":case"dir":case"accessKey":case"hidden":case"tabIndex":case"style":if(i.getAttribute){a=i.getAttribute(e);break}default:if(i.hasAttribute&&!i.hasAttribute(e))break;a=null!=i[e]?i[e]:i.getAttribute&&i.getAttribute(e)}if(null!=a)return a+="",n&&(a=a.toLowerCase(),r=r.toLowerCase()),t(a,r)}},":first-child":function(e){return!o(e)&&l(e)},":last-child":function(e){return!a(e)&&l(e)},":only-child":function(e){return!o(e)&&!a(e)&&l(e)},":nth-child":function(e,t){return g(e,(function(){return!0}),t)},":nth-last-child":function(e){return b[":nth-child"](e,!0)},":root":function(e){return e.ownerDocument.documentElement===e},":empty":function(e){return!e.firstChild},":not":function(e){var t=x(e);return function(e){return!t(e)}},":first-of-type":function(e){if(l(e)){for(var t=e.nodeName;e=o(e);)if(e.nodeName===t)return;return!0}},":last-of-type":function(e){if(l(e)){for(var t=e.nodeName;e=a(e);)if(e.nodeName===t)return;return!0}},":only-of-type":function(e){return b[":first-of-type"](e)&&b[":last-of-type"](e)},":nth-of-type":function(e,t){return g(e,(function(e,t){return e.nodeName===t.nodeName}),t)},":nth-last-of-type":function(e){return b[":nth-of-type"](e,!0)},":checked":function(e){return!(!e.checked&&!e.selected)},":indeterminate":function(e){return!b[":checked"](e)},":enabled":function(e){return!e.disabled&&"hidden"!==e.type},":disabled":function(e){return!!e.disabled},":target":function(e){return e.id===n.location.hash.substring(1)},":focus":function(e){return e===e.ownerDocument.activeElement},":is":function(e){return x(e)},":matches":function(e){return b[":is"](e)},":nth-match":function(e,t){var r=e.split(/\s*,\s*/),n=r.shift(),i=x(r.join(","));return g(n,i,t)},":nth-last-match":function(e){return b[":nth-match"](e,!0)},":links-here":function(e){return e+""==n.location+""},":lang":function(e){return function(t){for(;t;){if(t.lang)return 0===t.lang.indexOf(e);t=t.parentNode}}},":dir":function(e){return function(t){for(;t;){if(t.dir)return t.dir===e;t=t.parentNode}}},":scope":function(e,t){var r=t||e.ownerDocument;return 9===r.nodeType?e===r.documentElement:e===r},":any-link":function(e){return"string"==typeof e.href},":local-link":function(e){if(e.nodeName)return e.href&&e.host===n.location.host;var t=+e+1;return function(e){if(e.href){var r=n.location+"",i=e+"";return m(r,t)===m(i,t)}}},":default":function(e){return!!e.defaultSelected},":valid":function(e){return e.willValidate||e.validity&&e.validity.valid},":invalid":function(e){return!b[":valid"](e)},":in-range":function(e){return e.value>e.min&&e.value<=e.max},":out-of-range":function(e){return!b[":in-range"](e)},":required":function(e){return!!e.required},":optional":function(e){return!e.required},":read-only":function(e){if(e.readOnly)return!0;var t=e.getAttribute("contenteditable"),r=e.contentEditable,n=e.nodeName.toLowerCase();return((n="input"!==n&&"textarea"!==n)||e.disabled)&&null==t&&"true"!==r},":read-write":function(e){return!b[":read-only"](e)},":hover":function(){throw new Error(":hover is not supported.")},":active":function(){throw new Error(":active is not supported.")},":link":function(){throw new Error(":link is not supported.")},":visited":function(){throw new Error(":visited is not supported.")},":column":function(){throw new Error(":column is not supported.")},":nth-column":function(){throw new Error(":nth-column is not supported.")},":nth-last-column":function(){throw new Error(":nth-last-column is not supported.")},":current":function(){throw new Error(":current is not supported.")},":past":function(){throw new Error(":past is not supported.")},":future":function(){throw new Error(":future is not supported.")},":contains":function(e){return function(t){return-1!==(t.innerText||t.textContent||t.value||"").indexOf(e)}},":has":function(e){return function(t){return N(e,t).length>0}}},v={"-":function(){return!0},"=":function(e,t){return e===t},"*=":function(e,t){return-1!==e.indexOf(t)},"~=":function(e,t){var r,n,i,a;for(n=0;;n=r+1){if(-1===(r=e.indexOf(t,n)))return!1;if(i=e[r-1],a=e[r+t.length],!(i&&" "!==i||a&&" "!==a))return!0}},"|=":function(e,t){var r,n=e.indexOf(t);if(0===n)return"-"===(r=e[n+t.length])||!r},"^=":function(e,t){return 0===e.indexOf(t)},"$=":function(e,t){var r=e.lastIndexOf(t);return-1!==r&&r+t.length===e.length},"!=":function(e,t){return e!==t}},_={" ":function(e){return function(t){for(;t=t.parentNode;)if(e(t))return t}},">":function(e){return function(t){if(t=t.parentNode)return e(t)&&t}},"+":function(e){return function(t){if(t=o(t))return e(t)&&t}},"~":function(e){return function(t){for(;t=o(t);)if(e(t))return t}},noop:function(e){return function(t){return e(t)&&t}},ref:function(e,t){var r;function n(e){for(var t=e.ownerDocument.getElementsByTagName("*"),i=t.length;i--;)if(r=t[i],n.test(e))return r=null,!0;r=null}return n.combinator=function(n){if(r&&r.getAttribute){var i=r.getAttribute(t)||"";return"#"===i[0]&&(i=i.substring(1)),i===n.id&&e(r)?r:void 0}},n}},y={escape:/\\(?:[^0-9A-Fa-f\r\n]|[0-9A-Fa-f]{1,6}[\r\n\t ]?)/g,str_escape:/(escape)|\\(\n|\r\n?|\f)/g,nonascii:/[\u00A0-\uFFFF]/,cssid:/(?:(?!-?[0-9])(?:escape|nonascii|[-_a-zA-Z0-9])+)/,qname:/^ *(cssid|\*)/,simple:/^(?:([.#]cssid)|pseudo|attr)/,ref:/^ *\/(cssid)\/ */,combinator:/^(?: +([^ \w*.#\\]) +|( )+|([^ \w*.#\\]))(?! *$)/,attr:/^\[(cssid)(?:([^\w]?=)(inside))?\]/,pseudo:/^(:cssid)(?:\((inside)\))?/,inside:/(?:"(?:\\"|[^"])*"|'(?:\\'|[^'])*'|<[^"'>]*>|\\["'>]|[^"'>])*/,ident:/^(cssid)$/};y.cssid=f(y.cssid,"nonascii",y.nonascii),y.cssid=f(y.cssid,"escape",y.escape),y.qname=f(y.qname,"cssid",y.cssid),y.simple=f(y.simple,"cssid",y.cssid),y.ref=f(y.ref,"cssid",y.cssid),y.attr=f(y.attr,"cssid",y.cssid),y.pseudo=f(y.pseudo,"cssid",y.cssid),y.inside=f(y.inside,"[^\"'>]*",y.inside),y.attr=f(y.attr,"inside",p("\\[","\\]")),y.pseudo=f(y.pseudo,"inside",p("\\(","\\)")),y.simple=f(y.simple,"pseudo",y.pseudo),y.simple=f(y.simple,"attr",y.attr),y.ident=f(y.ident,"cssid",y.cssid),y.str_escape=f(y.str_escape,"escape",y.escape);var w=function(e){for(var t,r,n,i,a,o,s=e.replace(/^\s+|\s+$/g,""),c=[],l=[];s;){if(i=y.qname.exec(s))s=s.substring(i[0].length),n=h(i[1]),l.push(E(n,!0));else{if(!(i=y.simple.exec(s)))throw new SyntaxError("Invalid selector.");s=s.substring(i[0].length),n="*",l.push(E(n,!0)),l.push(E(i))}for(;i=y.simple.exec(s);)s=s.substring(i[0].length),l.push(E(i));if("!"===s[0]&&(s=s.substring(1),(r=S()).qname=n,l.push(r.simple)),i=y.ref.exec(s))s=s.substring(i[0].length),o=_.ref(k(l),h(i[1])),c.push(o.combinator),l=[];else{if(i=y.combinator.exec(s)){if(s=s.substring(i[0].length),","===(a=i[1]||i[2]||i[3])){c.push(_.noop(k(l)));break}}else a="noop";if(!_[a])throw new SyntaxError("Bad combinator.");c.push(_[a](k(l))),l=[]}}return(t=T(c)).qname=n,t.sel=s,r&&(r.lname=t.qname,r.test=t,r.qname=r.qname,r.sel=t.sel,t=r),o&&(o.test=t,o.qname=t.qname,o.sel=t.sel,t=o),t},E=function(e,t){if(t)return"*"===e?b["*"]:b.type(e);if(e[1])return"."===e[1][0]?b.attr("class","~=",h(e[1].substring(1)),!1):b.attr("id","=",h(e[1].substring(1)),!1);if(e[2])return e[3]?b[h(e[2])](u(e[3])):b[h(e[2])];if(e[4]){var r=e[6],n=/["'\s]\s*I$/i.test(r);return n&&(r=r.replace(/\s*I$/i,"")),b.attr(h(e[4]),e[5]||"-",u(r),n)}throw new SyntaxError("Unknown Selector.")},k=function(e){var t,r=e.length;return r<2?e[0]:function(n){if(n){for(t=0;t<r;t++)if(!e[t](n))return;return!0}}},T=function(e){return e.length<2?function(t){return!!e[0](t)}:function(t){for(var r=e.length;r--;)if(!(t=e[r](t)))return;return!0}},S=function(){var e;function t(r){for(var n=r.ownerDocument.getElementsByTagName(t.lname),i=n.length;i--;)if(t.test(n[i])&&e===r)return e=null,!0;e=null}return t.simple=function(t){return e=t,!0},t},x=function(e){for(var t=w(e),r=[t];t.sel;)t=w(t.sel),r.push(t);return r.length<2?t:function(e){for(var t=r.length,n=0;n<t;n++)if(r[n](e))return!0}},N=function(e,t){for(var r,n=[],a=w(e),o=t.getElementsByTagName(a.qname),s=0;r=o[s++];)a(r)&&n.push(r);if(a.sel){for(;a.sel;)for(a=w(a.sel),o=t.getElementsByTagName(a.qname),s=0;r=o[s++];)a(r)&&-1===d.call(n,r)&&n.push(r);n.sort(i)}return n};e.exports=t=function(e,t){var r,n;if(11!==t.nodeType&&-1===e.indexOf(" ")){if("#"===e[0]&&t.rooted&&/^#[A-Z_][-A-Z0-9_]*$/i.test(e)&&t.doc._hasMultipleElementsWithId&&(r=e.substring(1),!t.doc._hasMultipleElementsWithId(r)))return(n=t.doc.getElementById(r))?[n]:[];if("."===e[0]&&/^\.\w+$/.test(e))return t.getElementsByClassName(e.substring(1));if(/^\w+$/.test(e))return t.getElementsByTagName(e)}return N(e,t)},t.selectors=b,t.operators=v,t.combinators=_,t.matches=function(e,t){var r={sel:t};do{if((r=w(r.sel))(e))return!0}while(r.sel);return!1}},function(e,t,r){"use strict";var n=r(1),i=r(25),a=function(e,t){for(var r=e.createDocumentFragment(),i=0;i<t.length;i++){var a=t[i],o=a instanceof n;r.appendChild(o?a:e.createTextNode(String(a)))}return r},o={after:{value:function(){var e=Array.prototype.slice.call(arguments),t=this.parentNode,r=this.nextSibling;if(null!==t){for(;r&&e.some((function(e){return e===r}));)r=r.nextSibling;var n=a(this.doc,e);t.insertBefore(n,r)}}},before:{value:function(){var e=Array.prototype.slice.call(arguments),t=this.parentNode,r=this.previousSibling;if(null!==t){for(;r&&e.some((function(e){return e===r}));)r=r.previousSibling;var n=a(this.doc,e),i=r?r.nextSibling:t.firstChild;t.insertBefore(n,i)}}},remove:{value:function(){null!==this.parentNode&&(this.doc&&(this.doc._preremoveNodeIterators(this),this.rooted&&this.doc.mutateRemove(this)),this._remove(),this.parentNode=null)}},_remove:{value:function(){var e=this.parentNode;null!==e&&(e._childNodes?e._childNodes.splice(this.index,1):e._firstChild===this&&(this._nextSibling===this?e._firstChild=null:e._firstChild=this._nextSibling),i.remove(this),e.modify())}},replaceWith:{value:function(){var e=Array.prototype.slice.call(arguments),t=this.parentNode,r=this.nextSibling;if(null!==t){for(;r&&e.some((function(e){return e===r}));)r=r.nextSibling;var n=a(this.doc,e);this.parentNode===t?t.replaceChild(n,this):t.insertBefore(n,r)}}}};e.exports=o},function(e,t,r){"use strict";function n(e){if(!e)return Object.create(n.prototype);this.url=e.replace(/^[ \t\n\r\f]+|[ \t\n\r\f]+$/g,"");var t=n.pattern.exec(this.url);if(t){if(t[2]&&(this.scheme=t[2]),t[4]){var r=t[4].match(n.userinfoPattern);if(r&&(this.username=r[1],this.password=r[3],t[4]=t[4].substring(r[0].length)),t[4].match(n.portPattern)){var i=t[4].lastIndexOf(":");this.host=t[4].substring(0,i),this.port=t[4].substring(i+1)}else this.host=t[4]}t[5]&&(this.path=t[5]),t[6]&&(this.query=t[7]),t[8]&&(this.fragment=t[9])}}e.exports=n,n.pattern=/^(([^:\/?#]+):)?(\/\/([^\/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?$/,n.userinfoPattern=/^([^@:]*)(:([^@]*))?@/,n.portPattern=/:\d+$/,n.authorityPattern=/^[^:\/?#]+:\/\//,n.hierarchyPattern=/^[^:\/?#]+:\//,n.percentEncode=function(e){var t=e.charCodeAt(0);if(t<256)return"%"+t.toString(16);throw Error("can't percent-encode codepoints > 255 yet")},n.prototype={constructor:n,isAbsolute:function(){return!!this.scheme},isAuthorityBased:function(){return n.authorityPattern.test(this.url)},isHierarchical:function(){return n.hierarchyPattern.test(this.url)},toString:function(){var e="";return void 0!==this.scheme&&(e+=this.scheme+":"),this.isAbsolute()&&(e+="//",(this.username||this.password)&&(e+=this.username||"",this.password&&(e+=":"+this.password),e+="@"),this.host&&(e+=this.host)),void 0!==this.port&&(e+=":"+this.port),void 0!==this.path&&(e+=this.path),void 0!==this.query&&(e+="?"+this.query),void 0!==this.fragment&&(e+="#"+this.fragment),e},resolve:function(e){var t=this,r=new n(e),i=new n;return void 0!==r.scheme?(i.scheme=r.scheme,i.username=r.username,i.password=r.password,i.host=r.host,i.port=r.port,i.path=a(r.path),i.query=r.query):(i.scheme=t.scheme,void 0!==r.host?(i.username=r.username,i.password=r.password,i.host=r.host,i.port=r.port,i.path=a(r.path),i.query=r.query):(i.username=t.username,i.password=t.password,i.host=t.host,i.port=t.port,r.path?("/"===r.path.charAt(0)?i.path=a(r.path):(i.path=function(e,r){if(void 0!==t.host&&!t.path)return"/"+r;var n=e.lastIndexOf("/");return-1===n?r:e.substring(0,n+1)+r}(t.path,r.path),i.path=a(i.path)),i.query=r.query):(i.path=t.path,void 0!==r.query?i.query=r.query:i.query=t.query))),i.fragment=r.fragment,i.toString();function a(e){if(!e)return e;for(var t="";e.length>0;){if("."===e||".."===e){e="";break}var r=e.substring(0,2),n=e.substring(0,3),i=e.substring(0,4);if("../"===n)e=e.substring(3);else if("./"===r)e=e.substring(2);else if("/./"===n)e="/"+e.substring(3);else if("/."===r&&2===e.length)e="/";else if("/../"===i||"/.."===n&&3===e.length)e="/"+e.substring(4),t=t.replace(/\/?[^\/]*$/,"");else{var a=e.match(/(\/?([^\/]*))/)[0];t+=a,e=e.substring(a.length)}}return t}}}},function(e,t,r){"use strict";var n=r(1),i=r(4),a=r(18),o=r(0),s=r(38),c=r(39),l=t.elements={},u=Object.create(null);function h(e){return c(e,b,l,u)}function d(e){return{get:function(){var t=this._getattr(e);if(null===t)return"";var r=this.doc._resolve(t);return null===r?t:r},set:function(t){this._setattr(e,t)}}}function p(e){return{get:function(){var t=this._getattr(e);return null===t?null:"use-credentials"===t.toLowerCase()?"use-credentials":"anonymous"},set:function(t){null==t?this.removeAttribute(e):this._setattr(e,t)}}}t.createElement=function(e,t,r){return new(u[t]||v)(e,t,r)};var f={type:["","no-referrer","no-referrer-when-downgrade","same-origin","origin","strict-origin","origin-when-cross-origin","strict-origin-when-cross-origin","unsafe-url"],missing:""},m={A:!0,LINK:!0,BUTTON:!0,INPUT:!0,SELECT:!0,TEXTAREA:!0,COMMAND:!0},g=function(e,t,r){b.call(this,e,t,r),this._form=null},b=t.HTMLElement=h({superclass:i,ctor:function(e,t,r){i.call(this,e,t,o.NAMESPACE.HTML,r)},props:{innerHTML:{get:function(){return this.serialize()},set:function(e){var t=this.ownerDocument.implementation.mozHTMLParser(this.ownerDocument._address,this);t.parse(null===e?"":String(e),!0);for(var r=this instanceof u.template?this.content:this;r.hasChildNodes();)r.removeChild(r.firstChild);r.appendChild(t._asDocumentFragment())}},style:{get:function(){return this._style||(this._style=new a(this)),this._style},set:function(e){null==e&&(e=""),this._setattr("style",String(e))}},blur:{value:function(){}},focus:{value:function(){}},forceSpellCheck:{value:function(){}},click:{value:function(){if(!this._click_in_progress){this._click_in_progress=!0;try{this._pre_click_activation_steps&&this._pre_click_activation_steps();var e=this.ownerDocument.createEvent("MouseEvent");e.initMouseEvent("click",!0,!0,this.ownerDocument.defaultView,1,0,0,0,0,!1,!1,!1,!1,0,null),this.dispatchEvent(e)?this._post_click_activation_steps&&this._post_click_activation_steps(e):this._cancelled_activation_steps&&this._cancelled_activation_steps()}finally{this._click_in_progress=!1}}}},submit:{value:o.nyi}},attributes:{title:String,lang:String,dir:{type:["ltr","rtl","auto"],missing:""},accessKey:String,hidden:Boolean,tabIndex:{type:"long",default:function(){return this.tagName in m||this.contentEditable?0:-1}}},events:["abort","canplay","canplaythrough","change","click","contextmenu","cuechange","dblclick","drag","dragend","dragenter","dragleave","dragover","dragstart","drop","durationchange","emptied","ended","input","invalid","keydown","keypress","keyup","loadeddata","loadedmetadata","loadstart","mousedown","mousemove","mouseout","mouseover","mouseup","mousewheel","pause","play","playing","progress","ratechange","readystatechange","reset","seeked","seeking","select","show","stalled","submit","suspend","timeupdate","volumechange","waiting","blur","error","focus","load","scroll"]}),v=h({ctor:function(e,t,r){b.call(this,e,t,r)}}),_={form:{get:function(){return this._form}}};h({tag:"a",ctor:function(e,t,r){b.call(this,e,t,r)},props:{_post_click_activation_steps:{value:function(e){this.href&&(this.ownerDocument.defaultView.location=this.href)}}},attributes:{href:d,ping:String,download:String,target:String,rel:String,media:String,hreflang:String,type:String,referrerPolicy:f,coords:String,charset:String,name:String,rev:String,shape:String}}),s._inherit(u.a.prototype),h({tag:"area",ctor:function(e,t,r){b.call(this,e,t,r)},attributes:{alt:String,target:String,download:String,rel:String,media:String,href:d,hreflang:String,type:String,shape:String,coords:String,ping:String,referrerPolicy:f,noHref:Boolean}}),s._inherit(u.area.prototype),h({tag:"br",ctor:function(e,t,r){b.call(this,e,t,r)},attributes:{clear:String}}),h({tag:"base",ctor:function(e,t,r){b.call(this,e,t,r)},attributes:{target:String}}),h({tag:"body",ctor:function(e,t,r){b.call(this,e,t,r)},events:["afterprint","beforeprint","beforeunload","blur","error","focus","hashchange","load","message","offline","online","pagehide","pageshow","popstate","resize","scroll","storage","unload"],attributes:{text:{type:String,treatNullAsEmptyString:!0},link:{type:String,treatNullAsEmptyString:!0},vLink:{type:String,treatNullAsEmptyString:!0},aLink:{type:String,treatNullAsEmptyString:!0},bgColor:{type:String,treatNullAsEmptyString:!0},background:String}}),h({tag:"button",ctor:function(e,t,r){g.call(this,e,t,r)},props:_,attributes:{name:String,value:String,disabled:Boolean,autofocus:Boolean,type:{type:["submit","reset","button","menu"],missing:"submit"},formTarget:String,formNoValidate:Boolean,formMethod:{type:["get","post","dialog"],invalid:"get",missing:""},formEnctype:{type:["application/x-www-form-urlencoded","multipart/form-data","text/plain"],invalid:"application/x-www-form-urlencoded",missing:""}}}),h({tag:"dl",ctor:function(e,t,r){b.call(this,e,t,r)},attributes:{compact:Boolean}}),h({tag:"data",ctor:function(e,t,r){b.call(this,e,t,r)},attributes:{value:String}}),h({tag:"datalist",ctor:function(e,t,r){b.call(this,e,t,r)}}),h({tag:"details",ctor:function(e,t,r){b.call(this,e,t,r)},attributes:{open:Boolean}}),h({tag:"div",ctor:function(e,t,r){b.call(this,e,t,r)},attributes:{align:String}}),h({tag:"embed",ctor:function(e,t,r){b.call(this,e,t,r)},attributes:{src:d,type:String,width:String,height:String,align:String,name:String}}),h({tag:"fieldset",ctor:function(e,t,r){g.call(this,e,t,r)},props:_,attributes:{disabled:Boolean,name:String}}),h({tag:"form",ctor:function(e,t,r){b.call(this,e,t,r)},attributes:{action:String,autocomplete:{type:["on","off"],missing:"on"},name:String,acceptCharset:{name:"accept-charset"},target:String,noValidate:Boolean,method:{type:["get","post","dialog"],invalid:"get",missing:"get"},enctype:{type:["application/x-www-form-urlencoded","multipart/form-data","text/plain"],invalid:"application/x-www-form-urlencoded",missing:"application/x-www-form-urlencoded"},encoding:{name:"enctype",type:["application/x-www-form-urlencoded","multipart/form-data","text/plain"],invalid:"application/x-www-form-urlencoded",missing:"application/x-www-form-urlencoded"}}}),h({tag:"hr",ctor:function(e,t,r){b.call(this,e,t,r)},attributes:{align:String,color:String,noShade:Boolean,size:String,width:String}}),h({tag:"head",ctor:function(e,t,r){b.call(this,e,t,r)}}),h({tags:["h1","h2","h3","h4","h5","h6"],ctor:function(e,t,r){b.call(this,e,t,r)},attributes:{align:String}}),h({tag:"html",ctor:function(e,t,r){b.call(this,e,t,r)},attributes:{version:String}}),h({tag:"iframe",ctor:function(e,t,n){b.call(this,e,t,n);var i=r(19);this._contentWindow=new i},props:{contentWindow:{get:function(){return this._contentWindow}},contentDocument:{get:function(){return this.contentWindow.document}}},attributes:{src:d,srcdoc:String,name:String,width:String,height:String,seamless:Boolean,allowFullscreen:Boolean,allowUserMedia:Boolean,allowPaymentRequest:Boolean,referrerPolicy:f,align:String,scrolling:String,frameBorder:String,longDesc:d,marginHeight:{type:String,treatNullAsEmptyString:!0},marginWidth:{type:String,treatNullAsEmptyString:!0}}}),h({tag:"img",ctor:function(e,t,r){b.call(this,e,t,r)},attributes:{alt:String,src:d,srcset:String,crossOrigin:p,useMap:String,isMap:Boolean,height:{type:"unsigned long",default:0},width:{type:"unsigned long",default:0},referrerPolicy:f,name:String,lowsrc:d,align:String,hspace:{type:"unsigned long",default:0},vspace:{type:"unsigned long",default:0},longDesc:d,border:{type:String,treatNullAsEmptyString:!0}}}),h({tag:"input",ctor:function(e,t,r){g.call(this,e,t,r)},props:{form:_.form,_post_click_activation_steps:{value:function(e){if("checkbox"===this.type)this.checked=!this.checked;else if("radio"===this.type)for(var t=this.form.getElementsByName(this.name),r=t.length-1;r>=0;r--){var n=t[r];n.checked=n===this}}}},attributes:{name:String,disabled:Boolean,autofocus:Boolean,accept:String,alt:String,max:String,min:String,pattern:String,placeholder:String,step:String,dirName:String,defaultValue:{name:"value"},multiple:Boolean,required:Boolean,readOnly:Boolean,checked:Boolean,value:String,src:d,defaultChecked:{name:"checked",type:Boolean},size:{type:"unsigned long",default:20,min:1,setmin:1},width:{type:"unsigned long",min:0,setmin:0,default:0},height:{type:"unsigned long",min:0,setmin:0,default:0},minLength:{type:"unsigned long",min:0,setmin:0,default:-1},maxLength:{type:"unsigned long",min:0,setmin:0,default:-1},autocomplete:String,type:{type:["text","hidden","search","tel","url","email","password","datetime","date","month","week","time","datetime-local","number","range","color","checkbox","radio","file","submit","image","reset","button"],missing:"text"},formTarget:String,formNoValidate:Boolean,formMethod:{type:["get","post"],invalid:"get",missing:""},formEnctype:{type:["application/x-www-form-urlencoded","multipart/form-data","text/plain"],invalid:"application/x-www-form-urlencoded",missing:""},inputMode:{type:["verbatim","latin","latin-name","latin-prose","full-width-latin","kana","kana-name","katakana","numeric","tel","email","url"],missing:""},align:String,useMap:String}}),h({tag:"keygen",ctor:function(e,t,r){g.call(this,e,t,r)},props:_,attributes:{name:String,disabled:Boolean,autofocus:Boolean,challenge:String,keytype:{type:["rsa"],missing:""}}}),h({tag:"li",ctor:function(e,t,r){b.call(this,e,t,r)},attributes:{value:{type:"long",default:0},type:String}}),h({tag:"label",ctor:function(e,t,r){g.call(this,e,t,r)},props:_,attributes:{htmlFor:{name:"for",type:String}}}),h({tag:"legend",ctor:function(e,t,r){b.call(this,e,t,r)},attributes:{align:String}}),h({tag:"link",ctor:function(e,t,r){b.call(this,e,t,r)},attributes:{href:d,rel:String,media:String,hreflang:String,type:String,crossOrigin:p,nonce:String,integrity:String,referrerPolicy:f,charset:String,rev:String,target:String}}),h({tag:"map",ctor:function(e,t,r){b.call(this,e,t,r)},attributes:{name:String}}),h({tag:"menu",ctor:function(e,t,r){b.call(this,e,t,r)},attributes:{type:{type:["context","popup","toolbar"],missing:"toolbar"},label:String,compact:Boolean}}),h({tag:"meta",ctor:function(e,t,r){b.call(this,e,t,r)},attributes:{name:String,content:String,httpEquiv:{name:"http-equiv",type:String},scheme:String}}),h({tag:"meter",ctor:function(e,t,r){g.call(this,e,t,r)},props:_}),h({tags:["ins","del"],ctor:function(e,t,r){b.call(this,e,t,r)},attributes:{cite:d,dateTime:String}}),h({tag:"ol",ctor:function(e,t,r){b.call(this,e,t,r)},props:{_numitems:{get:function(){var e=0;return this.childNodes.forEach((function(t){t.nodeType===n.ELEMENT_NODE&&"LI"===t.tagName&&e++})),e}}},attributes:{type:String,reversed:Boolean,start:{type:"long",default:function(){return this.reversed?this._numitems:1}},compact:Boolean}}),h({tag:"object",ctor:function(e,t,r){g.call(this,e,t,r)},props:_,attributes:{data:d,type:String,name:String,useMap:String,typeMustMatch:Boolean,width:String,height:String,align:String,archive:String,code:String,declare:Boolean,hspace:{type:"unsigned long",default:0},standby:String,vspace:{type:"unsigned long",default:0},codeBase:d,codeType:String,border:{type:String,treatNullAsEmptyString:!0}}}),h({tag:"optgroup",ctor:function(e,t,r){b.call(this,e,t,r)},attributes:{disabled:Boolean,label:String}}),h({tag:"option",ctor:function(e,t,r){b.call(this,e,t,r)},props:{form:{get:function(){for(var e=this.parentNode;e&&e.nodeType===n.ELEMENT_NODE;){if("select"===e.localName)return e.form;e=e.parentNode}}},value:{get:function(){return this._getattr("value")||this.text},set:function(e){this._setattr("value",e)}},text:{get:function(){return this.textContent.replace(/[ \t\n\f\r]+/g," ").trim()},set:function(e){this.textContent=e}}},attributes:{disabled:Boolean,defaultSelected:{name:"selected",type:Boolean},label:String}}),h({tag:"output",ctor:function(e,t,r){g.call(this,e,t,r)},props:_,attributes:{name:String}}),h({tag:"p",ctor:function(e,t,r){b.call(this,e,t,r)},attributes:{align:String}}),h({tag:"param",ctor:function(e,t,r){b.call(this,e,t,r)},attributes:{name:String,value:String,type:String,valueType:String}}),h({tags:["pre","listing","xmp"],ctor:function(e,t,r){b.call(this,e,t,r)},attributes:{width:{type:"long",default:0}}}),h({tag:"progress",ctor:function(e,t,r){g.call(this,e,t,r)},props:_,attributes:{max:{type:Number,float:!0,default:1,min:0}}}),h({tags:["q","blockquote"],ctor:function(e,t,r){b.call(this,e,t,r)},attributes:{cite:d}}),h({tag:"script",ctor:function(e,t,r){b.call(this,e,t,r)},props:{text:{get:function(){for(var e="",t=0,r=this.childNodes.length;t<r;t++){var i=this.childNodes[t];i.nodeType===n.TEXT_NODE&&(e+=i._data)}return e},set:function(e){this.removeChildren(),null!==e&&""!==e&&this.appendChild(this.ownerDocument.createTextNode(e))}}},attributes:{src:d,type:String,charset:String,defer:Boolean,async:Boolean,crossOrigin:p,nonce:String,integrity:String}}),h({tag:"select",ctor:function(e,t,r){g.call(this,e,t,r)},props:{form:_.form,options:{get:function(){return this.getElementsByTagName("option")}}},attributes:{autocomplete:String,name:String,disabled:Boolean,autofocus:Boolean,multiple:Boolean,required:Boolean,size:{type:"unsigned long",default:0}}}),h({tag:"source",ctor:function(e,t,r){b.call(this,e,t,r)},attributes:{src:d,type:String,media:String}}),h({tag:"span",ctor:function(e,t,r){b.call(this,e,t,r)}}),h({tag:"style",ctor:function(e,t,r){b.call(this,e,t,r)},attributes:{media:String,type:String,scoped:Boolean}}),h({tag:"caption",ctor:function(e,t,r){b.call(this,e,t,r)},attributes:{align:String}}),h({ctor:function(e,t,r){b.call(this,e,t,r)},attributes:{colSpan:{type:"unsigned long",default:1},rowSpan:{type:"unsigned long",default:1},scope:{type:["row","col","rowgroup","colgroup"],missing:""},abbr:String,align:String,axis:String,height:String,width:String,ch:{name:"char",type:String},chOff:{name:"charoff",type:String},noWrap:Boolean,vAlign:String,bgColor:{type:String,treatNullAsEmptyString:!0}}}),h({tags:["col","colgroup"],ctor:function(e,t,r){b.call(this,e,t,r)},attributes:{span:{type:"limited unsigned long with fallback",default:1,min:1},align:String,ch:{name:"char",type:String},chOff:{name:"charoff",type:String},vAlign:String,width:String}}),h({tag:"table",ctor:function(e,t,r){b.call(this,e,t,r)},props:{rows:{get:function(){return this.getElementsByTagName("tr")}}},attributes:{align:String,border:String,frame:String,rules:String,summary:String,width:String,bgColor:{type:String,treatNullAsEmptyString:!0},cellPadding:{type:String,treatNullAsEmptyString:!0},cellSpacing:{type:String,treatNullAsEmptyString:!0}}}),h({tag:"template",ctor:function(e,t,r){b.call(this,e,t,r),this._contentFragment=e._templateDoc.createDocumentFragment()},props:{content:{get:function(){return this._contentFragment}},serialize:{value:function(){return this.content.serialize()}}}}),h({tag:"tr",ctor:function(e,t,r){b.call(this,e,t,r)},props:{cells:{get:function(){return this.querySelectorAll("td,th")}}},attributes:{align:String,ch:{name:"char",type:String},chOff:{name:"charoff",type:String},vAlign:String,bgColor:{type:String,treatNullAsEmptyString:!0}}}),h({tags:["thead","tfoot","tbody"],ctor:function(e,t,r){b.call(this,e,t,r)},props:{rows:{get:function(){return this.getElementsByTagName("tr")}}},attributes:{align:String,ch:{name:"char",type:String},chOff:{name:"charoff",type:String},vAlign:String}}),h({tag:"textarea",ctor:function(e,t,r){g.call(this,e,t,r)},props:{form:_.form,type:{get:function(){return"textarea"}},defaultValue:{get:function(){return this.textContent},set:function(e){this.textContent=e}},value:{get:function(){return this.defaultValue},set:function(e){this.defaultValue=e}},textLength:{get:function(){return this.value.length}}},attributes:{autocomplete:String,name:String,disabled:Boolean,autofocus:Boolean,placeholder:String,wrap:String,dirName:String,required:Boolean,readOnly:Boolean,rows:{type:"limited unsigned long with fallback",default:2},cols:{type:"limited unsigned long with fallback",default:20},maxLength:{type:"unsigned long",min:0,setmin:0,default:-1},minLength:{type:"unsigned long",min:0,setmin:0,default:-1},inputMode:{type:["verbatim","latin","latin-name","latin-prose","full-width-latin","kana","kana-name","katakana","numeric","tel","email","url"],missing:""}}}),h({tag:"time",ctor:function(e,t,r){b.call(this,e,t,r)},attributes:{dateTime:String,pubDate:Boolean}}),h({tag:"title",ctor:function(e,t,r){b.call(this,e,t,r)},props:{text:{get:function(){return this.textContent}}}}),h({tag:"ul",ctor:function(e,t,r){b.call(this,e,t,r)},attributes:{type:String,compact:Boolean}}),h({ctor:function(e,t,r){b.call(this,e,t,r)},attributes:{src:d,crossOrigin:p,preload:{type:["metadata","none","auto",{value:"",alias:"auto"}],missing:"auto"},loop:Boolean,autoplay:Boolean,mediaGroup:String,controls:Boolean,defaultMuted:{name:"muted",type:Boolean}}}),h({tag:"audio",superclass:l.HTMLMediaElement,ctor:function(e,t,r){l.HTMLMediaElement.call(this,e,t,r)}}),h({tag:"video",superclass:l.HTMLMediaElement,ctor:function(e,t,r){l.HTMLMediaElement.call(this,e,t,r)},attributes:{poster:d,width:{type:"unsigned long",min:0,default:0},height:{type:"unsigned long",min:0,default:0}}}),h({tag:"td",superclass:l.HTMLTableCellElement,ctor:function(e,t,r){l.HTMLTableCellElement.call(this,e,t,r)}}),h({tag:"th",superclass:l.HTMLTableCellElement,ctor:function(e,t,r){l.HTMLTableCellElement.call(this,e,t,r)}}),h({tag:"frameset",ctor:function(e,t,r){b.call(this,e,t,r)}}),h({tag:"frame",ctor:function(e,t,r){b.call(this,e,t,r)}}),h({tag:"canvas",ctor:function(e,t,r){b.call(this,e,t,r)},props:{getContext:{value:o.nyi},probablySupportsContext:{value:o.nyi},setContext:{value:o.nyi},transferControlToProxy:{value:o.nyi},toDataURL:{value:o.nyi},toBlob:{value:o.nyi}},attributes:{width:{type:"unsigned long",default:300},height:{type:"unsigned long",default:150}}}),h({tag:"dialog",ctor:function(e,t,r){b.call(this,e,t,r)},props:{show:{value:o.nyi},showModal:{value:o.nyi},close:{value:o.nyi}},attributes:{open:Boolean,returnValue:String}}),h({tag:"menuitem",ctor:function(e,t,r){b.call(this,e,t,r)},props:{_label:{get:function(){var e=this._getattr("label");return null!==e&&""!==e?e:(e=this.textContent).replace(/[ \t\n\f\r]+/g," ").trim()}},label:{get:function(){var e=this._getattr("label");return null!==e?e:this._label},set:function(e){this._setattr("label",e)}}},attributes:{type:{type:["command","checkbox","radio"],missing:"command"},icon:d,disabled:Boolean,checked:Boolean,radiogroup:String,default:Boolean}}),h({tag:"source",ctor:function(e,t,r){b.call(this,e,t,r)},attributes:{srcset:String,sizes:String,media:String,src:d,type:String}}),h({tag:"track",ctor:function(e,t,r){b.call(this,e,t,r)},attributes:{src:d,srclang:String,label:String,default:Boolean,kind:{type:["subtitles","captions","descriptions","chapters","metadata"],missing:"subtitles",invalid:"metadata"}},props:{NONE:{get:function(){return 0}},LOADING:{get:function(){return 1}},LOADED:{get:function(){return 2}},ERROR:{get:function(){return 3}},readyState:{get:o.nyi},track:{get:o.nyi}}}),h({tag:"font",ctor:function(e,t,r){b.call(this,e,t,r)},attributes:{color:{type:String,treatNullAsEmptyString:!0},face:{type:String},size:{type:String}}}),h({tag:"dir",ctor:function(e,t,r){b.call(this,e,t,r)},attributes:{compact:Boolean}}),h({tags:["abbr","address","article","aside","b","bdi","bdo","cite","code","dd","dfn","dt","em","figcaption","figure","footer","header","hgroup","i","kbd","main","mark","nav","noscript","rb","rp","rt","rtc","ruby","s","samp","section","small","strong","sub","summary","sup","u","var","wbr","acronym","basefont","big","center","nobr","noembed","noframes","plaintext","strike","tt"]})},function(e,t,r){"use strict";var n=r(58);function i(e){this._element=e}function a(e){var t=new n.css.Parser,r={property:Object.create(null),priority:Object.create(null)};return t.addListener("property",(function(e){e.invalid||(r.property[e.property.text]=e.value.text,e.important&&(r.priority[e.property.text]="important"))})),e=(""+e).replace(/^;/,""),t.parseStyleAttribute(e),r}e.exports=i;var o={};i.prototype=Object.create(Object.prototype,{_parsed:{get:function(){if(!this._parsedStyles||this.cssText!==this._lastParsedText){var e=this.cssText;this._parsedStyles=a(e),this._lastParsedText=e,delete this._names}return this._parsedStyles}},_serialize:{value:function(){var e=this._parsed,t="";for(var r in e.property)t&&(t+=" "),t+=r+": "+e.property[r],e.priority[r]&&(t+=" !"+e.priority[r]),t+=";";this.cssText=t,this._lastParsedText=t,delete this._names}},cssText:{get:function(){return this._element.getAttribute("style")},set:function(e){this._element.setAttribute("style",e)}},length:{get:function(){return this._names||(this._names=Object.getOwnPropertyNames(this._parsed.property)),this._names.length}},item:{value:function(e){return this._names||(this._names=Object.getOwnPropertyNames(this._parsed.property)),this._names[e]}},getPropertyValue:{value:function(e){return e=e.toLowerCase(),this._parsed.property[e]||""}},getPropertyPriority:{value:function(e){return e=e.toLowerCase(),this._parsed.priority[e]||""}},setProperty:{value:function(e,t,r){if(e=e.toLowerCase(),null==t&&(t=""),null==r&&(r=""),t!==o&&(t=""+t),""!==t){if(""===r||r===o||/^important$/i.test(r)){var n=this._parsed;if(t===o){if(!n.property[e])return;""!==r?n.priority[e]="important":delete n.priority[e]}else{if(-1!==t.indexOf(";"))return;var i=a(e+":"+t);if(0===Object.getOwnPropertyNames(i.property).length)return;if(0!==Object.getOwnPropertyNames(i.priority).length)return;for(var s in i.property)n.property[s]=i.property[s],r!==o&&(""!==r?n.priority[s]="important":n.priority[s]&&delete n.priority[s])}this._serialize()}}else this.removeProperty(e)}},setPropertyValue:{value:function(e,t){return this.setProperty(e,t,o)}},setPropertyPriority:{value:function(e,t){return this.setProperty(e,o,t)}},removeProperty:{value:function(e){e=e.toLowerCase();var t=this._parsed;e in t.property&&(delete t.property[e],delete t.priority[e],this._serialize())}}});var s={alignContent:"align-content",alignItems:"align-items",alignmentBaseline:"alignment-baseline",alignSelf:"align-self",animation:"animation",animationDelay:"animation-delay",animationDirection:"animation-direction",animationDuration:"animation-duration",animationFillMode:"animation-fill-mode",animationIterationCount:"animation-iteration-count",animationName:"animation-name",animationPlayState:"animation-play-state",animationTimingFunction:"animation-timing-function",backfaceVisibility:"backface-visibility",background:"background",backgroundAttachment:"background-attachment",backgroundClip:"background-clip",backgroundColor:"background-color",backgroundImage:"background-image",backgroundOrigin:"background-origin",backgroundPosition:"background-position",backgroundPositionX:"background-position-x",backgroundPositionY:"background-position-y",backgroundRepeat:"background-repeat",backgroundSize:"background-size",baselineShift:"baseline-shift",border:"border",borderBottom:"border-bottom",borderBottomColor:"border-bottom-color",borderBottomLeftRadius:"border-bottom-left-radius",borderBottomRightRadius:"border-bottom-right-radius",borderBottomStyle:"border-bottom-style",borderBottomWidth:"border-bottom-width",borderCollapse:"border-collapse",borderColor:"border-color",borderImage:"border-image",borderImageOutset:"border-image-outset",borderImageRepeat:"border-image-repeat",borderImageSlice:"border-image-slice",borderImageSource:"border-image-source",borderImageWidth:"border-image-width",borderLeft:"border-left",borderLeftColor:"border-left-color",borderLeftStyle:"border-left-style",borderLeftWidth:"border-left-width",borderRadius:"border-radius",borderRight:"border-right",borderRightColor:"border-right-color",borderRightStyle:"border-right-style",borderRightWidth:"border-right-width",borderSpacing:"border-spacing",borderStyle:"border-style",borderTop:"border-top",borderTopColor:"border-top-color",borderTopLeftRadius:"border-top-left-radius",borderTopRightRadius:"border-top-right-radius",borderTopStyle:"border-top-style",borderTopWidth:"border-top-width",borderWidth:"border-width",bottom:"bottom",boxShadow:"box-shadow",boxSizing:"box-sizing",breakAfter:"break-after",breakBefore:"break-before",breakInside:"break-inside",captionSide:"caption-side",clear:"clear",clip:"clip",clipPath:"clip-path",clipRule:"clip-rule",color:"color",colorInterpolationFilters:"color-interpolation-filters",columnCount:"column-count",columnFill:"column-fill",columnGap:"column-gap",columnRule:"column-rule",columnRuleColor:"column-rule-color",columnRuleStyle:"column-rule-style",columnRuleWidth:"column-rule-width",columns:"columns",columnSpan:"column-span",columnWidth:"column-width",content:"content",counterIncrement:"counter-increment",counterReset:"counter-reset",cssFloat:"float",cursor:"cursor",direction:"direction",display:"display",dominantBaseline:"dominant-baseline",emptyCells:"empty-cells",enableBackground:"enable-background",fill:"fill",fillOpacity:"fill-opacity",fillRule:"fill-rule",filter:"filter",flex:"flex",flexBasis:"flex-basis",flexDirection:"flex-direction",flexFlow:"flex-flow",flexGrow:"flex-grow",flexShrink:"flex-shrink",flexWrap:"flex-wrap",floodColor:"flood-color",floodOpacity:"flood-opacity",font:"font",fontFamily:"font-family",fontFeatureSettings:"font-feature-settings",fontSize:"font-size",fontSizeAdjust:"font-size-adjust",fontStretch:"font-stretch",fontStyle:"font-style",fontVariant:"font-variant",fontWeight:"font-weight",glyphOrientationHorizontal:"glyph-orientation-horizontal",glyphOrientationVertical:"glyph-orientation-vertical",grid:"grid",gridArea:"grid-area",gridAutoColumns:"grid-auto-columns",gridAutoFlow:"grid-auto-flow",gridAutoRows:"grid-auto-rows",gridColumn:"grid-column",gridColumnEnd:"grid-column-end",gridColumnGap:"grid-column-gap",gridColumnStart:"grid-column-start",gridGap:"grid-gap",gridRow:"grid-row",gridRowEnd:"grid-row-end",gridRowGap:"grid-row-gap",gridRowStart:"grid-row-start",gridTemplate:"grid-template",gridTemplateAreas:"grid-template-areas",gridTemplateColumns:"grid-template-columns",gridTemplateRows:"grid-template-rows",height:"height",imeMode:"ime-mode",justifyContent:"justify-content",kerning:"kerning",layoutGrid:"layout-grid",layoutGridChar:"layout-grid-char",layoutGridLine:"layout-grid-line",layoutGridMode:"layout-grid-mode",layoutGridType:"layout-grid-type",left:"left",letterSpacing:"letter-spacing",lightingColor:"lighting-color",lineBreak:"line-break",lineHeight:"line-height",listStyle:"list-style",listStyleImage:"list-style-image",listStylePosition:"list-style-position",listStyleType:"list-style-type",margin:"margin",marginBottom:"margin-bottom",marginLeft:"margin-left",marginRight:"margin-right",marginTop:"margin-top",marker:"marker",markerEnd:"marker-end",markerMid:"marker-mid",markerStart:"marker-start",mask:"mask",maxHeight:"max-height",maxWidth:"max-width",minHeight:"min-height",minWidth:"min-width",msContentZoomChaining:"-ms-content-zoom-chaining",msContentZooming:"-ms-content-zooming",msContentZoomLimit:"-ms-content-zoom-limit",msContentZoomLimitMax:"-ms-content-zoom-limit-max",msContentZoomLimitMin:"-ms-content-zoom-limit-min",msContentZoomSnap:"-ms-content-zoom-snap",msContentZoomSnapPoints:"-ms-content-zoom-snap-points",msContentZoomSnapType:"-ms-content-zoom-snap-type",msFlowFrom:"-ms-flow-from",msFlowInto:"-ms-flow-into",msFontFeatureSettings:"-ms-font-feature-settings",msGridColumn:"-ms-grid-column",msGridColumnAlign:"-ms-grid-column-align",msGridColumns:"-ms-grid-columns",msGridColumnSpan:"-ms-grid-column-span",msGridRow:"-ms-grid-row",msGridRowAlign:"-ms-grid-row-align",msGridRows:"-ms-grid-rows",msGridRowSpan:"-ms-grid-row-span",msHighContrastAdjust:"-ms-high-contrast-adjust",msHyphenateLimitChars:"-ms-hyphenate-limit-chars",msHyphenateLimitLines:"-ms-hyphenate-limit-lines",msHyphenateLimitZone:"-ms-hyphenate-limit-zone",msHyphens:"-ms-hyphens",msImeAlign:"-ms-ime-align",msOverflowStyle:"-ms-overflow-style",msScrollChaining:"-ms-scroll-chaining",msScrollLimit:"-ms-scroll-limit",msScrollLimitXMax:"-ms-scroll-limit-x-max",msScrollLimitXMin:"-ms-scroll-limit-x-min",msScrollLimitYMax:"-ms-scroll-limit-y-max",msScrollLimitYMin:"-ms-scroll-limit-y-min",msScrollRails:"-ms-scroll-rails",msScrollSnapPointsX:"-ms-scroll-snap-points-x",msScrollSnapPointsY:"-ms-scroll-snap-points-y",msScrollSnapType:"-ms-scroll-snap-type",msScrollSnapX:"-ms-scroll-snap-x",msScrollSnapY:"-ms-scroll-snap-y",msScrollTranslation:"-ms-scroll-translation",msTextCombineHorizontal:"-ms-text-combine-horizontal",msTextSizeAdjust:"-ms-text-size-adjust",msTouchAction:"-ms-touch-action",msTouchSelect:"-ms-touch-select",msUserSelect:"-ms-user-select",msWrapFlow:"-ms-wrap-flow",msWrapMargin:"-ms-wrap-margin",msWrapThrough:"-ms-wrap-through",opacity:"opacity",order:"order",orphans:"orphans",outline:"outline",outlineColor:"outline-color",outlineOffset:"outline-offset",outlineStyle:"outline-style",outlineWidth:"outline-width",overflow:"overflow",overflowX:"overflow-x",overflowY:"overflow-y",padding:"padding",paddingBottom:"padding-bottom",paddingLeft:"padding-left",paddingRight:"padding-right",paddingTop:"padding-top",page:"page",pageBreakAfter:"page-break-after",pageBreakBefore:"page-break-before",pageBreakInside:"page-break-inside",perspective:"perspective",perspectiveOrigin:"perspective-origin",pointerEvents:"pointer-events",position:"position",quotes:"quotes",right:"right",rotate:"rotate",rubyAlign:"ruby-align",rubyOverhang:"ruby-overhang",rubyPosition:"ruby-position",scale:"scale",size:"size",stopColor:"stop-color",stopOpacity:"stop-opacity",stroke:"stroke",strokeDasharray:"stroke-dasharray",strokeDashoffset:"stroke-dashoffset",strokeLinecap:"stroke-linecap",strokeLinejoin:"stroke-linejoin",strokeMiterlimit:"stroke-miterlimit",strokeOpacity:"stroke-opacity",strokeWidth:"stroke-width",tableLayout:"table-layout",textAlign:"text-align",textAlignLast:"text-align-last",textAnchor:"text-anchor",textDecoration:"text-decoration",textIndent:"text-indent",textJustify:"text-justify",textKashida:"text-kashida",textKashidaSpace:"text-kashida-space",textOverflow:"text-overflow",textShadow:"text-shadow",textTransform:"text-transform",textUnderlinePosition:"text-underline-position",top:"top",touchAction:"touch-action",transform:"transform",transformOrigin:"transform-origin",transformStyle:"transform-style",transition:"transition",transitionDelay:"transition-delay",transitionDuration:"transition-duration",transitionProperty:"transition-property",transitionTimingFunction:"transition-timing-function",translate:"translate",unicodeBidi:"unicode-bidi",verticalAlign:"vertical-align",visibility:"visibility",webkitAlignContent:"-webkit-align-content",webkitAlignItems:"-webkit-align-items",webkitAlignSelf:"-webkit-align-self",webkitAnimation:"-webkit-animation",webkitAnimationDelay:"-webkit-animation-delay",webkitAnimationDirection:"-webkit-animation-direction",webkitAnimationDuration:"-webkit-animation-duration",webkitAnimationFillMode:"-webkit-animation-fill-mode",webkitAnimationIterationCount:"-webkit-animation-iteration-count",webkitAnimationName:"-webkit-animation-name",webkitAnimationPlayState:"-webkit-animation-play-state",webkitAnimationTimingFunction:"-webkit-animation-timing-funciton",webkitAppearance:"-webkit-appearance",webkitBackfaceVisibility:"-webkit-backface-visibility",webkitBackgroundClip:"-webkit-background-clip",webkitBackgroundOrigin:"-webkit-background-origin",webkitBackgroundSize:"-webkit-background-size",webkitBorderBottomLeftRadius:"-webkit-border-bottom-left-radius",webkitBorderBottomRightRadius:"-webkit-border-bottom-right-radius",webkitBorderImage:"-webkit-border-image",webkitBorderRadius:"-webkit-border-radius",webkitBorderTopLeftRadius:"-webkit-border-top-left-radius",webkitBorderTopRightRadius:"-webkit-border-top-right-radius",webkitBoxAlign:"-webkit-box-align",webkitBoxDirection:"-webkit-box-direction",webkitBoxFlex:"-webkit-box-flex",webkitBoxOrdinalGroup:"-webkit-box-ordinal-group",webkitBoxOrient:"-webkit-box-orient",webkitBoxPack:"-webkit-box-pack",webkitBoxSizing:"-webkit-box-sizing",webkitColumnBreakAfter:"-webkit-column-break-after",webkitColumnBreakBefore:"-webkit-column-break-before",webkitColumnBreakInside:"-webkit-column-break-inside",webkitColumnCount:"-webkit-column-count",webkitColumnGap:"-webkit-column-gap",webkitColumnRule:"-webkit-column-rule",webkitColumnRuleColor:"-webkit-column-rule-color",webkitColumnRuleStyle:"-webkit-column-rule-style",webkitColumnRuleWidth:"-webkit-column-rule-width",webkitColumns:"-webkit-columns",webkitColumnSpan:"-webkit-column-span",webkitColumnWidth:"-webkit-column-width",webkitFilter:"-webkit-filter",webkitFlex:"-webkit-flex",webkitFlexBasis:"-webkit-flex-basis",webkitFlexDirection:"-webkit-flex-direction",webkitFlexFlow:"-webkit-flex-flow",webkitFlexGrow:"-webkit-flex-grow",webkitFlexShrink:"-webkit-flex-shrink",webkitFlexWrap:"-webkit-flex-wrap",webkitJustifyContent:"-webkit-justify-content",webkitOrder:"-webkit-order",webkitPerspective:"-webkit-perspective-origin",webkitPerspectiveOrigin:"-webkit-perspective-origin",webkitTapHighlightColor:"-webkit-tap-highlight-color",webkitTextFillColor:"-webkit-text-fill-color",webkitTextSizeAdjust:"-webkit-text-size-adjust",webkitTextStroke:"-webkit-text-stroke",webkitTextStrokeColor:"-webkit-text-stroke-color",webkitTextStrokeWidth:"-webkit-text-stroke-width",webkitTransform:"-webkit-transform",webkitTransformOrigin:"-webkit-transform-origin",webkitTransformStyle:"-webkit-transform-style",webkitTransition:"-webkit-transition",webkitTransitionDelay:"-webkit-transition-delay",webkitTransitionDuration:"-webkit-transition-duration",webkitTransitionProperty:"-webkit-transition-property",webkitTransitionTimingFunction:"-webkit-transition-timing-function",webkitUserModify:"-webkit-user-modify",webkitUserSelect:"-webkit-user-select",webkitWritingMode:"-webkit-writing-mode",whiteSpace:"white-space",widows:"widows",width:"width",wordBreak:"word-break",wordSpacing:"word-spacing",wordWrap:"word-wrap",writingMode:"writing-mode",zIndex:"z-index",zoom:"zoom",resize:"resize",userSelect:"user-select"};for(var c in s)l(c);function l(e){var t=s[e];Object.defineProperty(i.prototype,e,{get:function(){return this.getPropertyValue(t)},set:function(e){this.setProperty(t,e)}}),i.prototype.hasOwnProperty(t)||Object.defineProperty(i.prototype,t,{get:function(){return this.getPropertyValue(t)},set:function(e){this.setProperty(t,e)}})}},function(e,t,r){"use strict";var n=r(5),i=r(22),a=r(59),o=r(40),s=r(0);function c(e){this.document=e||new n(null).createHTMLDocument(""),this.document._scripting_enabled=!0,this.document.defaultView=this,this.location=new a(this,this.document._address||"about:blank")}e.exports=c,c.prototype=Object.create(i.prototype,{_run:{value:o.Window_run},console:{value:console},history:{value:{back:s.nyi,forward:s.nyi,go:s.nyi}},navigator:{value:r(60)},window:{get:function(){return this}},self:{get:function(){return this}},frames:{get:function(){return this}},parent:{get:function(){return this}},top:{get:function(){return this}},length:{value:0},frameElement:{value:null},opener:{value:null},onload:{get:function(){return this._getEventHandler("load")},set:function(e){this._setEventHandler("load",e)}},getComputedStyle:{value:function(e){return e.style}}}),s.expose(r(61),c),s.expose(r(41),c)},function(e,t,r){"use strict";e.exports=o;var n=r(1),i=r(32),a=r(15);function o(e,t,r,a){i.call(this),this.nodeType=n.DOCUMENT_TYPE_NODE,this.ownerDocument=e||null,this.name=t,this.publicId=r||"",this.systemId=a||""}o.prototype=Object.create(i.prototype,{nodeName:{get:function(){return this.name}},nodeValue:{get:function(){return null},set:function(){}},clone:{value:function(){return new o(this.ownerDocument,this.name,this.publicId,this.systemId)}},isEqual:{value:function(e){return this.name===e.name&&this.publicId===e.publicId&&this.systemId===e.systemId}}}),Object.defineProperties(o.prototype,a)},function(e,t,r){"use strict";e.exports=oe;var n=r(9),i=r(20),a=r(1),o=r(0).NAMESPACE,s=r(17),c=s.elements,l=Function.prototype.apply.bind(Array.prototype.push),u=[],h=/^HTML$|^-\/\/W3O\/\/DTD W3 HTML Strict 3\.0\/\/EN\/\/$|^-\/W3C\/DTD HTML 4\.0 Transitional\/EN$|^\+\/\/Silmaril\/\/dtd html Pro v0r11 19970101\/\/|^-\/\/AdvaSoft Ltd\/\/DTD HTML 3\.0 asWedit \+ extensions\/\/|^-\/\/AS\/\/DTD HTML 3\.0 asWedit \+ extensions\/\/|^-\/\/IETF\/\/DTD HTML 2\.0 Level 1\/\/|^-\/\/IETF\/\/DTD HTML 2\.0 Level 2\/\/|^-\/\/IETF\/\/DTD HTML 2\.0 Strict Level 1\/\/|^-\/\/IETF\/\/DTD HTML 2\.0 Strict Level 2\/\/|^-\/\/IETF\/\/DTD HTML 2\.0 Strict\/\/|^-\/\/IETF\/\/DTD HTML 2\.0\/\/|^-\/\/IETF\/\/DTD HTML 2\.1E\/\/|^-\/\/IETF\/\/DTD HTML 3\.0\/\/|^-\/\/IETF\/\/DTD HTML 3\.2 Final\/\/|^-\/\/IETF\/\/DTD HTML 3\.2\/\/|^-\/\/IETF\/\/DTD HTML 3\/\/|^-\/\/IETF\/\/DTD HTML Level 0\/\/|^-\/\/IETF\/\/DTD HTML Level 1\/\/|^-\/\/IETF\/\/DTD HTML Level 2\/\/|^-\/\/IETF\/\/DTD HTML Level 3\/\/|^-\/\/IETF\/\/DTD HTML Strict Level 0\/\/|^-\/\/IETF\/\/DTD HTML Strict Level 1\/\/|^-\/\/IETF\/\/DTD HTML Strict Level 2\/\/|^-\/\/IETF\/\/DTD HTML Strict Level 3\/\/|^-\/\/IETF\/\/DTD HTML Strict\/\/|^-\/\/IETF\/\/DTD HTML\/\/|^-\/\/Metrius\/\/DTD Metrius Presentational\/\/|^-\/\/Microsoft\/\/DTD Internet Explorer 2\.0 HTML Strict\/\/|^-\/\/Microsoft\/\/DTD Internet Explorer 2\.0 HTML\/\/|^-\/\/Microsoft\/\/DTD Internet Explorer 2\.0 Tables\/\/|^-\/\/Microsoft\/\/DTD Internet Explorer 3\.0 HTML Strict\/\/|^-\/\/Microsoft\/\/DTD Internet Explorer 3\.0 HTML\/\/|^-\/\/Microsoft\/\/DTD Internet Explorer 3\.0 Tables\/\/|^-\/\/Netscape Comm\. Corp\.\/\/DTD HTML\/\/|^-\/\/Netscape Comm\. Corp\.\/\/DTD Strict HTML\/\/|^-\/\/O'Reilly and Associates\/\/DTD HTML 2\.0\/\/|^-\/\/O'Reilly and Associates\/\/DTD HTML Extended 1\.0\/\/|^-\/\/O'Reilly and Associates\/\/DTD HTML Extended Relaxed 1\.0\/\/|^-\/\/SoftQuad Software\/\/DTD HoTMetaL PRO 6\.0::19990601::extensions to HTML 4\.0\/\/|^-\/\/SoftQuad\/\/DTD HoTMetaL PRO 4\.0::19971010::extensions to HTML 4\.0\/\/|^-\/\/Spyglass\/\/DTD HTML 2\.0 Extended\/\/|^-\/\/SQ\/\/DTD HTML 2\.0 HoTMetaL \+ extensions\/\/|^-\/\/Sun Microsystems Corp\.\/\/DTD HotJava HTML\/\/|^-\/\/Sun Microsystems Corp\.\/\/DTD HotJava Strict HTML\/\/|^-\/\/W3C\/\/DTD HTML 3 1995-03-24\/\/|^-\/\/W3C\/\/DTD HTML 3\.2 Draft\/\/|^-\/\/W3C\/\/DTD HTML 3\.2 Final\/\/|^-\/\/W3C\/\/DTD HTML 3\.2\/\/|^-\/\/W3C\/\/DTD HTML 3\.2S Draft\/\/|^-\/\/W3C\/\/DTD HTML 4\.0 Frameset\/\/|^-\/\/W3C\/\/DTD HTML 4\.0 Transitional\/\/|^-\/\/W3C\/\/DTD HTML Experimental 19960712\/\/|^-\/\/W3C\/\/DTD HTML Experimental 970421\/\/|^-\/\/W3C\/\/DTD W3 HTML\/\/|^-\/\/W3O\/\/DTD W3 HTML 3\.0\/\/|^-\/\/WebTechs\/\/DTD Mozilla HTML 2\.0\/\/|^-\/\/WebTechs\/\/DTD Mozilla HTML\/\//i,d=/^-\/\/W3C\/\/DTD HTML 4\.01 Frameset\/\/|^-\/\/W3C\/\/DTD HTML 4\.01 Transitional\/\//i,p=/^-\/\/W3C\/\/DTD XHTML 1\.0 Frameset\/\/|^-\/\/W3C\/\/DTD XHTML 1\.0 Transitional\/\//i,f=Object.create(null);f[o.HTML]={__proto__:null,address:!0,applet:!0,area:!0,article:!0,aside:!0,base:!0,basefont:!0,bgsound:!0,blockquote:!0,body:!0,br:!0,button:!0,caption:!0,center:!0,col:!0,colgroup:!0,dd:!0,details:!0,dir:!0,div:!0,dl:!0,dt:!0,embed:!0,fieldset:!0,figcaption:!0,figure:!0,footer:!0,form:!0,frame:!0,frameset:!0,h1:!0,h2:!0,h3:!0,h4:!0,h5:!0,h6:!0,head:!0,header:!0,hgroup:!0,hr:!0,html:!0,iframe:!0,img:!0,input:!0,li:!0,link:!0,listing:!0,main:!0,marquee:!0,menu:!0,meta:!0,nav:!0,noembed:!0,noframes:!0,noscript:!0,object:!0,ol:!0,p:!0,param:!0,plaintext:!0,pre:!0,script:!0,section:!0,select:!0,source:!0,style:!0,summary:!0,table:!0,tbody:!0,td:!0,template:!0,textarea:!0,tfoot:!0,th:!0,thead:!0,title:!0,tr:!0,track:!0,ul:!0,wbr:!0,xmp:!0},f[o.SVG]={__proto__:null,foreignObject:!0,desc:!0,title:!0},f[o.MATHML]={__proto__:null,mi:!0,mo:!0,mn:!0,ms:!0,mtext:!0,"annotation-xml":!0};var m=Object.create(null);m[o.HTML]={__proto__:null,address:!0,div:!0,p:!0};var g=Object.create(null);g[o.HTML]={__proto__:null,dd:!0,dt:!0};var b=Object.create(null);b[o.HTML]={__proto__:null,table:!0,thead:!0,tbody:!0,tfoot:!0,tr:!0};var v=Object.create(null);v[o.HTML]={__proto__:null,dd:!0,dt:!0,li:!0,menuitem:!0,optgroup:!0,option:!0,p:!0,rb:!0,rp:!0,rt:!0,rtc:!0};var _=Object.create(null);_[o.HTML]={__proto__:null,caption:!0,colgroup:!0,dd:!0,dt:!0,li:!0,optgroup:!0,option:!0,p:!0,rb:!0,rp:!0,rt:!0,rtc:!0,tbody:!0,td:!0,tfoot:!0,th:!0,thead:!0,tr:!0};var y=Object.create(null);y[o.HTML]={__proto__:null,table:!0,template:!0,html:!0};var w=Object.create(null);w[o.HTML]={__proto__:null,tbody:!0,tfoot:!0,thead:!0,template:!0,html:!0};var E=Object.create(null);E[o.HTML]={__proto__:null,tr:!0,template:!0,html:!0};var k=Object.create(null);k[o.HTML]={__proto__:null,button:!0,fieldset:!0,input:!0,keygen:!0,object:!0,output:!0,select:!0,textarea:!0,img:!0};var T=Object.create(null);T[o.HTML]={__proto__:null,applet:!0,caption:!0,html:!0,table:!0,td:!0,th:!0,marquee:!0,object:!0,template:!0},T[o.MATHML]={__proto__:null,mi:!0,mo:!0,mn:!0,ms:!0,mtext:!0,"annotation-xml":!0},T[o.SVG]={__proto__:null,foreignObject:!0,desc:!0,title:!0};var S=Object.create(T);S[o.HTML]=Object.create(T[o.HTML]),S[o.HTML].ol=!0,S[o.HTML].ul=!0;var x=Object.create(T);x[o.HTML]=Object.create(T[o.HTML]),x[o.HTML].button=!0;var N=Object.create(null);N[o.HTML]={__proto__:null,html:!0,table:!0,template:!0},Object.create(null)[o.HTML]={__proto__:null,optgroup:!0,option:!0};var C=Object.create(null);C[o.MATHML]={__proto__:null,mi:!0,mo:!0,mn:!0,ms:!0,mtext:!0};var A=Object.create(null);A[o.SVG]={__proto__:null,foreignObject:!0,desc:!0,title:!0};var R={__proto__:null,"xlink:actuate":o.XLINK,"xlink:arcrole":o.XLINK,"xlink:href":o.XLINK,"xlink:role":o.XLINK,"xlink:show":o.XLINK,"xlink:title":o.XLINK,"xlink:type":o.XLINK,"xml:base":o.XML,"xml:lang":o.XML,"xml:space":o.XML,xmlns:o.XMLNS,"xmlns:xlink":o.XMLNS},L={__proto__:null,attributename:"attributeName",attributetype:"attributeType",basefrequency:"baseFrequency",baseprofile:"baseProfile",calcmode:"calcMode",clippathunits:"clipPathUnits",diffuseconstant:"diffuseConstant",edgemode:"edgeMode",filterunits:"filterUnits",glyphref:"glyphRef",gradienttransform:"gradientTransform",gradientunits:"gradientUnits",kernelmatrix:"kernelMatrix",kernelunitlength:"kernelUnitLength",keypoints:"keyPoints",keysplines:"keySplines",keytimes:"keyTimes",lengthadjust:"lengthAdjust",limitingconeangle:"limitingConeAngle",markerheight:"markerHeight",markerunits:"markerUnits",markerwidth:"markerWidth",maskcontentunits:"maskContentUnits",maskunits:"maskUnits",numoctaves:"numOctaves",pathlength:"pathLength",patterncontentunits:"patternContentUnits",patterntransform:"patternTransform",patternunits:"patternUnits",pointsatx:"pointsAtX",pointsaty:"pointsAtY",pointsatz:"pointsAtZ",preservealpha:"preserveAlpha",preserveaspectratio:"preserveAspectRatio",primitiveunits:"primitiveUnits",refx:"refX",refy:"refY",repeatcount:"repeatCount",repeatdur:"repeatDur",requiredextensions:"requiredExtensions",requiredfeatures:"requiredFeatures",specularconstant:"specularConstant",specularexponent:"specularExponent",spreadmethod:"spreadMethod",startoffset:"startOffset",stddeviation:"stdDeviation",stitchtiles:"stitchTiles",surfacescale:"surfaceScale",systemlanguage:"systemLanguage",tablevalues:"tableValues",targetx:"targetX",targety:"targetY",textlength:"textLength",viewbox:"viewBox",viewtarget:"viewTarget",xchannelselector:"xChannelSelector",ychannelselector:"yChannelSelector",zoomandpan:"zoomAndPan"},O={__proto__:null,altglyph:"altGlyph",altglyphdef:"altGlyphDef",altglyphitem:"altGlyphItem",animatecolor:"animateColor",animatemotion:"animateMotion",animatetransform:"animateTransform",clippath:"clipPath",feblend:"feBlend",fecolormatrix:"feColorMatrix",fecomponenttransfer:"feComponentTransfer",fecomposite:"feComposite",feconvolvematrix:"feConvolveMatrix",fediffuselighting:"feDiffuseLighting",fedisplacementmap:"feDisplacementMap",fedistantlight:"feDistantLight",feflood:"feFlood",fefunca:"feFuncA",fefuncb:"feFuncB",fefuncg:"feFuncG",fefuncr:"feFuncR",fegaussianblur:"feGaussianBlur",feimage:"feImage",femerge:"feMerge",femergenode:"feMergeNode",femorphology:"feMorphology",feoffset:"feOffset",fepointlight:"fePointLight",fespecularlighting:"feSpecularLighting",fespotlight:"feSpotLight",fetile:"feTile",feturbulence:"feTurbulence",foreignobject:"foreignObject",glyphref:"glyphRef",lineargradient:"linearGradient",radialgradient:"radialGradient",textpath:"textPath"},I={__proto__:null,0:65533,128:8364,130:8218,131:402,132:8222,133:8230,134:8224,135:8225,136:710,137:8240,138:352,139:8249,140:338,142:381,145:8216,146:8217,147:8220,148:8221,149:8226,150:8211,151:8212,152:732,153:8482,154:353,155:8250,156:339,158:382,159:376},D={__proto__:null,AElig:198,"AElig;":198,AMP:38,"AMP;":38,Aacute:193,"Aacute;":193,"Abreve;":258,Acirc:194,"Acirc;":194,"Acy;":1040,"Afr;":[55349,56580],Agrave:192,"Agrave;":192,"Alpha;":913,"Amacr;":256,"And;":10835,"Aogon;":260,"Aopf;":[55349,56632],"ApplyFunction;":8289,Aring:197,"Aring;":197,"Ascr;":[55349,56476],"Assign;":8788,Atilde:195,"Atilde;":195,Auml:196,"Auml;":196,"Backslash;":8726,"Barv;":10983,"Barwed;":8966,"Bcy;":1041,"Because;":8757,"Bernoullis;":8492,"Beta;":914,"Bfr;":[55349,56581],"Bopf;":[55349,56633],"Breve;":728,"Bscr;":8492,"Bumpeq;":8782,"CHcy;":1063,COPY:169,"COPY;":169,"Cacute;":262,"Cap;":8914,"CapitalDifferentialD;":8517,"Cayleys;":8493,"Ccaron;":268,Ccedil:199,"Ccedil;":199,"Ccirc;":264,"Cconint;":8752,"Cdot;":266,"Cedilla;":184,"CenterDot;":183,"Cfr;":8493,"Chi;":935,"CircleDot;":8857,"CircleMinus;":8854,"CirclePlus;":8853,"CircleTimes;":8855,"ClockwiseContourIntegral;":8754,"CloseCurlyDoubleQuote;":8221,"CloseCurlyQuote;":8217,"Colon;":8759,"Colone;":10868,"Congruent;":8801,"Conint;":8751,"ContourIntegral;":8750,"Copf;":8450,"Coproduct;":8720,"CounterClockwiseContourIntegral;":8755,"Cross;":10799,"Cscr;":[55349,56478],"Cup;":8915,"CupCap;":8781,"DD;":8517,"DDotrahd;":10513,"DJcy;":1026,"DScy;":1029,"DZcy;":1039,"Dagger;":8225,"Darr;":8609,"Dashv;":10980,"Dcaron;":270,"Dcy;":1044,"Del;":8711,"Delta;":916,"Dfr;":[55349,56583],"DiacriticalAcute;":180,"DiacriticalDot;":729,"DiacriticalDoubleAcute;":733,"DiacriticalGrave;":96,"DiacriticalTilde;":732,"Diamond;":8900,"DifferentialD;":8518,"Dopf;":[55349,56635],"Dot;":168,"DotDot;":8412,"DotEqual;":8784,"DoubleContourIntegral;":8751,"DoubleDot;":168,"DoubleDownArrow;":8659,"DoubleLeftArrow;":8656,"DoubleLeftRightArrow;":8660,"DoubleLeftTee;":10980,"DoubleLongLeftArrow;":10232,"DoubleLongLeftRightArrow;":10234,"DoubleLongRightArrow;":10233,"DoubleRightArrow;":8658,"DoubleRightTee;":8872,"DoubleUpArrow;":8657,"DoubleUpDownArrow;":8661,"DoubleVerticalBar;":8741,"DownArrow;":8595,"DownArrowBar;":10515,"DownArrowUpArrow;":8693,"DownBreve;":785,"DownLeftRightVector;":10576,"DownLeftTeeVector;":10590,"DownLeftVector;":8637,"DownLeftVectorBar;":10582,"DownRightTeeVector;":10591,"DownRightVector;":8641,"DownRightVectorBar;":10583,"DownTee;":8868,"DownTeeArrow;":8615,"Downarrow;":8659,"Dscr;":[55349,56479],"Dstrok;":272,"ENG;":330,ETH:208,"ETH;":208,Eacute:201,"Eacute;":201,"Ecaron;":282,Ecirc:202,"Ecirc;":202,"Ecy;":1069,"Edot;":278,"Efr;":[55349,56584],Egrave:200,"Egrave;":200,"Element;":8712,"Emacr;":274,"EmptySmallSquare;":9723,"EmptyVerySmallSquare;":9643,"Eogon;":280,"Eopf;":[55349,56636],"Epsilon;":917,"Equal;":10869,"EqualTilde;":8770,"Equilibrium;":8652,"Escr;":8496,"Esim;":10867,"Eta;":919,Euml:203,"Euml;":203,"Exists;":8707,"ExponentialE;":8519,"Fcy;":1060,"Ffr;":[55349,56585],"FilledSmallSquare;":9724,"FilledVerySmallSquare;":9642,"Fopf;":[55349,56637],"ForAll;":8704,"Fouriertrf;":8497,"Fscr;":8497,"GJcy;":1027,GT:62,"GT;":62,"Gamma;":915,"Gammad;":988,"Gbreve;":286,"Gcedil;":290,"Gcirc;":284,"Gcy;":1043,"Gdot;":288,"Gfr;":[55349,56586],"Gg;":8921,"Gopf;":[55349,56638],"GreaterEqual;":8805,"GreaterEqualLess;":8923,"GreaterFullEqual;":8807,"GreaterGreater;":10914,"GreaterLess;":8823,"GreaterSlantEqual;":10878,"GreaterTilde;":8819,"Gscr;":[55349,56482],"Gt;":8811,"HARDcy;":1066,"Hacek;":711,"Hat;":94,"Hcirc;":292,"Hfr;":8460,"HilbertSpace;":8459,"Hopf;":8461,"HorizontalLine;":9472,"Hscr;":8459,"Hstrok;":294,"HumpDownHump;":8782,"HumpEqual;":8783,"IEcy;":1045,"IJlig;":306,"IOcy;":1025,Iacute:205,"Iacute;":205,Icirc:206,"Icirc;":206,"Icy;":1048,"Idot;":304,"Ifr;":8465,Igrave:204,"Igrave;":204,"Im;":8465,"Imacr;":298,"ImaginaryI;":8520,"Implies;":8658,"Int;":8748,"Integral;":8747,"Intersection;":8898,"InvisibleComma;":8291,"InvisibleTimes;":8290,"Iogon;":302,"Iopf;":[55349,56640],"Iota;":921,"Iscr;":8464,"Itilde;":296,"Iukcy;":1030,Iuml:207,"Iuml;":207,"Jcirc;":308,"Jcy;":1049,"Jfr;":[55349,56589],"Jopf;":[55349,56641],"Jscr;":[55349,56485],"Jsercy;":1032,"Jukcy;":1028,"KHcy;":1061,"KJcy;":1036,"Kappa;":922,"Kcedil;":310,"Kcy;":1050,"Kfr;":[55349,56590],"Kopf;":[55349,56642],"Kscr;":[55349,56486],"LJcy;":1033,LT:60,"LT;":60,"Lacute;":313,"Lambda;":923,"Lang;":10218,"Laplacetrf;":8466,"Larr;":8606,"Lcaron;":317,"Lcedil;":315,"Lcy;":1051,"LeftAngleBracket;":10216,"LeftArrow;":8592,"LeftArrowBar;":8676,"LeftArrowRightArrow;":8646,"LeftCeiling;":8968,"LeftDoubleBracket;":10214,"LeftDownTeeVector;":10593,"LeftDownVector;":8643,"LeftDownVectorBar;":10585,"LeftFloor;":8970,"LeftRightArrow;":8596,"LeftRightVector;":10574,"LeftTee;":8867,"LeftTeeArrow;":8612,"LeftTeeVector;":10586,"LeftTriangle;":8882,"LeftTriangleBar;":10703,"LeftTriangleEqual;":8884,"LeftUpDownVector;":10577,"LeftUpTeeVector;":10592,"LeftUpVector;":8639,"LeftUpVectorBar;":10584,"LeftVector;":8636,"LeftVectorBar;":10578,"Leftarrow;":8656,"Leftrightarrow;":8660,"LessEqualGreater;":8922,"LessFullEqual;":8806,"LessGreater;":8822,"LessLess;":10913,"LessSlantEqual;":10877,"LessTilde;":8818,"Lfr;":[55349,56591],"Ll;":8920,"Lleftarrow;":8666,"Lmidot;":319,"LongLeftArrow;":10229,"LongLeftRightArrow;":10231,"LongRightArrow;":10230,"Longleftarrow;":10232,"Longleftrightarrow;":10234,"Longrightarrow;":10233,"Lopf;":[55349,56643],"LowerLeftArrow;":8601,"LowerRightArrow;":8600,"Lscr;":8466,"Lsh;":8624,"Lstrok;":321,"Lt;":8810,"Map;":10501,"Mcy;":1052,"MediumSpace;":8287,"Mellintrf;":8499,"Mfr;":[55349,56592],"MinusPlus;":8723,"Mopf;":[55349,56644],"Mscr;":8499,"Mu;":924,"NJcy;":1034,"Nacute;":323,"Ncaron;":327,"Ncedil;":325,"Ncy;":1053,"NegativeMediumSpace;":8203,"NegativeThickSpace;":8203,"NegativeThinSpace;":8203,"NegativeVeryThinSpace;":8203,"NestedGreaterGreater;":8811,"NestedLessLess;":8810,"NewLine;":10,"Nfr;":[55349,56593],"NoBreak;":8288,"NonBreakingSpace;":160,"Nopf;":8469,"Not;":10988,"NotCongruent;":8802,"NotCupCap;":8813,"NotDoubleVerticalBar;":8742,"NotElement;":8713,"NotEqual;":8800,"NotEqualTilde;":[8770,824],"NotExists;":8708,"NotGreater;":8815,"NotGreaterEqual;":8817,"NotGreaterFullEqual;":[8807,824],"NotGreaterGreater;":[8811,824],"NotGreaterLess;":8825,"NotGreaterSlantEqual;":[10878,824],"NotGreaterTilde;":8821,"NotHumpDownHump;":[8782,824],"NotHumpEqual;":[8783,824],"NotLeftTriangle;":8938,"NotLeftTriangleBar;":[10703,824],"NotLeftTriangleEqual;":8940,"NotLess;":8814,"NotLessEqual;":8816,"NotLessGreater;":8824,"NotLessLess;":[8810,824],"NotLessSlantEqual;":[10877,824],"NotLessTilde;":8820,"NotNestedGreaterGreater;":[10914,824],"NotNestedLessLess;":[10913,824],"NotPrecedes;":8832,"NotPrecedesEqual;":[10927,824],"NotPrecedesSlantEqual;":8928,"NotReverseElement;":8716,"NotRightTriangle;":8939,"NotRightTriangleBar;":[10704,824],"NotRightTriangleEqual;":8941,"NotSquareSubset;":[8847,824],"NotSquareSubsetEqual;":8930,"NotSquareSuperset;":[8848,824],"NotSquareSupersetEqual;":8931,"NotSubset;":[8834,8402],"NotSubsetEqual;":8840,"NotSucceeds;":8833,"NotSucceedsEqual;":[10928,824],"NotSucceedsSlantEqual;":8929,"NotSucceedsTilde;":[8831,824],"NotSuperset;":[8835,8402],"NotSupersetEqual;":8841,"NotTilde;":8769,"NotTildeEqual;":8772,"NotTildeFullEqual;":8775,"NotTildeTilde;":8777,"NotVerticalBar;":8740,"Nscr;":[55349,56489],Ntilde:209,"Ntilde;":209,"Nu;":925,"OElig;":338,Oacute:211,"Oacute;":211,Ocirc:212,"Ocirc;":212,"Ocy;":1054,"Odblac;":336,"Ofr;":[55349,56594],Ograve:210,"Ograve;":210,"Omacr;":332,"Omega;":937,"Omicron;":927,"Oopf;":[55349,56646],"OpenCurlyDoubleQuote;":8220,"OpenCurlyQuote;":8216,"Or;":10836,"Oscr;":[55349,56490],Oslash:216,"Oslash;":216,Otilde:213,"Otilde;":213,"Otimes;":10807,Ouml:214,"Ouml;":214,"OverBar;":8254,"OverBrace;":9182,"OverBracket;":9140,"OverParenthesis;":9180,"PartialD;":8706,"Pcy;":1055,"Pfr;":[55349,56595],"Phi;":934,"Pi;":928,"PlusMinus;":177,"Poincareplane;":8460,"Popf;":8473,"Pr;":10939,"Precedes;":8826,"PrecedesEqual;":10927,"PrecedesSlantEqual;":8828,"PrecedesTilde;":8830,"Prime;":8243,"Product;":8719,"Proportion;":8759,"Proportional;":8733,"Pscr;":[55349,56491],"Psi;":936,QUOT:34,"QUOT;":34,"Qfr;":[55349,56596],"Qopf;":8474,"Qscr;":[55349,56492],"RBarr;":10512,REG:174,"REG;":174,"Racute;":340,"Rang;":10219,"Rarr;":8608,"Rarrtl;":10518,"Rcaron;":344,"Rcedil;":342,"Rcy;":1056,"Re;":8476,"ReverseElement;":8715,"ReverseEquilibrium;":8651,"ReverseUpEquilibrium;":10607,"Rfr;":8476,"Rho;":929,"RightAngleBracket;":10217,"RightArrow;":8594,"RightArrowBar;":8677,"RightArrowLeftArrow;":8644,"RightCeiling;":8969,"RightDoubleBracket;":10215,"RightDownTeeVector;":10589,"RightDownVector;":8642,"RightDownVectorBar;":10581,"RightFloor;":8971,"RightTee;":8866,"RightTeeArrow;":8614,"RightTeeVector;":10587,"RightTriangle;":8883,"RightTriangleBar;":10704,"RightTriangleEqual;":8885,"RightUpDownVector;":10575,"RightUpTeeVector;":10588,"RightUpVector;":8638,"RightUpVectorBar;":10580,"RightVector;":8640,"RightVectorBar;":10579,"Rightarrow;":8658,"Ropf;":8477,"RoundImplies;":10608,"Rrightarrow;":8667,"Rscr;":8475,"Rsh;":8625,"RuleDelayed;":10740,"SHCHcy;":1065,"SHcy;":1064,"SOFTcy;":1068,"Sacute;":346,"Sc;":10940,"Scaron;":352,"Scedil;":350,"Scirc;":348,"Scy;":1057,"Sfr;":[55349,56598],"ShortDownArrow;":8595,"ShortLeftArrow;":8592,"ShortRightArrow;":8594,"ShortUpArrow;":8593,"Sigma;":931,"SmallCircle;":8728,"Sopf;":[55349,56650],"Sqrt;":8730,"Square;":9633,"SquareIntersection;":8851,"SquareSubset;":8847,"SquareSubsetEqual;":8849,"SquareSuperset;":8848,"SquareSupersetEqual;":8850,"SquareUnion;":8852,"Sscr;":[55349,56494],"Star;":8902,"Sub;":8912,"Subset;":8912,"SubsetEqual;":8838,"Succeeds;":8827,"SucceedsEqual;":10928,"SucceedsSlantEqual;":8829,"SucceedsTilde;":8831,"SuchThat;":8715,"Sum;":8721,"Sup;":8913,"Superset;":8835,"SupersetEqual;":8839,"Supset;":8913,THORN:222,"THORN;":222,"TRADE;":8482,"TSHcy;":1035,"TScy;":1062,"Tab;":9,"Tau;":932,"Tcaron;":356,"Tcedil;":354,"Tcy;":1058,"Tfr;":[55349,56599],"Therefore;":8756,"Theta;":920,"ThickSpace;":[8287,8202],"ThinSpace;":8201,"Tilde;":8764,"TildeEqual;":8771,"TildeFullEqual;":8773,"TildeTilde;":8776,"Topf;":[55349,56651],"TripleDot;":8411,"Tscr;":[55349,56495],"Tstrok;":358,Uacute:218,"Uacute;":218,"Uarr;":8607,"Uarrocir;":10569,"Ubrcy;":1038,"Ubreve;":364,Ucirc:219,"Ucirc;":219,"Ucy;":1059,"Udblac;":368,"Ufr;":[55349,56600],Ugrave:217,"Ugrave;":217,"Umacr;":362,"UnderBar;":95,"UnderBrace;":9183,"UnderBracket;":9141,"UnderParenthesis;":9181,"Union;":8899,"UnionPlus;":8846,"Uogon;":370,"Uopf;":[55349,56652],"UpArrow;":8593,"UpArrowBar;":10514,"UpArrowDownArrow;":8645,"UpDownArrow;":8597,"UpEquilibrium;":10606,"UpTee;":8869,"UpTeeArrow;":8613,"Uparrow;":8657,"Updownarrow;":8661,"UpperLeftArrow;":8598,"UpperRightArrow;":8599,"Upsi;":978,"Upsilon;":933,"Uring;":366,"Uscr;":[55349,56496],"Utilde;":360,Uuml:220,"Uuml;":220,"VDash;":8875,"Vbar;":10987,"Vcy;":1042,"Vdash;":8873,"Vdashl;":10982,"Vee;":8897,"Verbar;":8214,"Vert;":8214,"VerticalBar;":8739,"VerticalLine;":124,"VerticalSeparator;":10072,"VerticalTilde;":8768,"VeryThinSpace;":8202,"Vfr;":[55349,56601],"Vopf;":[55349,56653],"Vscr;":[55349,56497],"Vvdash;":8874,"Wcirc;":372,"Wedge;":8896,"Wfr;":[55349,56602],"Wopf;":[55349,56654],"Wscr;":[55349,56498],"Xfr;":[55349,56603],"Xi;":926,"Xopf;":[55349,56655],"Xscr;":[55349,56499],"YAcy;":1071,"YIcy;":1031,"YUcy;":1070,Yacute:221,"Yacute;":221,"Ycirc;":374,"Ycy;":1067,"Yfr;":[55349,56604],"Yopf;":[55349,56656],"Yscr;":[55349,56500],"Yuml;":376,"ZHcy;":1046,"Zacute;":377,"Zcaron;":381,"Zcy;":1047,"Zdot;":379,"ZeroWidthSpace;":8203,"Zeta;":918,"Zfr;":8488,"Zopf;":8484,"Zscr;":[55349,56501],aacute:225,"aacute;":225,"abreve;":259,"ac;":8766,"acE;":[8766,819],"acd;":8767,acirc:226,"acirc;":226,acute:180,"acute;":180,"acy;":1072,aelig:230,"aelig;":230,"af;":8289,"afr;":[55349,56606],agrave:224,"agrave;":224,"alefsym;":8501,"aleph;":8501,"alpha;":945,"amacr;":257,"amalg;":10815,amp:38,"amp;":38,"and;":8743,"andand;":10837,"andd;":10844,"andslope;":10840,"andv;":10842,"ang;":8736,"ange;":10660,"angle;":8736,"angmsd;":8737,"angmsdaa;":10664,"angmsdab;":10665,"angmsdac;":10666,"angmsdad;":10667,"angmsdae;":10668,"angmsdaf;":10669,"angmsdag;":10670,"angmsdah;":10671,"angrt;":8735,"angrtvb;":8894,"angrtvbd;":10653,"angsph;":8738,"angst;":197,"angzarr;":9084,"aogon;":261,"aopf;":[55349,56658],"ap;":8776,"apE;":10864,"apacir;":10863,"ape;":8778,"apid;":8779,"apos;":39,"approx;":8776,"approxeq;":8778,aring:229,"aring;":229,"ascr;":[55349,56502],"ast;":42,"asymp;":8776,"asympeq;":8781,atilde:227,"atilde;":227,auml:228,"auml;":228,"awconint;":8755,"awint;":10769,"bNot;":10989,"backcong;":8780,"backepsilon;":1014,"backprime;":8245,"backsim;":8765,"backsimeq;":8909,"barvee;":8893,"barwed;":8965,"barwedge;":8965,"bbrk;":9141,"bbrktbrk;":9142,"bcong;":8780,"bcy;":1073,"bdquo;":8222,"becaus;":8757,"because;":8757,"bemptyv;":10672,"bepsi;":1014,"bernou;":8492,"beta;":946,"beth;":8502,"between;":8812,"bfr;":[55349,56607],"bigcap;":8898,"bigcirc;":9711,"bigcup;":8899,"bigodot;":10752,"bigoplus;":10753,"bigotimes;":10754,"bigsqcup;":10758,"bigstar;":9733,"bigtriangledown;":9661,"bigtriangleup;":9651,"biguplus;":10756,"bigvee;":8897,"bigwedge;":8896,"bkarow;":10509,"blacklozenge;":10731,"blacksquare;":9642,"blacktriangle;":9652,"blacktriangledown;":9662,"blacktriangleleft;":9666,"blacktriangleright;":9656,"blank;":9251,"blk12;":9618,"blk14;":9617,"blk34;":9619,"block;":9608,"bne;":[61,8421],"bnequiv;":[8801,8421],"bnot;":8976,"bopf;":[55349,56659],"bot;":8869,"bottom;":8869,"bowtie;":8904,"boxDL;":9559,"boxDR;":9556,"boxDl;":9558,"boxDr;":9555,"boxH;":9552,"boxHD;":9574,"boxHU;":9577,"boxHd;":9572,"boxHu;":9575,"boxUL;":9565,"boxUR;":9562,"boxUl;":9564,"boxUr;":9561,"boxV;":9553,"boxVH;":9580,"boxVL;":9571,"boxVR;":9568,"boxVh;":9579,"boxVl;":9570,"boxVr;":9567,"boxbox;":10697,"boxdL;":9557,"boxdR;":9554,"boxdl;":9488,"boxdr;":9484,"boxh;":9472,"boxhD;":9573,"boxhU;":9576,"boxhd;":9516,"boxhu;":9524,"boxminus;":8863,"boxplus;":8862,"boxtimes;":8864,"boxuL;":9563,"boxuR;":9560,"boxul;":9496,"boxur;":9492,"boxv;":9474,"boxvH;":9578,"boxvL;":9569,"boxvR;":9566,"boxvh;":9532,"boxvl;":9508,"boxvr;":9500,"bprime;":8245,"breve;":728,brvbar:166,"brvbar;":166,"bscr;":[55349,56503],"bsemi;":8271,"bsim;":8765,"bsime;":8909,"bsol;":92,"bsolb;":10693,"bsolhsub;":10184,"bull;":8226,"bullet;":8226,"bump;":8782,"bumpE;":10926,"bumpe;":8783,"bumpeq;":8783,"cacute;":263,"cap;":8745,"capand;":10820,"capbrcup;":10825,"capcap;":10827,"capcup;":10823,"capdot;":10816,"caps;":[8745,65024],"caret;":8257,"caron;":711,"ccaps;":10829,"ccaron;":269,ccedil:231,"ccedil;":231,"ccirc;":265,"ccups;":10828,"ccupssm;":10832,"cdot;":267,cedil:184,"cedil;":184,"cemptyv;":10674,cent:162,"cent;":162,"centerdot;":183,"cfr;":[55349,56608],"chcy;":1095,"check;":10003,"checkmark;":10003,"chi;":967,"cir;":9675,"cirE;":10691,"circ;":710,"circeq;":8791,"circlearrowleft;":8634,"circlearrowright;":8635,"circledR;":174,"circledS;":9416,"circledast;":8859,"circledcirc;":8858,"circleddash;":8861,"cire;":8791,"cirfnint;":10768,"cirmid;":10991,"cirscir;":10690,"clubs;":9827,"clubsuit;":9827,"colon;":58,"colone;":8788,"coloneq;":8788,"comma;":44,"commat;":64,"comp;":8705,"compfn;":8728,"complement;":8705,"complexes;":8450,"cong;":8773,"congdot;":10861,"conint;":8750,"copf;":[55349,56660],"coprod;":8720,copy:169,"copy;":169,"copysr;":8471,"crarr;":8629,"cross;":10007,"cscr;":[55349,56504],"csub;":10959,"csube;":10961,"csup;":10960,"csupe;":10962,"ctdot;":8943,"cudarrl;":10552,"cudarrr;":10549,"cuepr;":8926,"cuesc;":8927,"cularr;":8630,"cularrp;":10557,"cup;":8746,"cupbrcap;":10824,"cupcap;":10822,"cupcup;":10826,"cupdot;":8845,"cupor;":10821,"cups;":[8746,65024],"curarr;":8631,"curarrm;":10556,"curlyeqprec;":8926,"curlyeqsucc;":8927,"curlyvee;":8910,"curlywedge;":8911,curren:164,"curren;":164,"curvearrowleft;":8630,"curvearrowright;":8631,"cuvee;":8910,"cuwed;":8911,"cwconint;":8754,"cwint;":8753,"cylcty;":9005,"dArr;":8659,"dHar;":10597,"dagger;":8224,"daleth;":8504,"darr;":8595,"dash;":8208,"dashv;":8867,"dbkarow;":10511,"dblac;":733,"dcaron;":271,"dcy;":1076,"dd;":8518,"ddagger;":8225,"ddarr;":8650,"ddotseq;":10871,deg:176,"deg;":176,"delta;":948,"demptyv;":10673,"dfisht;":10623,"dfr;":[55349,56609],"dharl;":8643,"dharr;":8642,"diam;":8900,"diamond;":8900,"diamondsuit;":9830,"diams;":9830,"die;":168,"digamma;":989,"disin;":8946,"div;":247,divide:247,"divide;":247,"divideontimes;":8903,"divonx;":8903,"djcy;":1106,"dlcorn;":8990,"dlcrop;":8973,"dollar;":36,"dopf;":[55349,56661],"dot;":729,"doteq;":8784,"doteqdot;":8785,"dotminus;":8760,"dotplus;":8724,"dotsquare;":8865,"doublebarwedge;":8966,"downarrow;":8595,"downdownarrows;":8650,"downharpoonleft;":8643,"downharpoonright;":8642,"drbkarow;":10512,"drcorn;":8991,"drcrop;":8972,"dscr;":[55349,56505],"dscy;":1109,"dsol;":10742,"dstrok;":273,"dtdot;":8945,"dtri;":9663,"dtrif;":9662,"duarr;":8693,"duhar;":10607,"dwangle;":10662,"dzcy;":1119,"dzigrarr;":10239,"eDDot;":10871,"eDot;":8785,eacute:233,"eacute;":233,"easter;":10862,"ecaron;":283,"ecir;":8790,ecirc:234,"ecirc;":234,"ecolon;":8789,"ecy;":1101,"edot;":279,"ee;":8519,"efDot;":8786,"efr;":[55349,56610],"eg;":10906,egrave:232,"egrave;":232,"egs;":10902,"egsdot;":10904,"el;":10905,"elinters;":9191,"ell;":8467,"els;":10901,"elsdot;":10903,"emacr;":275,"empty;":8709,"emptyset;":8709,"emptyv;":8709,"emsp13;":8196,"emsp14;":8197,"emsp;":8195,"eng;":331,"ensp;":8194,"eogon;":281,"eopf;":[55349,56662],"epar;":8917,"eparsl;":10723,"eplus;":10865,"epsi;":949,"epsilon;":949,"epsiv;":1013,"eqcirc;":8790,"eqcolon;":8789,"eqsim;":8770,"eqslantgtr;":10902,"eqslantless;":10901,"equals;":61,"equest;":8799,"equiv;":8801,"equivDD;":10872,"eqvparsl;":10725,"erDot;":8787,"erarr;":10609,"escr;":8495,"esdot;":8784,"esim;":8770,"eta;":951,eth:240,"eth;":240,euml:235,"euml;":235,"euro;":8364,"excl;":33,"exist;":8707,"expectation;":8496,"exponentiale;":8519,"fallingdotseq;":8786,"fcy;":1092,"female;":9792,"ffilig;":64259,"fflig;":64256,"ffllig;":64260,"ffr;":[55349,56611],"filig;":64257,"fjlig;":[102,106],"flat;":9837,"fllig;":64258,"fltns;":9649,"fnof;":402,"fopf;":[55349,56663],"forall;":8704,"fork;":8916,"forkv;":10969,"fpartint;":10765,frac12:189,"frac12;":189,"frac13;":8531,frac14:188,"frac14;":188,"frac15;":8533,"frac16;":8537,"frac18;":8539,"frac23;":8532,"frac25;":8534,frac34:190,"frac34;":190,"frac35;":8535,"frac38;":8540,"frac45;":8536,"frac56;":8538,"frac58;":8541,"frac78;":8542,"frasl;":8260,"frown;":8994,"fscr;":[55349,56507],"gE;":8807,"gEl;":10892,"gacute;":501,"gamma;":947,"gammad;":989,"gap;":10886,"gbreve;":287,"gcirc;":285,"gcy;":1075,"gdot;":289,"ge;":8805,"gel;":8923,"geq;":8805,"geqq;":8807,"geqslant;":10878,"ges;":10878,"gescc;":10921,"gesdot;":10880,"gesdoto;":10882,"gesdotol;":10884,"gesl;":[8923,65024],"gesles;":10900,"gfr;":[55349,56612],"gg;":8811,"ggg;":8921,"gimel;":8503,"gjcy;":1107,"gl;":8823,"glE;":10898,"gla;":10917,"glj;":10916,"gnE;":8809,"gnap;":10890,"gnapprox;":10890,"gne;":10888,"gneq;":10888,"gneqq;":8809,"gnsim;":8935,"gopf;":[55349,56664],"grave;":96,"gscr;":8458,"gsim;":8819,"gsime;":10894,"gsiml;":10896,gt:62,"gt;":62,"gtcc;":10919,"gtcir;":10874,"gtdot;":8919,"gtlPar;":10645,"gtquest;":10876,"gtrapprox;":10886,"gtrarr;":10616,"gtrdot;":8919,"gtreqless;":8923,"gtreqqless;":10892,"gtrless;":8823,"gtrsim;":8819,"gvertneqq;":[8809,65024],"gvnE;":[8809,65024],"hArr;":8660,"hairsp;":8202,"half;":189,"hamilt;":8459,"hardcy;":1098,"harr;":8596,"harrcir;":10568,"harrw;":8621,"hbar;":8463,"hcirc;":293,"hearts;":9829,"heartsuit;":9829,"hellip;":8230,"hercon;":8889,"hfr;":[55349,56613],"hksearow;":10533,"hkswarow;":10534,"hoarr;":8703,"homtht;":8763,"hookleftarrow;":8617,"hookrightarrow;":8618,"hopf;":[55349,56665],"horbar;":8213,"hscr;":[55349,56509],"hslash;":8463,"hstrok;":295,"hybull;":8259,"hyphen;":8208,iacute:237,"iacute;":237,"ic;":8291,icirc:238,"icirc;":238,"icy;":1080,"iecy;":1077,iexcl:161,"iexcl;":161,"iff;":8660,"ifr;":[55349,56614],igrave:236,"igrave;":236,"ii;":8520,"iiiint;":10764,"iiint;":8749,"iinfin;":10716,"iiota;":8489,"ijlig;":307,"imacr;":299,"image;":8465,"imagline;":8464,"imagpart;":8465,"imath;":305,"imof;":8887,"imped;":437,"in;":8712,"incare;":8453,"infin;":8734,"infintie;":10717,"inodot;":305,"int;":8747,"intcal;":8890,"integers;":8484,"intercal;":8890,"intlarhk;":10775,"intprod;":10812,"iocy;":1105,"iogon;":303,"iopf;":[55349,56666],"iota;":953,"iprod;":10812,iquest:191,"iquest;":191,"iscr;":[55349,56510],"isin;":8712,"isinE;":8953,"isindot;":8949,"isins;":8948,"isinsv;":8947,"isinv;":8712,"it;":8290,"itilde;":297,"iukcy;":1110,iuml:239,"iuml;":239,"jcirc;":309,"jcy;":1081,"jfr;":[55349,56615],"jmath;":567,"jopf;":[55349,56667],"jscr;":[55349,56511],"jsercy;":1112,"jukcy;":1108,"kappa;":954,"kappav;":1008,"kcedil;":311,"kcy;":1082,"kfr;":[55349,56616],"kgreen;":312,"khcy;":1093,"kjcy;":1116,"kopf;":[55349,56668],"kscr;":[55349,56512],"lAarr;":8666,"lArr;":8656,"lAtail;":10523,"lBarr;":10510,"lE;":8806,"lEg;":10891,"lHar;":10594,"lacute;":314,"laemptyv;":10676,"lagran;":8466,"lambda;":955,"lang;":10216,"langd;":10641,"langle;":10216,"lap;":10885,laquo:171,"laquo;":171,"larr;":8592,"larrb;":8676,"larrbfs;":10527,"larrfs;":10525,"larrhk;":8617,"larrlp;":8619,"larrpl;":10553,"larrsim;":10611,"larrtl;":8610,"lat;":10923,"latail;":10521,"late;":10925,"lates;":[10925,65024],"lbarr;":10508,"lbbrk;":10098,"lbrace;":123,"lbrack;":91,"lbrke;":10635,"lbrksld;":10639,"lbrkslu;":10637,"lcaron;":318,"lcedil;":316,"lceil;":8968,"lcub;":123,"lcy;":1083,"ldca;":10550,"ldquo;":8220,"ldquor;":8222,"ldrdhar;":10599,"ldrushar;":10571,"ldsh;":8626,"le;":8804,"leftarrow;":8592,"leftarrowtail;":8610,"leftharpoondown;":8637,"leftharpoonup;":8636,"leftleftarrows;":8647,"leftrightarrow;":8596,"leftrightarrows;":8646,"leftrightharpoons;":8651,"leftrightsquigarrow;":8621,"leftthreetimes;":8907,"leg;":8922,"leq;":8804,"leqq;":8806,"leqslant;":10877,"les;":10877,"lescc;":10920,"lesdot;":10879,"lesdoto;":10881,"lesdotor;":10883,"lesg;":[8922,65024],"lesges;":10899,"lessapprox;":10885,"lessdot;":8918,"lesseqgtr;":8922,"lesseqqgtr;":10891,"lessgtr;":8822,"lesssim;":8818,"lfisht;":10620,"lfloor;":8970,"lfr;":[55349,56617],"lg;":8822,"lgE;":10897,"lhard;":8637,"lharu;":8636,"lharul;":10602,"lhblk;":9604,"ljcy;":1113,"ll;":8810,"llarr;":8647,"llcorner;":8990,"llhard;":10603,"lltri;":9722,"lmidot;":320,"lmoust;":9136,"lmoustache;":9136,"lnE;":8808,"lnap;":10889,"lnapprox;":10889,"lne;":10887,"lneq;":10887,"lneqq;":8808,"lnsim;":8934,"loang;":10220,"loarr;":8701,"lobrk;":10214,"longleftarrow;":10229,"longleftrightarrow;":10231,"longmapsto;":10236,"longrightarrow;":10230,"looparrowleft;":8619,"looparrowright;":8620,"lopar;":10629,"lopf;":[55349,56669],"loplus;":10797,"lotimes;":10804,"lowast;":8727,"lowbar;":95,"loz;":9674,"lozenge;":9674,"lozf;":10731,"lpar;":40,"lparlt;":10643,"lrarr;":8646,"lrcorner;":8991,"lrhar;":8651,"lrhard;":10605,"lrm;":8206,"lrtri;":8895,"lsaquo;":8249,"lscr;":[55349,56513],"lsh;":8624,"lsim;":8818,"lsime;":10893,"lsimg;":10895,"lsqb;":91,"lsquo;":8216,"lsquor;":8218,"lstrok;":322,lt:60,"lt;":60,"ltcc;":10918,"ltcir;":10873,"ltdot;":8918,"lthree;":8907,"ltimes;":8905,"ltlarr;":10614,"ltquest;":10875,"ltrPar;":10646,"ltri;":9667,"ltrie;":8884,"ltrif;":9666,"lurdshar;":10570,"luruhar;":10598,"lvertneqq;":[8808,65024],"lvnE;":[8808,65024],"mDDot;":8762,macr:175,"macr;":175,"male;":9794,"malt;":10016,"maltese;":10016,"map;":8614,"mapsto;":8614,"mapstodown;":8615,"mapstoleft;":8612,"mapstoup;":8613,"marker;":9646,"mcomma;":10793,"mcy;":1084,"mdash;":8212,"measuredangle;":8737,"mfr;":[55349,56618],"mho;":8487,micro:181,"micro;":181,"mid;":8739,"midast;":42,"midcir;":10992,middot:183,"middot;":183,"minus;":8722,"minusb;":8863,"minusd;":8760,"minusdu;":10794,"mlcp;":10971,"mldr;":8230,"mnplus;":8723,"models;":8871,"mopf;":[55349,56670],"mp;":8723,"mscr;":[55349,56514],"mstpos;":8766,"mu;":956,"multimap;":8888,"mumap;":8888,"nGg;":[8921,824],"nGt;":[8811,8402],"nGtv;":[8811,824],"nLeftarrow;":8653,"nLeftrightarrow;":8654,"nLl;":[8920,824],"nLt;":[8810,8402],"nLtv;":[8810,824],"nRightarrow;":8655,"nVDash;":8879,"nVdash;":8878,"nabla;":8711,"nacute;":324,"nang;":[8736,8402],"nap;":8777,"napE;":[10864,824],"napid;":[8779,824],"napos;":329,"napprox;":8777,"natur;":9838,"natural;":9838,"naturals;":8469,nbsp:160,"nbsp;":160,"nbump;":[8782,824],"nbumpe;":[8783,824],"ncap;":10819,"ncaron;":328,"ncedil;":326,"ncong;":8775,"ncongdot;":[10861,824],"ncup;":10818,"ncy;":1085,"ndash;":8211,"ne;":8800,"neArr;":8663,"nearhk;":10532,"nearr;":8599,"nearrow;":8599,"nedot;":[8784,824],"nequiv;":8802,"nesear;":10536,"nesim;":[8770,824],"nexist;":8708,"nexists;":8708,"nfr;":[55349,56619],"ngE;":[8807,824],"nge;":8817,"ngeq;":8817,"ngeqq;":[8807,824],"ngeqslant;":[10878,824],"nges;":[10878,824],"ngsim;":8821,"ngt;":8815,"ngtr;":8815,"nhArr;":8654,"nharr;":8622,"nhpar;":10994,"ni;":8715,"nis;":8956,"nisd;":8954,"niv;":8715,"njcy;":1114,"nlArr;":8653,"nlE;":[8806,824],"nlarr;":8602,"nldr;":8229,"nle;":8816,"nleftarrow;":8602,"nleftrightarrow;":8622,"nleq;":8816,"nleqq;":[8806,824],"nleqslant;":[10877,824],"nles;":[10877,824],"nless;":8814,"nlsim;":8820,"nlt;":8814,"nltri;":8938,"nltrie;":8940,"nmid;":8740,"nopf;":[55349,56671],not:172,"not;":172,"notin;":8713,"notinE;":[8953,824],"notindot;":[8949,824],"notinva;":8713,"notinvb;":8951,"notinvc;":8950,"notni;":8716,"notniva;":8716,"notnivb;":8958,"notnivc;":8957,"npar;":8742,"nparallel;":8742,"nparsl;":[11005,8421],"npart;":[8706,824],"npolint;":10772,"npr;":8832,"nprcue;":8928,"npre;":[10927,824],"nprec;":8832,"npreceq;":[10927,824],"nrArr;":8655,"nrarr;":8603,"nrarrc;":[10547,824],"nrarrw;":[8605,824],"nrightarrow;":8603,"nrtri;":8939,"nrtrie;":8941,"nsc;":8833,"nsccue;":8929,"nsce;":[10928,824],"nscr;":[55349,56515],"nshortmid;":8740,"nshortparallel;":8742,"nsim;":8769,"nsime;":8772,"nsimeq;":8772,"nsmid;":8740,"nspar;":8742,"nsqsube;":8930,"nsqsupe;":8931,"nsub;":8836,"nsubE;":[10949,824],"nsube;":8840,"nsubset;":[8834,8402],"nsubseteq;":8840,"nsubseteqq;":[10949,824],"nsucc;":8833,"nsucceq;":[10928,824],"nsup;":8837,"nsupE;":[10950,824],"nsupe;":8841,"nsupset;":[8835,8402],"nsupseteq;":8841,"nsupseteqq;":[10950,824],"ntgl;":8825,ntilde:241,"ntilde;":241,"ntlg;":8824,"ntriangleleft;":8938,"ntrianglelefteq;":8940,"ntriangleright;":8939,"ntrianglerighteq;":8941,"nu;":957,"num;":35,"numero;":8470,"numsp;":8199,"nvDash;":8877,"nvHarr;":10500,"nvap;":[8781,8402],"nvdash;":8876,"nvge;":[8805,8402],"nvgt;":[62,8402],"nvinfin;":10718,"nvlArr;":10498,"nvle;":[8804,8402],"nvlt;":[60,8402],"nvltrie;":[8884,8402],"nvrArr;":10499,"nvrtrie;":[8885,8402],"nvsim;":[8764,8402],"nwArr;":8662,"nwarhk;":10531,"nwarr;":8598,"nwarrow;":8598,"nwnear;":10535,"oS;":9416,oacute:243,"oacute;":243,"oast;":8859,"ocir;":8858,ocirc:244,"ocirc;":244,"ocy;":1086,"odash;":8861,"odblac;":337,"odiv;":10808,"odot;":8857,"odsold;":10684,"oelig;":339,"ofcir;":10687,"ofr;":[55349,56620],"ogon;":731,ograve:242,"ograve;":242,"ogt;":10689,"ohbar;":10677,"ohm;":937,"oint;":8750,"olarr;":8634,"olcir;":10686,"olcross;":10683,"oline;":8254,"olt;":10688,"omacr;":333,"omega;":969,"omicron;":959,"omid;":10678,"ominus;":8854,"oopf;":[55349,56672],"opar;":10679,"operp;":10681,"oplus;":8853,"or;":8744,"orarr;":8635,"ord;":10845,"order;":8500,"orderof;":8500,ordf:170,"ordf;":170,ordm:186,"ordm;":186,"origof;":8886,"oror;":10838,"orslope;":10839,"orv;":10843,"oscr;":8500,oslash:248,"oslash;":248,"osol;":8856,otilde:245,"otilde;":245,"otimes;":8855,"otimesas;":10806,ouml:246,"ouml;":246,"ovbar;":9021,"par;":8741,para:182,"para;":182,"parallel;":8741,"parsim;":10995,"parsl;":11005,"part;":8706,"pcy;":1087,"percnt;":37,"period;":46,"permil;":8240,"perp;":8869,"pertenk;":8241,"pfr;":[55349,56621],"phi;":966,"phiv;":981,"phmmat;":8499,"phone;":9742,"pi;":960,"pitchfork;":8916,"piv;":982,"planck;":8463,"planckh;":8462,"plankv;":8463,"plus;":43,"plusacir;":10787,"plusb;":8862,"pluscir;":10786,"plusdo;":8724,"plusdu;":10789,"pluse;":10866,plusmn:177,"plusmn;":177,"plussim;":10790,"plustwo;":10791,"pm;":177,"pointint;":10773,"popf;":[55349,56673],pound:163,"pound;":163,"pr;":8826,"prE;":10931,"prap;":10935,"prcue;":8828,"pre;":10927,"prec;":8826,"precapprox;":10935,"preccurlyeq;":8828,"preceq;":10927,"precnapprox;":10937,"precneqq;":10933,"precnsim;":8936,"precsim;":8830,"prime;":8242,"primes;":8473,"prnE;":10933,"prnap;":10937,"prnsim;":8936,"prod;":8719,"profalar;":9006,"profline;":8978,"profsurf;":8979,"prop;":8733,"propto;":8733,"prsim;":8830,"prurel;":8880,"pscr;":[55349,56517],"psi;":968,"puncsp;":8200,"qfr;":[55349,56622],"qint;":10764,"qopf;":[55349,56674],"qprime;":8279,"qscr;":[55349,56518],"quaternions;":8461,"quatint;":10774,"quest;":63,"questeq;":8799,quot:34,"quot;":34,"rAarr;":8667,"rArr;":8658,"rAtail;":10524,"rBarr;":10511,"rHar;":10596,"race;":[8765,817],"racute;":341,"radic;":8730,"raemptyv;":10675,"rang;":10217,"rangd;":10642,"range;":10661,"rangle;":10217,raquo:187,"raquo;":187,"rarr;":8594,"rarrap;":10613,"rarrb;":8677,"rarrbfs;":10528,"rarrc;":10547,"rarrfs;":10526,"rarrhk;":8618,"rarrlp;":8620,"rarrpl;":10565,"rarrsim;":10612,"rarrtl;":8611,"rarrw;":8605,"ratail;":10522,"ratio;":8758,"rationals;":8474,"rbarr;":10509,"rbbrk;":10099,"rbrace;":125,"rbrack;":93,"rbrke;":10636,"rbrksld;":10638,"rbrkslu;":10640,"rcaron;":345,"rcedil;":343,"rceil;":8969,"rcub;":125,"rcy;":1088,"rdca;":10551,"rdldhar;":10601,"rdquo;":8221,"rdquor;":8221,"rdsh;":8627,"real;":8476,"realine;":8475,"realpart;":8476,"reals;":8477,"rect;":9645,reg:174,"reg;":174,"rfisht;":10621,"rfloor;":8971,"rfr;":[55349,56623],"rhard;":8641,"rharu;":8640,"rharul;":10604,"rho;":961,"rhov;":1009,"rightarrow;":8594,"rightarrowtail;":8611,"rightharpoondown;":8641,"rightharpoonup;":8640,"rightleftarrows;":8644,"rightleftharpoons;":8652,"rightrightarrows;":8649,"rightsquigarrow;":8605,"rightthreetimes;":8908,"ring;":730,"risingdotseq;":8787,"rlarr;":8644,"rlhar;":8652,"rlm;":8207,"rmoust;":9137,"rmoustache;":9137,"rnmid;":10990,"roang;":10221,"roarr;":8702,"robrk;":10215,"ropar;":10630,"ropf;":[55349,56675],"roplus;":10798,"rotimes;":10805,"rpar;":41,"rpargt;":10644,"rppolint;":10770,"rrarr;":8649,"rsaquo;":8250,"rscr;":[55349,56519],"rsh;":8625,"rsqb;":93,"rsquo;":8217,"rsquor;":8217,"rthree;":8908,"rtimes;":8906,"rtri;":9657,"rtrie;":8885,"rtrif;":9656,"rtriltri;":10702,"ruluhar;":10600,"rx;":8478,"sacute;":347,"sbquo;":8218,"sc;":8827,"scE;":10932,"scap;":10936,"scaron;":353,"sccue;":8829,"sce;":10928,"scedil;":351,"scirc;":349,"scnE;":10934,"scnap;":10938,"scnsim;":8937,"scpolint;":10771,"scsim;":8831,"scy;":1089,"sdot;":8901,"sdotb;":8865,"sdote;":10854,"seArr;":8664,"searhk;":10533,"searr;":8600,"searrow;":8600,sect:167,"sect;":167,"semi;":59,"seswar;":10537,"setminus;":8726,"setmn;":8726,"sext;":10038,"sfr;":[55349,56624],"sfrown;":8994,"sharp;":9839,"shchcy;":1097,"shcy;":1096,"shortmid;":8739,"shortparallel;":8741,shy:173,"shy;":173,"sigma;":963,"sigmaf;":962,"sigmav;":962,"sim;":8764,"simdot;":10858,"sime;":8771,"simeq;":8771,"simg;":10910,"simgE;":10912,"siml;":10909,"simlE;":10911,"simne;":8774,"simplus;":10788,"simrarr;":10610,"slarr;":8592,"smallsetminus;":8726,"smashp;":10803,"smeparsl;":10724,"smid;":8739,"smile;":8995,"smt;":10922,"smte;":10924,"smtes;":[10924,65024],"softcy;":1100,"sol;":47,"solb;":10692,"solbar;":9023,"sopf;":[55349,56676],"spades;":9824,"spadesuit;":9824,"spar;":8741,"sqcap;":8851,"sqcaps;":[8851,65024],"sqcup;":8852,"sqcups;":[8852,65024],"sqsub;":8847,"sqsube;":8849,"sqsubset;":8847,"sqsubseteq;":8849,"sqsup;":8848,"sqsupe;":8850,"sqsupset;":8848,"sqsupseteq;":8850,"squ;":9633,"square;":9633,"squarf;":9642,"squf;":9642,"srarr;":8594,"sscr;":[55349,56520],"ssetmn;":8726,"ssmile;":8995,"sstarf;":8902,"star;":9734,"starf;":9733,"straightepsilon;":1013,"straightphi;":981,"strns;":175,"sub;":8834,"subE;":10949,"subdot;":10941,"sube;":8838,"subedot;":10947,"submult;":10945,"subnE;":10955,"subne;":8842,"subplus;":10943,"subrarr;":10617,"subset;":8834,"subseteq;":8838,"subseteqq;":10949,"subsetneq;":8842,"subsetneqq;":10955,"subsim;":10951,"subsub;":10965,"subsup;":10963,"succ;":8827,"succapprox;":10936,"succcurlyeq;":8829,"succeq;":10928,"succnapprox;":10938,"succneqq;":10934,"succnsim;":8937,"succsim;":8831,"sum;":8721,"sung;":9834,sup1:185,"sup1;":185,sup2:178,"sup2;":178,sup3:179,"sup3;":179,"sup;":8835,"supE;":10950,"supdot;":10942,"supdsub;":10968,"supe;":8839,"supedot;":10948,"suphsol;":10185,"suphsub;":10967,"suplarr;":10619,"supmult;":10946,"supnE;":10956,"supne;":8843,"supplus;":10944,"supset;":8835,"supseteq;":8839,"supseteqq;":10950,"supsetneq;":8843,"supsetneqq;":10956,"supsim;":10952,"supsub;":10964,"supsup;":10966,"swArr;":8665,"swarhk;":10534,"swarr;":8601,"swarrow;":8601,"swnwar;":10538,szlig:223,"szlig;":223,"target;":8982,"tau;":964,"tbrk;":9140,"tcaron;":357,"tcedil;":355,"tcy;":1090,"tdot;":8411,"telrec;":8981,"tfr;":[55349,56625],"there4;":8756,"therefore;":8756,"theta;":952,"thetasym;":977,"thetav;":977,"thickapprox;":8776,"thicksim;":8764,"thinsp;":8201,"thkap;":8776,"thksim;":8764,thorn:254,"thorn;":254,"tilde;":732,times:215,"times;":215,"timesb;":8864,"timesbar;":10801,"timesd;":10800,"tint;":8749,"toea;":10536,"top;":8868,"topbot;":9014,"topcir;":10993,"topf;":[55349,56677],"topfork;":10970,"tosa;":10537,"tprime;":8244,"trade;":8482,"triangle;":9653,"triangledown;":9663,"triangleleft;":9667,"trianglelefteq;":8884,"triangleq;":8796,"triangleright;":9657,"trianglerighteq;":8885,"tridot;":9708,"trie;":8796,"triminus;":10810,"triplus;":10809,"trisb;":10701,"tritime;":10811,"trpezium;":9186,"tscr;":[55349,56521],"tscy;":1094,"tshcy;":1115,"tstrok;":359,"twixt;":8812,"twoheadleftarrow;":8606,"twoheadrightarrow;":8608,"uArr;":8657,"uHar;":10595,uacute:250,"uacute;":250,"uarr;":8593,"ubrcy;":1118,"ubreve;":365,ucirc:251,"ucirc;":251,"ucy;":1091,"udarr;":8645,"udblac;":369,"udhar;":10606,"ufisht;":10622,"ufr;":[55349,56626],ugrave:249,"ugrave;":249,"uharl;":8639,"uharr;":8638,"uhblk;":9600,"ulcorn;":8988,"ulcorner;":8988,"ulcrop;":8975,"ultri;":9720,"umacr;":363,uml:168,"uml;":168,"uogon;":371,"uopf;":[55349,56678],"uparrow;":8593,"updownarrow;":8597,"upharpoonleft;":8639,"upharpoonright;":8638,"uplus;":8846,"upsi;":965,"upsih;":978,"upsilon;":965,"upuparrows;":8648,"urcorn;":8989,"urcorner;":8989,"urcrop;":8974,"uring;":367,"urtri;":9721,"uscr;":[55349,56522],"utdot;":8944,"utilde;":361,"utri;":9653,"utrif;":9652,"uuarr;":8648,uuml:252,"uuml;":252,"uwangle;":10663,"vArr;":8661,"vBar;":10984,"vBarv;":10985,"vDash;":8872,"vangrt;":10652,"varepsilon;":1013,"varkappa;":1008,"varnothing;":8709,"varphi;":981,"varpi;":982,"varpropto;":8733,"varr;":8597,"varrho;":1009,"varsigma;":962,"varsubsetneq;":[8842,65024],"varsubsetneqq;":[10955,65024],"varsupsetneq;":[8843,65024],"varsupsetneqq;":[10956,65024],"vartheta;":977,"vartriangleleft;":8882,"vartriangleright;":8883,"vcy;":1074,"vdash;":8866,"vee;":8744,"veebar;":8891,"veeeq;":8794,"vellip;":8942,"verbar;":124,"vert;":124,"vfr;":[55349,56627],"vltri;":8882,"vnsub;":[8834,8402],"vnsup;":[8835,8402],"vopf;":[55349,56679],"vprop;":8733,"vrtri;":8883,"vscr;":[55349,56523],"vsubnE;":[10955,65024],"vsubne;":[8842,65024],"vsupnE;":[10956,65024],"vsupne;":[8843,65024],"vzigzag;":10650,"wcirc;":373,"wedbar;":10847,"wedge;":8743,"wedgeq;":8793,"weierp;":8472,"wfr;":[55349,56628],"wopf;":[55349,56680],"wp;":8472,"wr;":8768,"wreath;":8768,"wscr;":[55349,56524],"xcap;":8898,"xcirc;":9711,"xcup;":8899,"xdtri;":9661,"xfr;":[55349,56629],"xhArr;":10234,"xharr;":10231,"xi;":958,"xlArr;":10232,"xlarr;":10229,"xmap;":10236,"xnis;":8955,"xodot;":10752,"xopf;":[55349,56681],"xoplus;":10753,"xotime;":10754,"xrArr;":10233,"xrarr;":10230,"xscr;":[55349,56525],"xsqcup;":10758,"xuplus;":10756,"xutri;":9651,"xvee;":8897,"xwedge;":8896,yacute:253,"yacute;":253,"yacy;":1103,"ycirc;":375,"ycy;":1099,yen:165,"yen;":165,"yfr;":[55349,56630],"yicy;":1111,"yopf;":[55349,56682],"yscr;":[55349,56526],"yucy;":1102,yuml:255,"yuml;":255,"zacute;":378,"zcaron;":382,"zcy;":1079,"zdot;":380,"zeetrf;":8488,"zeta;":950,"zfr;":[55349,56631],"zhcy;":1078,"zigrarr;":8669,"zopf;":[55349,56683],"zscr;":[55349,56527],"zwj;":8205,"zwnj;":8204},M=/(A(?:Elig;?|MP;?|acute;?|breve;|c(?:irc;?|y;)|fr;|grave;?|lpha;|macr;|nd;|o(?:gon;|pf;)|pplyFunction;|ring;?|s(?:cr;|sign;)|tilde;?|uml;?)|B(?:a(?:ckslash;|r(?:v;|wed;))|cy;|e(?:cause;|rnoullis;|ta;)|fr;|opf;|reve;|scr;|umpeq;)|C(?:Hcy;|OPY;?|a(?:cute;|p(?:;|italDifferentialD;)|yleys;)|c(?:aron;|edil;?|irc;|onint;)|dot;|e(?:dilla;|nterDot;)|fr;|hi;|ircle(?:Dot;|Minus;|Plus;|Times;)|lo(?:ckwiseContourIntegral;|seCurly(?:DoubleQuote;|Quote;))|o(?:lon(?:;|e;)|n(?:gruent;|int;|tourIntegral;)|p(?:f;|roduct;)|unterClockwiseContourIntegral;)|ross;|scr;|up(?:;|Cap;))|D(?:D(?:;|otrahd;)|Jcy;|Scy;|Zcy;|a(?:gger;|rr;|shv;)|c(?:aron;|y;)|el(?:;|ta;)|fr;|i(?:a(?:critical(?:Acute;|Do(?:t;|ubleAcute;)|Grave;|Tilde;)|mond;)|fferentialD;)|o(?:pf;|t(?:;|Dot;|Equal;)|uble(?:ContourIntegral;|Do(?:t;|wnArrow;)|L(?:eft(?:Arrow;|RightArrow;|Tee;)|ong(?:Left(?:Arrow;|RightArrow;)|RightArrow;))|Right(?:Arrow;|Tee;)|Up(?:Arrow;|DownArrow;)|VerticalBar;)|wn(?:Arrow(?:;|Bar;|UpArrow;)|Breve;|Left(?:RightVector;|TeeVector;|Vector(?:;|Bar;))|Right(?:TeeVector;|Vector(?:;|Bar;))|Tee(?:;|Arrow;)|arrow;))|s(?:cr;|trok;))|E(?:NG;|TH;?|acute;?|c(?:aron;|irc;?|y;)|dot;|fr;|grave;?|lement;|m(?:acr;|pty(?:SmallSquare;|VerySmallSquare;))|o(?:gon;|pf;)|psilon;|qu(?:al(?:;|Tilde;)|ilibrium;)|s(?:cr;|im;)|ta;|uml;?|x(?:ists;|ponentialE;))|F(?:cy;|fr;|illed(?:SmallSquare;|VerySmallSquare;)|o(?:pf;|rAll;|uriertrf;)|scr;)|G(?:Jcy;|T;?|amma(?:;|d;)|breve;|c(?:edil;|irc;|y;)|dot;|fr;|g;|opf;|reater(?:Equal(?:;|Less;)|FullEqual;|Greater;|Less;|SlantEqual;|Tilde;)|scr;|t;)|H(?:ARDcy;|a(?:cek;|t;)|circ;|fr;|ilbertSpace;|o(?:pf;|rizontalLine;)|s(?:cr;|trok;)|ump(?:DownHump;|Equal;))|I(?:Ecy;|Jlig;|Ocy;|acute;?|c(?:irc;?|y;)|dot;|fr;|grave;?|m(?:;|a(?:cr;|ginaryI;)|plies;)|n(?:t(?:;|e(?:gral;|rsection;))|visible(?:Comma;|Times;))|o(?:gon;|pf;|ta;)|scr;|tilde;|u(?:kcy;|ml;?))|J(?:c(?:irc;|y;)|fr;|opf;|s(?:cr;|ercy;)|ukcy;)|K(?:Hcy;|Jcy;|appa;|c(?:edil;|y;)|fr;|opf;|scr;)|L(?:Jcy;|T;?|a(?:cute;|mbda;|ng;|placetrf;|rr;)|c(?:aron;|edil;|y;)|e(?:ft(?:A(?:ngleBracket;|rrow(?:;|Bar;|RightArrow;))|Ceiling;|Do(?:ubleBracket;|wn(?:TeeVector;|Vector(?:;|Bar;)))|Floor;|Right(?:Arrow;|Vector;)|T(?:ee(?:;|Arrow;|Vector;)|riangle(?:;|Bar;|Equal;))|Up(?:DownVector;|TeeVector;|Vector(?:;|Bar;))|Vector(?:;|Bar;)|arrow;|rightarrow;)|ss(?:EqualGreater;|FullEqual;|Greater;|Less;|SlantEqual;|Tilde;))|fr;|l(?:;|eftarrow;)|midot;|o(?:ng(?:Left(?:Arrow;|RightArrow;)|RightArrow;|left(?:arrow;|rightarrow;)|rightarrow;)|pf;|wer(?:LeftArrow;|RightArrow;))|s(?:cr;|h;|trok;)|t;)|M(?:ap;|cy;|e(?:diumSpace;|llintrf;)|fr;|inusPlus;|opf;|scr;|u;)|N(?:Jcy;|acute;|c(?:aron;|edil;|y;)|e(?:gative(?:MediumSpace;|Thi(?:ckSpace;|nSpace;)|VeryThinSpace;)|sted(?:GreaterGreater;|LessLess;)|wLine;)|fr;|o(?:Break;|nBreakingSpace;|pf;|t(?:;|C(?:ongruent;|upCap;)|DoubleVerticalBar;|E(?:lement;|qual(?:;|Tilde;)|xists;)|Greater(?:;|Equal;|FullEqual;|Greater;|Less;|SlantEqual;|Tilde;)|Hump(?:DownHump;|Equal;)|Le(?:ftTriangle(?:;|Bar;|Equal;)|ss(?:;|Equal;|Greater;|Less;|SlantEqual;|Tilde;))|Nested(?:GreaterGreater;|LessLess;)|Precedes(?:;|Equal;|SlantEqual;)|R(?:everseElement;|ightTriangle(?:;|Bar;|Equal;))|S(?:quareSu(?:bset(?:;|Equal;)|perset(?:;|Equal;))|u(?:bset(?:;|Equal;)|cceeds(?:;|Equal;|SlantEqual;|Tilde;)|perset(?:;|Equal;)))|Tilde(?:;|Equal;|FullEqual;|Tilde;)|VerticalBar;))|scr;|tilde;?|u;)|O(?:Elig;|acute;?|c(?:irc;?|y;)|dblac;|fr;|grave;?|m(?:acr;|ega;|icron;)|opf;|penCurly(?:DoubleQuote;|Quote;)|r;|s(?:cr;|lash;?)|ti(?:lde;?|mes;)|uml;?|ver(?:B(?:ar;|rac(?:e;|ket;))|Parenthesis;))|P(?:artialD;|cy;|fr;|hi;|i;|lusMinus;|o(?:incareplane;|pf;)|r(?:;|ecedes(?:;|Equal;|SlantEqual;|Tilde;)|ime;|o(?:duct;|portion(?:;|al;)))|s(?:cr;|i;))|Q(?:UOT;?|fr;|opf;|scr;)|R(?:Barr;|EG;?|a(?:cute;|ng;|rr(?:;|tl;))|c(?:aron;|edil;|y;)|e(?:;|verse(?:E(?:lement;|quilibrium;)|UpEquilibrium;))|fr;|ho;|ight(?:A(?:ngleBracket;|rrow(?:;|Bar;|LeftArrow;))|Ceiling;|Do(?:ubleBracket;|wn(?:TeeVector;|Vector(?:;|Bar;)))|Floor;|T(?:ee(?:;|Arrow;|Vector;)|riangle(?:;|Bar;|Equal;))|Up(?:DownVector;|TeeVector;|Vector(?:;|Bar;))|Vector(?:;|Bar;)|arrow;)|o(?:pf;|undImplies;)|rightarrow;|s(?:cr;|h;)|uleDelayed;)|S(?:H(?:CHcy;|cy;)|OFTcy;|acute;|c(?:;|aron;|edil;|irc;|y;)|fr;|hort(?:DownArrow;|LeftArrow;|RightArrow;|UpArrow;)|igma;|mallCircle;|opf;|q(?:rt;|uare(?:;|Intersection;|Su(?:bset(?:;|Equal;)|perset(?:;|Equal;))|Union;))|scr;|tar;|u(?:b(?:;|set(?:;|Equal;))|c(?:ceeds(?:;|Equal;|SlantEqual;|Tilde;)|hThat;)|m;|p(?:;|erset(?:;|Equal;)|set;)))|T(?:HORN;?|RADE;|S(?:Hcy;|cy;)|a(?:b;|u;)|c(?:aron;|edil;|y;)|fr;|h(?:e(?:refore;|ta;)|i(?:ckSpace;|nSpace;))|ilde(?:;|Equal;|FullEqual;|Tilde;)|opf;|ripleDot;|s(?:cr;|trok;))|U(?:a(?:cute;?|rr(?:;|ocir;))|br(?:cy;|eve;)|c(?:irc;?|y;)|dblac;|fr;|grave;?|macr;|n(?:der(?:B(?:ar;|rac(?:e;|ket;))|Parenthesis;)|ion(?:;|Plus;))|o(?:gon;|pf;)|p(?:Arrow(?:;|Bar;|DownArrow;)|DownArrow;|Equilibrium;|Tee(?:;|Arrow;)|arrow;|downarrow;|per(?:LeftArrow;|RightArrow;)|si(?:;|lon;))|ring;|scr;|tilde;|uml;?)|V(?:Dash;|bar;|cy;|dash(?:;|l;)|e(?:e;|r(?:bar;|t(?:;|ical(?:Bar;|Line;|Separator;|Tilde;))|yThinSpace;))|fr;|opf;|scr;|vdash;)|W(?:circ;|edge;|fr;|opf;|scr;)|X(?:fr;|i;|opf;|scr;)|Y(?:Acy;|Icy;|Ucy;|acute;?|c(?:irc;|y;)|fr;|opf;|scr;|uml;)|Z(?:Hcy;|acute;|c(?:aron;|y;)|dot;|e(?:roWidthSpace;|ta;)|fr;|opf;|scr;)|a(?:acute;?|breve;|c(?:;|E;|d;|irc;?|ute;?|y;)|elig;?|f(?:;|r;)|grave;?|l(?:e(?:fsym;|ph;)|pha;)|m(?:a(?:cr;|lg;)|p;?)|n(?:d(?:;|and;|d;|slope;|v;)|g(?:;|e;|le;|msd(?:;|a(?:a;|b;|c;|d;|e;|f;|g;|h;))|rt(?:;|vb(?:;|d;))|s(?:ph;|t;)|zarr;))|o(?:gon;|pf;)|p(?:;|E;|acir;|e;|id;|os;|prox(?:;|eq;))|ring;?|s(?:cr;|t;|ymp(?:;|eq;))|tilde;?|uml;?|w(?:conint;|int;))|b(?:Not;|a(?:ck(?:cong;|epsilon;|prime;|sim(?:;|eq;))|r(?:vee;|wed(?:;|ge;)))|brk(?:;|tbrk;)|c(?:ong;|y;)|dquo;|e(?:caus(?:;|e;)|mptyv;|psi;|rnou;|t(?:a;|h;|ween;))|fr;|ig(?:c(?:ap;|irc;|up;)|o(?:dot;|plus;|times;)|s(?:qcup;|tar;)|triangle(?:down;|up;)|uplus;|vee;|wedge;)|karow;|l(?:a(?:ck(?:lozenge;|square;|triangle(?:;|down;|left;|right;))|nk;)|k(?:1(?:2;|4;)|34;)|ock;)|n(?:e(?:;|quiv;)|ot;)|o(?:pf;|t(?:;|tom;)|wtie;|x(?:D(?:L;|R;|l;|r;)|H(?:;|D;|U;|d;|u;)|U(?:L;|R;|l;|r;)|V(?:;|H;|L;|R;|h;|l;|r;)|box;|d(?:L;|R;|l;|r;)|h(?:;|D;|U;|d;|u;)|minus;|plus;|times;|u(?:L;|R;|l;|r;)|v(?:;|H;|L;|R;|h;|l;|r;)))|prime;|r(?:eve;|vbar;?)|s(?:cr;|emi;|im(?:;|e;)|ol(?:;|b;|hsub;))|u(?:ll(?:;|et;)|mp(?:;|E;|e(?:;|q;))))|c(?:a(?:cute;|p(?:;|and;|brcup;|c(?:ap;|up;)|dot;|s;)|r(?:et;|on;))|c(?:a(?:ps;|ron;)|edil;?|irc;|ups(?:;|sm;))|dot;|e(?:dil;?|mptyv;|nt(?:;|erdot;|))|fr;|h(?:cy;|eck(?:;|mark;)|i;)|ir(?:;|E;|c(?:;|eq;|le(?:arrow(?:left;|right;)|d(?:R;|S;|ast;|circ;|dash;)))|e;|fnint;|mid;|scir;)|lubs(?:;|uit;)|o(?:lon(?:;|e(?:;|q;))|m(?:ma(?:;|t;)|p(?:;|fn;|le(?:ment;|xes;)))|n(?:g(?:;|dot;)|int;)|p(?:f;|rod;|y(?:;|sr;|)))|r(?:arr;|oss;)|s(?:cr;|u(?:b(?:;|e;)|p(?:;|e;)))|tdot;|u(?:darr(?:l;|r;)|e(?:pr;|sc;)|larr(?:;|p;)|p(?:;|brcap;|c(?:ap;|up;)|dot;|or;|s;)|r(?:arr(?:;|m;)|ly(?:eq(?:prec;|succ;)|vee;|wedge;)|ren;?|vearrow(?:left;|right;))|vee;|wed;)|w(?:conint;|int;)|ylcty;)|d(?:Arr;|Har;|a(?:gger;|leth;|rr;|sh(?:;|v;))|b(?:karow;|lac;)|c(?:aron;|y;)|d(?:;|a(?:gger;|rr;)|otseq;)|e(?:g;?|lta;|mptyv;)|f(?:isht;|r;)|har(?:l;|r;)|i(?:am(?:;|ond(?:;|suit;)|s;)|e;|gamma;|sin;|v(?:;|ide(?:;|ontimes;|)|onx;))|jcy;|lc(?:orn;|rop;)|o(?:llar;|pf;|t(?:;|eq(?:;|dot;)|minus;|plus;|square;)|ublebarwedge;|wn(?:arrow;|downarrows;|harpoon(?:left;|right;)))|r(?:bkarow;|c(?:orn;|rop;))|s(?:c(?:r;|y;)|ol;|trok;)|t(?:dot;|ri(?:;|f;))|u(?:arr;|har;)|wangle;|z(?:cy;|igrarr;))|e(?:D(?:Dot;|ot;)|a(?:cute;?|ster;)|c(?:aron;|ir(?:;|c;?)|olon;|y;)|dot;|e;|f(?:Dot;|r;)|g(?:;|rave;?|s(?:;|dot;))|l(?:;|inters;|l;|s(?:;|dot;))|m(?:acr;|pty(?:;|set;|v;)|sp(?:1(?:3;|4;)|;))|n(?:g;|sp;)|o(?:gon;|pf;)|p(?:ar(?:;|sl;)|lus;|si(?:;|lon;|v;))|q(?:c(?:irc;|olon;)|s(?:im;|lant(?:gtr;|less;))|u(?:als;|est;|iv(?:;|DD;))|vparsl;)|r(?:Dot;|arr;)|s(?:cr;|dot;|im;)|t(?:a;|h;?)|u(?:ml;?|ro;)|x(?:cl;|ist;|p(?:ectation;|onentiale;)))|f(?:allingdotseq;|cy;|emale;|f(?:ilig;|l(?:ig;|lig;)|r;)|ilig;|jlig;|l(?:at;|lig;|tns;)|nof;|o(?:pf;|r(?:all;|k(?:;|v;)))|partint;|r(?:a(?:c(?:1(?:2;?|3;|4;?|5;|6;|8;)|2(?:3;|5;)|3(?:4;?|5;|8;)|45;|5(?:6;|8;)|78;)|sl;)|own;)|scr;)|g(?:E(?:;|l;)|a(?:cute;|mma(?:;|d;)|p;)|breve;|c(?:irc;|y;)|dot;|e(?:;|l;|q(?:;|q;|slant;)|s(?:;|cc;|dot(?:;|o(?:;|l;))|l(?:;|es;)))|fr;|g(?:;|g;)|imel;|jcy;|l(?:;|E;|a;|j;)|n(?:E;|ap(?:;|prox;)|e(?:;|q(?:;|q;))|sim;)|opf;|rave;|s(?:cr;|im(?:;|e;|l;))|t(?:;|c(?:c;|ir;)|dot;|lPar;|quest;|r(?:a(?:pprox;|rr;)|dot;|eq(?:less;|qless;)|less;|sim;)|)|v(?:ertneqq;|nE;))|h(?:Arr;|a(?:irsp;|lf;|milt;|r(?:dcy;|r(?:;|cir;|w;)))|bar;|circ;|e(?:arts(?:;|uit;)|llip;|rcon;)|fr;|ks(?:earow;|warow;)|o(?:arr;|mtht;|ok(?:leftarrow;|rightarrow;)|pf;|rbar;)|s(?:cr;|lash;|trok;)|y(?:bull;|phen;))|i(?:acute;?|c(?:;|irc;?|y;)|e(?:cy;|xcl;?)|f(?:f;|r;)|grave;?|i(?:;|i(?:int;|nt;)|nfin;|ota;)|jlig;|m(?:a(?:cr;|g(?:e;|line;|part;)|th;)|of;|ped;)|n(?:;|care;|fin(?:;|tie;)|odot;|t(?:;|cal;|e(?:gers;|rcal;)|larhk;|prod;))|o(?:cy;|gon;|pf;|ta;)|prod;|quest;?|s(?:cr;|in(?:;|E;|dot;|s(?:;|v;)|v;))|t(?:;|ilde;)|u(?:kcy;|ml;?))|j(?:c(?:irc;|y;)|fr;|math;|opf;|s(?:cr;|ercy;)|ukcy;)|k(?:appa(?:;|v;)|c(?:edil;|y;)|fr;|green;|hcy;|jcy;|opf;|scr;)|l(?:A(?:arr;|rr;|tail;)|Barr;|E(?:;|g;)|Har;|a(?:cute;|emptyv;|gran;|mbda;|ng(?:;|d;|le;)|p;|quo;?|rr(?:;|b(?:;|fs;)|fs;|hk;|lp;|pl;|sim;|tl;)|t(?:;|ail;|e(?:;|s;)))|b(?:arr;|brk;|r(?:ac(?:e;|k;)|k(?:e;|sl(?:d;|u;))))|c(?:aron;|e(?:dil;|il;)|ub;|y;)|d(?:ca;|quo(?:;|r;)|r(?:dhar;|ushar;)|sh;)|e(?:;|ft(?:arrow(?:;|tail;)|harpoon(?:down;|up;)|leftarrows;|right(?:arrow(?:;|s;)|harpoons;|squigarrow;)|threetimes;)|g;|q(?:;|q;|slant;)|s(?:;|cc;|dot(?:;|o(?:;|r;))|g(?:;|es;)|s(?:approx;|dot;|eq(?:gtr;|qgtr;)|gtr;|sim;)))|f(?:isht;|loor;|r;)|g(?:;|E;)|h(?:ar(?:d;|u(?:;|l;))|blk;)|jcy;|l(?:;|arr;|corner;|hard;|tri;)|m(?:idot;|oust(?:;|ache;))|n(?:E;|ap(?:;|prox;)|e(?:;|q(?:;|q;))|sim;)|o(?:a(?:ng;|rr;)|brk;|ng(?:left(?:arrow;|rightarrow;)|mapsto;|rightarrow;)|oparrow(?:left;|right;)|p(?:ar;|f;|lus;)|times;|w(?:ast;|bar;)|z(?:;|enge;|f;))|par(?:;|lt;)|r(?:arr;|corner;|har(?:;|d;)|m;|tri;)|s(?:aquo;|cr;|h;|im(?:;|e;|g;)|q(?:b;|uo(?:;|r;))|trok;)|t(?:;|c(?:c;|ir;)|dot;|hree;|imes;|larr;|quest;|r(?:Par;|i(?:;|e;|f;))|)|ur(?:dshar;|uhar;)|v(?:ertneqq;|nE;))|m(?:DDot;|a(?:cr;?|l(?:e;|t(?:;|ese;))|p(?:;|sto(?:;|down;|left;|up;))|rker;)|c(?:omma;|y;)|dash;|easuredangle;|fr;|ho;|i(?:cro;?|d(?:;|ast;|cir;|dot;?)|nus(?:;|b;|d(?:;|u;)))|l(?:cp;|dr;)|nplus;|o(?:dels;|pf;)|p;|s(?:cr;|tpos;)|u(?:;|ltimap;|map;))|n(?:G(?:g;|t(?:;|v;))|L(?:eft(?:arrow;|rightarrow;)|l;|t(?:;|v;))|Rightarrow;|V(?:Dash;|dash;)|a(?:bla;|cute;|ng;|p(?:;|E;|id;|os;|prox;)|tur(?:;|al(?:;|s;)))|b(?:sp;?|ump(?:;|e;))|c(?:a(?:p;|ron;)|edil;|ong(?:;|dot;)|up;|y;)|dash;|e(?:;|Arr;|ar(?:hk;|r(?:;|ow;))|dot;|quiv;|s(?:ear;|im;)|xist(?:;|s;))|fr;|g(?:E;|e(?:;|q(?:;|q;|slant;)|s;)|sim;|t(?:;|r;))|h(?:Arr;|arr;|par;)|i(?:;|s(?:;|d;)|v;)|jcy;|l(?:Arr;|E;|arr;|dr;|e(?:;|ft(?:arrow;|rightarrow;)|q(?:;|q;|slant;)|s(?:;|s;))|sim;|t(?:;|ri(?:;|e;)))|mid;|o(?:pf;|t(?:;|in(?:;|E;|dot;|v(?:a;|b;|c;))|ni(?:;|v(?:a;|b;|c;))|))|p(?:ar(?:;|allel;|sl;|t;)|olint;|r(?:;|cue;|e(?:;|c(?:;|eq;))))|r(?:Arr;|arr(?:;|c;|w;)|ightarrow;|tri(?:;|e;))|s(?:c(?:;|cue;|e;|r;)|hort(?:mid;|parallel;)|im(?:;|e(?:;|q;))|mid;|par;|qsu(?:be;|pe;)|u(?:b(?:;|E;|e;|set(?:;|eq(?:;|q;)))|cc(?:;|eq;)|p(?:;|E;|e;|set(?:;|eq(?:;|q;)))))|t(?:gl;|ilde;?|lg;|riangle(?:left(?:;|eq;)|right(?:;|eq;)))|u(?:;|m(?:;|ero;|sp;))|v(?:Dash;|Harr;|ap;|dash;|g(?:e;|t;)|infin;|l(?:Arr;|e;|t(?:;|rie;))|r(?:Arr;|trie;)|sim;)|w(?:Arr;|ar(?:hk;|r(?:;|ow;))|near;))|o(?:S;|a(?:cute;?|st;)|c(?:ir(?:;|c;?)|y;)|d(?:ash;|blac;|iv;|ot;|sold;)|elig;|f(?:cir;|r;)|g(?:on;|rave;?|t;)|h(?:bar;|m;)|int;|l(?:arr;|c(?:ir;|ross;)|ine;|t;)|m(?:acr;|ega;|i(?:cron;|d;|nus;))|opf;|p(?:ar;|erp;|lus;)|r(?:;|arr;|d(?:;|er(?:;|of;)|f;?|m;?)|igof;|or;|slope;|v;)|s(?:cr;|lash;?|ol;)|ti(?:lde;?|mes(?:;|as;))|uml;?|vbar;)|p(?:ar(?:;|a(?:;|llel;|)|s(?:im;|l;)|t;)|cy;|er(?:cnt;|iod;|mil;|p;|tenk;)|fr;|h(?:i(?:;|v;)|mmat;|one;)|i(?:;|tchfork;|v;)|l(?:an(?:ck(?:;|h;)|kv;)|us(?:;|acir;|b;|cir;|d(?:o;|u;)|e;|mn;?|sim;|two;))|m;|o(?:intint;|pf;|und;?)|r(?:;|E;|ap;|cue;|e(?:;|c(?:;|approx;|curlyeq;|eq;|n(?:approx;|eqq;|sim;)|sim;))|ime(?:;|s;)|n(?:E;|ap;|sim;)|o(?:d;|f(?:alar;|line;|surf;)|p(?:;|to;))|sim;|urel;)|s(?:cr;|i;)|uncsp;)|q(?:fr;|int;|opf;|prime;|scr;|u(?:at(?:ernions;|int;)|est(?:;|eq;)|ot;?))|r(?:A(?:arr;|rr;|tail;)|Barr;|Har;|a(?:c(?:e;|ute;)|dic;|emptyv;|ng(?:;|d;|e;|le;)|quo;?|rr(?:;|ap;|b(?:;|fs;)|c;|fs;|hk;|lp;|pl;|sim;|tl;|w;)|t(?:ail;|io(?:;|nals;)))|b(?:arr;|brk;|r(?:ac(?:e;|k;)|k(?:e;|sl(?:d;|u;))))|c(?:aron;|e(?:dil;|il;)|ub;|y;)|d(?:ca;|ldhar;|quo(?:;|r;)|sh;)|e(?:al(?:;|ine;|part;|s;)|ct;|g;?)|f(?:isht;|loor;|r;)|h(?:ar(?:d;|u(?:;|l;))|o(?:;|v;))|i(?:ght(?:arrow(?:;|tail;)|harpoon(?:down;|up;)|left(?:arrows;|harpoons;)|rightarrows;|squigarrow;|threetimes;)|ng;|singdotseq;)|l(?:arr;|har;|m;)|moust(?:;|ache;)|nmid;|o(?:a(?:ng;|rr;)|brk;|p(?:ar;|f;|lus;)|times;)|p(?:ar(?:;|gt;)|polint;)|rarr;|s(?:aquo;|cr;|h;|q(?:b;|uo(?:;|r;)))|t(?:hree;|imes;|ri(?:;|e;|f;|ltri;))|uluhar;|x;)|s(?:acute;|bquo;|c(?:;|E;|a(?:p;|ron;)|cue;|e(?:;|dil;)|irc;|n(?:E;|ap;|sim;)|polint;|sim;|y;)|dot(?:;|b;|e;)|e(?:Arr;|ar(?:hk;|r(?:;|ow;))|ct;?|mi;|swar;|tm(?:inus;|n;)|xt;)|fr(?:;|own;)|h(?:arp;|c(?:hcy;|y;)|ort(?:mid;|parallel;)|y;?)|i(?:gma(?:;|f;|v;)|m(?:;|dot;|e(?:;|q;)|g(?:;|E;)|l(?:;|E;)|ne;|plus;|rarr;))|larr;|m(?:a(?:llsetminus;|shp;)|eparsl;|i(?:d;|le;)|t(?:;|e(?:;|s;)))|o(?:ftcy;|l(?:;|b(?:;|ar;))|pf;)|pa(?:des(?:;|uit;)|r;)|q(?:c(?:ap(?:;|s;)|up(?:;|s;))|su(?:b(?:;|e;|set(?:;|eq;))|p(?:;|e;|set(?:;|eq;)))|u(?:;|ar(?:e;|f;)|f;))|rarr;|s(?:cr;|etmn;|mile;|tarf;)|t(?:ar(?:;|f;)|r(?:aight(?:epsilon;|phi;)|ns;))|u(?:b(?:;|E;|dot;|e(?:;|dot;)|mult;|n(?:E;|e;)|plus;|rarr;|s(?:et(?:;|eq(?:;|q;)|neq(?:;|q;))|im;|u(?:b;|p;)))|cc(?:;|approx;|curlyeq;|eq;|n(?:approx;|eqq;|sim;)|sim;)|m;|ng;|p(?:1;?|2;?|3;?|;|E;|d(?:ot;|sub;)|e(?:;|dot;)|hs(?:ol;|ub;)|larr;|mult;|n(?:E;|e;)|plus;|s(?:et(?:;|eq(?:;|q;)|neq(?:;|q;))|im;|u(?:b;|p;))))|w(?:Arr;|ar(?:hk;|r(?:;|ow;))|nwar;)|zlig;?)|t(?:a(?:rget;|u;)|brk;|c(?:aron;|edil;|y;)|dot;|elrec;|fr;|h(?:e(?:re(?:4;|fore;)|ta(?:;|sym;|v;))|i(?:ck(?:approx;|sim;)|nsp;)|k(?:ap;|sim;)|orn;?)|i(?:lde;|mes(?:;|b(?:;|ar;)|d;|)|nt;)|o(?:ea;|p(?:;|bot;|cir;|f(?:;|ork;))|sa;)|prime;|r(?:ade;|i(?:angle(?:;|down;|left(?:;|eq;)|q;|right(?:;|eq;))|dot;|e;|minus;|plus;|sb;|time;)|pezium;)|s(?:c(?:r;|y;)|hcy;|trok;)|w(?:ixt;|ohead(?:leftarrow;|rightarrow;)))|u(?:Arr;|Har;|a(?:cute;?|rr;)|br(?:cy;|eve;)|c(?:irc;?|y;)|d(?:arr;|blac;|har;)|f(?:isht;|r;)|grave;?|h(?:ar(?:l;|r;)|blk;)|l(?:c(?:orn(?:;|er;)|rop;)|tri;)|m(?:acr;|l;?)|o(?:gon;|pf;)|p(?:arrow;|downarrow;|harpoon(?:left;|right;)|lus;|si(?:;|h;|lon;)|uparrows;)|r(?:c(?:orn(?:;|er;)|rop;)|ing;|tri;)|scr;|t(?:dot;|ilde;|ri(?:;|f;))|u(?:arr;|ml;?)|wangle;)|v(?:Arr;|Bar(?:;|v;)|Dash;|a(?:ngrt;|r(?:epsilon;|kappa;|nothing;|p(?:hi;|i;|ropto;)|r(?:;|ho;)|s(?:igma;|u(?:bsetneq(?:;|q;)|psetneq(?:;|q;)))|t(?:heta;|riangle(?:left;|right;))))|cy;|dash;|e(?:e(?:;|bar;|eq;)|llip;|r(?:bar;|t;))|fr;|ltri;|nsu(?:b;|p;)|opf;|prop;|rtri;|s(?:cr;|u(?:bn(?:E;|e;)|pn(?:E;|e;)))|zigzag;)|w(?:circ;|e(?:d(?:bar;|ge(?:;|q;))|ierp;)|fr;|opf;|p;|r(?:;|eath;)|scr;)|x(?:c(?:ap;|irc;|up;)|dtri;|fr;|h(?:Arr;|arr;)|i;|l(?:Arr;|arr;)|map;|nis;|o(?:dot;|p(?:f;|lus;)|time;)|r(?:Arr;|arr;)|s(?:cr;|qcup;)|u(?:plus;|tri;)|vee;|wedge;)|y(?:ac(?:ute;?|y;)|c(?:irc;|y;)|en;?|fr;|icy;|opf;|scr;|u(?:cy;|ml;?))|z(?:acute;|c(?:aron;|y;)|dot;|e(?:etrf;|ta;)|fr;|hcy;|igrarr;|opf;|scr;|w(?:j;|nj;)))|[\s\S]/g,q=/[^\r"&\u0000]+/g,P=/[^\r'&\u0000]+/g,B=/[^\r\t\n\f &>\u0000]+/g,H=/[^\r\t\n\f \/>A-Z\u0000]+/g,U=/[^\r\t\n\f \/=>A-Z\u0000]+/g,F=/[^\]\r\u0000\uffff]*/g,j=/[^&<\r\u0000\uffff]*/g,z=/[^<\r\u0000\uffff]*/g,V=/[^\r\u0000\uffff]*/g,W=/(?:(\/)?([a-z]+)>)|[\s\S]/g,G=/(?:([-a-z]+)[ \t\n\f]*=[ \t\n\f]*('[^'&\r\u0000]*'|"[^"&\r\u0000]*"|[^\t\n\r\f "&'\u0000>][^&> \t\n\r\f\u0000]*[ \t\n\f]))|[\s\S]/g,Y=/[^\x09\x0A\x0C\x0D\x20]/,$=/[^\x09\x0A\x0C\x0D\x20]/g,K=/[^\x00\x09\x0A\x0C\x0D\x20]/,X=/^[\x09\x0A\x0C\x0D\x20]+/,Q=/\x00/g;function Z(e){if(e.length<16384)return String.fromCharCode.apply(String,e);for(var t="",r=0;r<e.length;r+=16384)t+=String.fromCharCode.apply(String,e.slice(r,r+16384));return t}function J(e,t){if("string"==typeof t)return e.namespaceURI===o.HTML&&e.localName===t;var r=t[e.namespaceURI];return r&&r[e.localName]}function ee(e){return J(e,C)}function te(e){if(J(e,A))return!0;if(e.namespaceURI===o.MATHML&&"annotation-xml"===e.localName){var t=e.getAttribute("encoding");if(t&&(t=t.toLowerCase()),"text/html"===t||"application/xhtml+xml"===t)return!0}return!1}function re(e){for(var t=0,r=e.length;t<r;t++)e[t][0]in L&&(e[t][0]=L[e[t][0]])}function ne(e){for(var t=0,r=e.length;t<r;t++)if("definitionurl"===e[t][0]){e[t][0]="definitionURL";break}}function ie(e){for(var t=0,r=e.length;t<r;t++)e[t][0]in R&&e[t].push(R[e[t][0]])}function ae(e,t){for(var r=0,n=e.length;r<n;r++){var i=e[r][0],a=e[r][1];t.hasAttribute(i)||t._setAttribute(i,a)}}function oe(e,t,r){var v,_,T=null,S=0,x=0,N=!1,C=!1,A=0,R=[],L="",se=!0,ce=0,le=Et,ue="",he="",de=[],pe="",fe="",me=[],ge=[],be=[],ve=[],_e=[],ye=!1,we=function(e,t,r,n){switch(e){case 1:if(0===(t=t.replace(X,"")).length)return;break;case 4:return void Be._appendChild(Be.createComment(t));case 5:var a=t,o=r,s=n;return Be.appendChild(new i(Be,a,o,s)),Ie||"html"!==a.toLowerCase()||h.test(o)||s&&"http://www.ibm.com/data/dtd/v11/ibmxhtml1-transitional.dtd"===s.toLowerCase()||void 0===s&&d.test(o)?Be._quirks=!0:(p.test(o)||void 0!==s&&d.test(o))&&(Be._limitedQuirks=!0),void(we=$r)}Be._quirks=!0,(we=$r)(e,t,r,n)},Ee=null,ke=[],Te=new oe.ElementStack,Se=new oe.ActiveFormattingElements,xe=void 0!==t,Ne=null,Ce=null,Ae=!0;t&&(Ae=t.ownerDocument._scripting_enabled),r&&!1===r.scripting_enabled&&(Ae=!1);var Re,Le,Oe=!0,Ie=!1,De=[],Me=!1,qe=!1,Pe={document:function(){return Be},_asDocumentFragment:function(){for(var e=Be.createDocumentFragment(),t=Be.firstChild;t.hasChildNodes();)e.appendChild(t.firstChild);return e},pause:function(){ce++},resume:function(){ce--,this.parse("")},parse:function(e,t,r){var n;return ce>0?(L+=e,!0):(0===A?(L&&(e=L+e,L=""),t&&(e+="￿",N=!0),T=e,S=e.length,x=0,se&&(se=!1,65279===T.charCodeAt(0)&&(x=1)),A++,n=Fe(r),L=T.substring(x,S),A--):(A++,R.push(T,S,x),T=e,S=e.length,x=0,Fe(),n=!1,L=T.substring(x,S),x=R.pop(),S=R.pop(),T=R.pop(),L&&(T=L+T.substring(x),S=T.length,x=0,L=""),A--),n)}},Be=new n(!0,e);if(Be._parser=Pe,Be._scripting_enabled=Ae,t){if(t.ownerDocument._quirks&&(Be._quirks=!0),t.ownerDocument._limitedQuirks&&(Be._limitedQuirks=!0),t.namespaceURI===o.HTML)switch(t.localName){case"title":case"textarea":le=kt;break;case"style":case"xmp":case"iframe":case"noembed":case"noframes":case"script":case"plaintext":le=xt;break;case"noscript":Ae&&(le=xt)}var He=Be.createElement("html");Be._appendChild(He),Te.push(He),t instanceof c.HTMLTemplateElement&&ke.push(hn),mt();for(var Ue=t;null!==Ue;Ue=Ue.parentElement)if(Ue instanceof c.HTMLFormElement){Ce=Ue;break}}function Fe(e){for(var t,r,n,i;x<S;){if(ce>0||e&&e())return!0;switch(typeof le.lookahead){case"undefined":if(t=T.charCodeAt(x++),C&&(C=!1,10===t)){x++;continue}switch(t){case 13:x<S?10===T.charCodeAt(x)&&x++:C=!0,le(10);break;case 65535:if(N&&x===S){le(-1);break}default:le(t)}break;case"number":t=T.charCodeAt(x);var a=le.lookahead,o=!0;if(a<0&&(o=!1,a=-a),a<S-x)r=o?T.substring(x,x+a):null,i=!1;else{if(!N)return!0;r=o?T.substring(x,S):null,i=!0,65535===t&&x===S-1&&(t=-1)}le(t,r,i);break;case"string":t=T.charCodeAt(x),n=le.lookahead;var s=T.indexOf(n,x);if(-1!==s)r=T.substring(x,s+n.length),i=!1;else{if(!N)return!0;r=T.substring(x,S),65535===t&&x===S-1&&(t=-1),i=!0}le(t,r,i)}}return!1}function je(e,t){for(var r=0;r<_e.length;r++)if(_e[r][0]===e)return;void 0!==t?_e.push([e,t]):_e.push([e])}function ze(){ye=!0,ue="",_e.length=0}function Ve(){de.length=0}function We(){pe=""}function Ge(){fe=""}function Ye(){me.length=0}function $e(){ge.length=0,be=null,ve=null}function Ke(){be=[]}function Xe(){ve=[]}function Qe(){Ie=!0}function Ze(e){return he===e}function Je(){if(De.length>0){var e=Z(De);if(De.length=0,qe&&(qe=!1,"\n"===e[0]&&(e=e.substring(1)),0===e.length))return;at(1,e),Me=!1}qe=!1}function et(e){e.lastIndex=x-1;var t=e.exec(T);if(t&&t.index===x-1)return t=t[0],x+=t.length-1,N&&x===S&&(t=t.slice(0,-1),x--),t;throw new Error("should never happen")}function tt(e){e.lastIndex=x-1;var t=e.exec(T)[0];return!!t&&(function(e){De.length>0&&Je();if(qe&&(qe=!1,"\n"===e[0]&&(e=e.substring(1)),0===e.length))return;at(1,e)}(t),x+=t.length-1,!0)}function rt(){if(ye)at(3,ue);else{var e=ue;ue="",he=e,at(2,e,_e)}}function nt(){at(5,Z(ge),be?Z(be):void 0,ve?Z(ve):void 0)}function it(){Je(),we(-1),Be.modclock=1}var at=Pe.insertToken=function(e,t,r,n){Je();var i=Te.top;i&&i.namespaceURI!==o.HTML?2!==e&&1!==e?bn(e,t,r,n):ee(i)&&(1===e||2===e&&"mglyph"!==t&&"malignmark"!==t)||2===e&&"svg"===t&&i.namespaceURI===o.MATHML&&"annotation-xml"===i.localName||te(i)?(Le=!0,we(e,t,r,n),Le=!1):bn(e,t,r,n):we(e,t,r,n)};function ot(e){var t=Te.top;lt&&J(t,b)?ft((function(t){return t.createComment(e)})):(t instanceof c.HTMLTemplateElement&&(t=t.content),t._appendChild(t.ownerDocument.createComment(e)))}function st(e){var t=Te.top;if(lt&&J(t,b))ft((function(t){return t.createTextNode(e)}));else{t instanceof c.HTMLTemplateElement&&(t=t.content);var r=t.lastChild;r&&r.nodeType===a.TEXT_NODE?r.appendData(e):t._appendChild(t.ownerDocument.createTextNode(e))}}function ct(e,t,r){var n=s.createElement(e,t,null);if(r)for(var i=0,a=r.length;i<a;i++)n._setAttribute(r[i][0],r[i][1]);return n}var lt=!1;function ut(e,t){var r=ht((function(r){return ct(r,e,t)}));return J(r,k)&&(r._form=Ce),r}function ht(e){var t;return lt&&J(Te.top,b)?t=ft(e):Te.top instanceof c.HTMLTemplateElement?(t=e(Te.top.content.ownerDocument),Te.top.content._appendChild(t)):(t=e(Te.top.ownerDocument),Te.top._appendChild(t)),Te.push(t),t}function dt(e,t,r){return ht((function(n){var i=n._createElementNS(e,r,null);if(t)for(var a=0,o=t.length;a<o;a++){var s=t[a];2===s.length?i._setAttribute(s[0],s[1]):i._setAttributeNS(s[2],s[0],s[1])}return i}))}function pt(e){for(var t=Te.elements.length-1;t>=0;t--)if(Te.elements[t]instanceof e)return t;return-1}function ft(e){var t,r,n,i,o,s;if((n=pt(c.HTMLTableElement),(i=pt(c.HTMLTemplateElement))>=0&&(n<0||i>n)?t=Te.elements[i]:n>=0&&((t=Te.elements[n].parentNode)?r=Te.elements[n]:t=Te.elements[n-1]),t||(t=Te.elements[0]),t instanceof c.HTMLTemplateElement&&(t=t.content),(o=e(t.ownerDocument)).nodeType===a.TEXT_NODE)&&((s=r?r.previousSibling:t.lastChild)&&s.nodeType===a.TEXT_NODE))return s.appendData(o.data),o;return r?t.insertBefore(o,r):t._appendChild(o),o}function mt(){for(var e=!1,r=Te.elements.length-1;r>=0;r--){var n=Te.elements[r];if(0===r&&(e=!0,xe&&(n=t)),n.namespaceURI===o.HTML){var i=n.localName;switch(i){case"select":for(var a=r;a>0;){var s=Te.elements[--a];if(s instanceof c.HTMLTemplateElement)break;if(s instanceof c.HTMLTableElement)return void(we=un)}return void(we=ln);case"tr":return void(we=sn);case"tbody":case"tfoot":case"thead":return void(we=on);case"caption":return void(we=nn);case"colgroup":return void(we=an);case"table":return void(we=tn);case"template":return void(we=ke[ke.length-1]);case"body":return void(we=Jr);case"frameset":return void(we=pn);case"html":return void(we=null===Ne?Kr:Zr);default:if(!e){if("head"===i)return void(we=Xr);if("td"===i||"th"===i)return void(we=cn)}}}if(e)return void(we=Jr)}}function gt(e,t){ut(e,t),le=Tt,Ee=we,we=en}function bt(e,t){return{elt:ct(e,Se.list[t].localName,Se.attrs[t]),attrs:Se.attrs[t]}}function vt(){if(0!==Se.list.length){var e=Se.list[Se.list.length-1];if(e!==Se.MARKER&&-1===Te.elements.lastIndexOf(e)){for(var t=Se.list.length-2;t>=0&&(e=Se.list[t])!==Se.MARKER&&-1===Te.elements.lastIndexOf(e);t--);for(t+=1;t<Se.list.length;t++){var r=ht((function(e){return bt(e,t).elt}));Se.list[t]=r}}}}var _t={localName:"BM"};function yt(){delete Be._parser,Te.elements.length=0,Be.defaultView&&Be.defaultView.dispatchEvent(new c.Event("load",{}))}function wt(e,t){le=t,x--}function Et(e){switch(e){case 38:v=Et,le=Hr;break;case 60:if(function(){if(x===S)return!1;W.lastIndex=x;var e=W.exec(T);if(!e)throw new Error("should never happen");var t=e[2];return!!t&&(e[1]?(x+=t.length+2,at(3,t)):(x+=t.length+1,he=t,at(2,t,u)),!0)}())break;le=Nt;break;case 0:De.push(e),Me=!0;break;case-1:it();break;default:tt(j)||De.push(e)}}function kt(e){switch(e){case 38:v=kt,le=Hr;break;case 60:le=Rt;break;case 0:De.push(65533),Me=!0;break;case-1:it();break;default:De.push(e)}}function Tt(e){switch(e){case 60:le=It;break;case 0:De.push(65533);break;case-1:it();break;default:tt(z)||De.push(e)}}function St(e){switch(e){case 60:le=qt;break;case 0:De.push(65533);break;case-1:it();break;default:tt(z)||De.push(e)}}function xt(e){switch(e){case 0:De.push(65533);break;case-1:it();break;default:tt(V)||De.push(e)}}function Nt(e){switch(e){case 33:le=lr;break;case 47:le=Ct;break;case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:case 97:case 98:case 99:case 100:case 101:case 102:case 103:case 104:case 105:case 106:case 107:case 108:case 109:case 110:case 111:case 112:case 113:case 114:case 115:case 116:case 117:case 118:case 119:case 120:case 121:case 122:ye=!1,ue="",_e.length=0,wt(0,At);break;case 63:wt(0,cr);break;default:De.push(60),wt(0,Et)}}function Ct(e){switch(e){case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:case 97:case 98:case 99:case 100:case 101:case 102:case 103:case 104:case 105:case 106:case 107:case 108:case 109:case 110:case 111:case 112:case 113:case 114:case 115:case 116:case 117:case 118:case 119:case 120:case 121:case 122:ze(),wt(0,At);break;case 62:le=Et;break;case-1:De.push(60),De.push(47),it();break;default:wt(0,cr)}}function At(e){switch(e){case 9:case 10:case 12:case 32:le=Jt;break;case 47:le=sr;break;case 62:le=Et,rt();break;case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:ue+=String.fromCharCode(e+32);break;case 0:ue+=String.fromCharCode(65533);break;case-1:it();break;default:ue+=et(H)}}function Rt(e){47===e?(Ve(),le=Lt):(De.push(60),wt(0,kt))}function Lt(e){switch(e){case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:case 97:case 98:case 99:case 100:case 101:case 102:case 103:case 104:case 105:case 106:case 107:case 108:case 109:case 110:case 111:case 112:case 113:case 114:case 115:case 116:case 117:case 118:case 119:case 120:case 121:case 122:ze(),wt(0,Ot);break;default:De.push(60),De.push(47),wt(0,kt)}}function Ot(e){switch(e){case 9:case 10:case 12:case 32:if(Ze(ue))return void(le=Jt);break;case 47:if(Ze(ue))return void(le=sr);break;case 62:if(Ze(ue))return le=Et,void rt();break;case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:return ue+=String.fromCharCode(e+32),void de.push(e);case 97:case 98:case 99:case 100:case 101:case 102:case 103:case 104:case 105:case 106:case 107:case 108:case 109:case 110:case 111:case 112:case 113:case 114:case 115:case 116:case 117:case 118:case 119:case 120:case 121:case 122:return ue+=String.fromCharCode(e),void de.push(e)}De.push(60),De.push(47),l(De,de),wt(0,kt)}function It(e){47===e?(Ve(),le=Dt):(De.push(60),wt(0,Tt))}function Dt(e){switch(e){case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:case 97:case 98:case 99:case 100:case 101:case 102:case 103:case 104:case 105:case 106:case 107:case 108:case 109:case 110:case 111:case 112:case 113:case 114:case 115:case 116:case 117:case 118:case 119:case 120:case 121:case 122:ze(),wt(0,Mt);break;default:De.push(60),De.push(47),wt(0,Tt)}}function Mt(e){switch(e){case 9:case 10:case 12:case 32:if(Ze(ue))return void(le=Jt);break;case 47:if(Ze(ue))return void(le=sr);break;case 62:if(Ze(ue))return le=Et,void rt();break;case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:return ue+=String.fromCharCode(e+32),void de.push(e);case 97:case 98:case 99:case 100:case 101:case 102:case 103:case 104:case 105:case 106:case 107:case 108:case 109:case 110:case 111:case 112:case 113:case 114:case 115:case 116:case 117:case 118:case 119:case 120:case 121:case 122:return ue+=String.fromCharCode(e),void de.push(e)}De.push(60),De.push(47),l(De,de),wt(0,Tt)}function qt(e){switch(e){case 47:Ve(),le=Pt;break;case 33:le=Ht,De.push(60),De.push(33);break;default:De.push(60),wt(0,St)}}function Pt(e){switch(e){case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:case 97:case 98:case 99:case 100:case 101:case 102:case 103:case 104:case 105:case 106:case 107:case 108:case 109:case 110:case 111:case 112:case 113:case 114:case 115:case 116:case 117:case 118:case 119:case 120:case 121:case 122:ze(),wt(0,Bt);break;default:De.push(60),De.push(47),wt(0,St)}}function Bt(e){switch(e){case 9:case 10:case 12:case 32:if(Ze(ue))return void(le=Jt);break;case 47:if(Ze(ue))return void(le=sr);break;case 62:if(Ze(ue))return le=Et,void rt();break;case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:return ue+=String.fromCharCode(e+32),void de.push(e);case 97:case 98:case 99:case 100:case 101:case 102:case 103:case 104:case 105:case 106:case 107:case 108:case 109:case 110:case 111:case 112:case 113:case 114:case 115:case 116:case 117:case 118:case 119:case 120:case 121:case 122:return ue+=String.fromCharCode(e),void de.push(e)}De.push(60),De.push(47),l(De,de),wt(0,St)}function Ht(e){45===e?(le=Ut,De.push(45)):wt(0,St)}function Ut(e){45===e?(le=zt,De.push(45)):wt(0,St)}function Ft(e){switch(e){case 45:le=jt,De.push(45);break;case 60:le=Vt;break;case 0:De.push(65533);break;case-1:it();break;default:De.push(e)}}function jt(e){switch(e){case 45:le=zt,De.push(45);break;case 60:le=Vt;break;case 0:le=Ft,De.push(65533);break;case-1:it();break;default:le=Ft,De.push(e)}}function zt(e){switch(e){case 45:De.push(45);break;case 60:le=Vt;break;case 62:le=St,De.push(62);break;case 0:le=Ft,De.push(65533);break;case-1:it();break;default:le=Ft,De.push(e)}}function Vt(e){switch(e){case 47:Ve(),le=Wt;break;case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:case 97:case 98:case 99:case 100:case 101:case 102:case 103:case 104:case 105:case 106:case 107:case 108:case 109:case 110:case 111:case 112:case 113:case 114:case 115:case 116:case 117:case 118:case 119:case 120:case 121:case 122:Ve(),De.push(60),wt(0,Yt);break;default:De.push(60),wt(0,Ft)}}function Wt(e){switch(e){case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:case 97:case 98:case 99:case 100:case 101:case 102:case 103:case 104:case 105:case 106:case 107:case 108:case 109:case 110:case 111:case 112:case 113:case 114:case 115:case 116:case 117:case 118:case 119:case 120:case 121:case 122:ze(),wt(0,Gt);break;default:De.push(60),De.push(47),wt(0,Ft)}}function Gt(e){switch(e){case 9:case 10:case 12:case 32:if(Ze(ue))return void(le=Jt);break;case 47:if(Ze(ue))return void(le=sr);break;case 62:if(Ze(ue))return le=Et,void rt();break;case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:return ue+=String.fromCharCode(e+32),void de.push(e);case 97:case 98:case 99:case 100:case 101:case 102:case 103:case 104:case 105:case 106:case 107:case 108:case 109:case 110:case 111:case 112:case 113:case 114:case 115:case 116:case 117:case 118:case 119:case 120:case 121:case 122:return ue+=String.fromCharCode(e),void de.push(e)}De.push(60),De.push(47),l(De,de),wt(0,Ft)}function Yt(e){switch(e){case 9:case 10:case 12:case 32:case 47:case 62:le="script"===Z(de)?$t:Ft,De.push(e);break;case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:de.push(e+32),De.push(e);break;case 97:case 98:case 99:case 100:case 101:case 102:case 103:case 104:case 105:case 106:case 107:case 108:case 109:case 110:case 111:case 112:case 113:case 114:case 115:case 116:case 117:case 118:case 119:case 120:case 121:case 122:de.push(e),De.push(e);break;default:wt(0,Ft)}}function $t(e){switch(e){case 45:le=Kt,De.push(45);break;case 60:le=Qt,De.push(60);break;case 0:De.push(65533);break;case-1:it();break;default:De.push(e)}}function Kt(e){switch(e){case 45:le=Xt,De.push(45);break;case 60:le=Qt,De.push(60);break;case 0:le=$t,De.push(65533);break;case-1:it();break;default:le=$t,De.push(e)}}function Xt(e){switch(e){case 45:De.push(45);break;case 60:le=Qt,De.push(60);break;case 62:le=St,De.push(62);break;case 0:le=$t,De.push(65533);break;case-1:it();break;default:le=$t,De.push(e)}}function Qt(e){47===e?(Ve(),le=Zt,De.push(47)):wt(0,$t)}function Zt(e){switch(e){case 9:case 10:case 12:case 32:case 47:case 62:le="script"===Z(de)?Ft:$t,De.push(e);break;case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:de.push(e+32),De.push(e);break;case 97:case 98:case 99:case 100:case 101:case 102:case 103:case 104:case 105:case 106:case 107:case 108:case 109:case 110:case 111:case 112:case 113:case 114:case 115:case 116:case 117:case 118:case 119:case 120:case 121:case 122:de.push(e),De.push(e);break;default:wt(0,$t)}}function Jt(e){switch(e){case 9:case 10:case 12:case 32:break;case 47:le=sr;break;case 62:le=Et,rt();break;case-1:it();break;case 61:We(),pe+=String.fromCharCode(e),le=er;break;default:if(function(){G.lastIndex=x-1;var e=G.exec(T);if(!e)throw new Error("should never happen");var t=e[1];if(!t)return!1;var r=e[2],n=r.length;switch(r[0]){case'"':case"'":r=r.substring(1,n-1),x+=e[0].length-1,le=or;break;default:le=Jt,x+=e[0].length-1,r=r.substring(0,n-1)}for(var i=0;i<_e.length;i++)if(_e[i][0]===t)return!0;return _e.push([t,r]),!0}())break;We(),wt(0,er)}}function er(e){switch(e){case 9:case 10:case 12:case 32:case 47:case 62:case-1:wt(0,tr);break;case 61:le=rr;break;case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:pe+=String.fromCharCode(e+32);break;case 0:pe+=String.fromCharCode(65533);break;case 34:case 39:case 60:default:pe+=et(U)}}function tr(e){switch(e){case 9:case 10:case 12:case 32:break;case 47:je(pe),le=sr;break;case 61:le=rr;break;case 62:le=Et,je(pe),rt();break;case-1:je(pe),it();break;default:je(pe),We(),wt(0,er)}}function rr(e){switch(e){case 9:case 10:case 12:case 32:break;case 34:Ge(),le=nr;break;case 39:Ge(),le=ir;break;case 62:default:Ge(),wt(0,ar)}}function nr(e){switch(e){case 34:je(pe,fe),le=or;break;case 38:v=nr,le=Hr;break;case 0:fe+=String.fromCharCode(65533);break;case-1:it();break;case 10:fe+=String.fromCharCode(e);break;default:fe+=et(q)}}function ir(e){switch(e){case 39:je(pe,fe),le=or;break;case 38:v=ir,le=Hr;break;case 0:fe+=String.fromCharCode(65533);break;case-1:it();break;case 10:fe+=String.fromCharCode(e);break;default:fe+=et(P)}}function ar(e){switch(e){case 9:case 10:case 12:case 32:je(pe,fe),le=Jt;break;case 38:v=ar,le=Hr;break;case 62:je(pe,fe),le=Et,rt();break;case 0:fe+=String.fromCharCode(65533);break;case-1:x--,le=Et;break;case 34:case 39:case 60:case 61:case 96:default:fe+=et(B)}}function or(e){switch(e){case 9:case 10:case 12:case 32:le=Jt;break;case 47:le=sr;break;case 62:le=Et,rt();break;case-1:it();break;default:wt(0,Jt)}}function sr(e){switch(e){case 62:le=Et,ye?at(3,ue,null,!0):at(2,ue,_e,!0);break;case-1:it();break;default:wt(0,Jt)}}function cr(e,t,r){var n=t.length;x+=r?n-1:n;var i=t.substring(0,n-1);i=(i=(i=i.replace(/\u0000/g,"�")).replace(/\u000D\u000A/g,"\n")).replace(/\u000D/g,"\n"),at(4,i),le=Et}function lr(e,t,r){if("-"===t[0]&&"-"===t[1])return x+=2,Ye(),void(le=ur);"DOCTYPE"===t.toUpperCase()?(x+=7,le=yr):"[CDATA["===t&&Te.top&&"http://www.w3.org/1999/xhtml"!==Te.top.namespaceURI?(x+=7,le=qr):le=cr}function ur(e){switch(Ye(),e){case 45:le=hr;break;case 62:le=Et,at(4,Z(me));break;default:wt(0,dr)}}function hr(e){switch(e){case 45:le=vr;break;case 62:le=Et,at(4,Z(me));break;case-1:at(4,Z(me)),it();break;default:me.push(45),wt(0,dr)}}function dr(e){switch(e){case 60:me.push(e),le=pr;break;case 45:le=br;break;case 0:me.push(65533);break;case-1:at(4,Z(me)),it();break;default:me.push(e)}}function pr(e){switch(e){case 33:me.push(e),le=fr;break;case 60:me.push(e);break;default:wt(0,dr)}}function fr(e){switch(e){case 45:le=mr;break;default:wt(0,dr)}}function mr(e){switch(e){case 45:le=gr;break;default:wt(0,br)}}function gr(e){switch(e){case 62:case-1:default:wt(0,vr)}}function br(e){switch(e){case 45:le=vr;break;case-1:at(4,Z(me)),it();break;default:me.push(45),wt(0,dr)}}function vr(e){switch(e){case 62:le=Et,at(4,Z(me));break;case 33:le=_r;break;case 45:me.push(45);break;case-1:at(4,Z(me)),it();break;default:me.push(45),me.push(45),wt(0,dr)}}function _r(e){switch(e){case 45:me.push(45),me.push(45),me.push(33),le=br;break;case 62:le=Et,at(4,Z(me));break;case-1:at(4,Z(me)),it();break;default:me.push(45),me.push(45),me.push(33),wt(0,dr)}}function yr(e){switch(e){case 9:case 10:case 12:case 32:le=wr;break;case-1:$e(),Qe(),nt(),it();break;default:wt(0,wr)}}function wr(e){switch(e){case 9:case 10:case 12:case 32:break;case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:$e(),ge.push(e+32),le=Er;break;case 0:$e(),ge.push(65533),le=Er;break;case 62:$e(),Qe(),le=Et,nt();break;case-1:$e(),Qe(),nt(),it();break;default:$e(),ge.push(e),le=Er}}function Er(e){switch(e){case 9:case 10:case 12:case 32:le=kr;break;case 62:le=Et,nt();break;case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:ge.push(e+32);break;case 0:ge.push(65533);break;case-1:Qe(),nt(),it();break;default:ge.push(e)}}function kr(e,t,r){switch(e){case 9:case 10:case 12:case 32:x+=1;break;case 62:le=Et,x+=1,nt();break;case-1:Qe(),nt(),it();break;default:"PUBLIC"===(t=t.toUpperCase())?(x+=6,le=Tr):"SYSTEM"===t?(x+=6,le=Rr):(Qe(),le=Mr)}}function Tr(e){switch(e){case 9:case 10:case 12:case 32:le=Sr;break;case 34:Ke(),le=xr;break;case 39:Ke(),le=Nr;break;case 62:Qe(),le=Et,nt();break;case-1:Qe(),nt(),it();break;default:Qe(),le=Mr}}function Sr(e){switch(e){case 9:case 10:case 12:case 32:break;case 34:Ke(),le=xr;break;case 39:Ke(),le=Nr;break;case 62:Qe(),le=Et,nt();break;case-1:Qe(),nt(),it();break;default:Qe(),le=Mr}}function xr(e){switch(e){case 34:le=Cr;break;case 0:be.push(65533);break;case 62:Qe(),le=Et,nt();break;case-1:Qe(),nt(),it();break;default:be.push(e)}}function Nr(e){switch(e){case 39:le=Cr;break;case 0:be.push(65533);break;case 62:Qe(),le=Et,nt();break;case-1:Qe(),nt(),it();break;default:be.push(e)}}function Cr(e){switch(e){case 9:case 10:case 12:case 32:le=Ar;break;case 62:le=Et,nt();break;case 34:Xe(),le=Or;break;case 39:Xe(),le=Ir;break;case-1:Qe(),nt(),it();break;default:Qe(),le=Mr}}function Ar(e){switch(e){case 9:case 10:case 12:case 32:break;case 62:le=Et,nt();break;case 34:Xe(),le=Or;break;case 39:Xe(),le=Ir;break;case-1:Qe(),nt(),it();break;default:Qe(),le=Mr}}function Rr(e){switch(e){case 9:case 10:case 12:case 32:le=Lr;break;case 34:Xe(),le=Or;break;case 39:Xe(),le=Ir;break;case 62:Qe(),le=Et,nt();break;case-1:Qe(),nt(),it();break;default:Qe(),le=Mr}}function Lr(e){switch(e){case 9:case 10:case 12:case 32:break;case 34:Xe(),le=Or;break;case 39:Xe(),le=Ir;break;case 62:Qe(),le=Et,nt();break;case-1:Qe(),nt(),it();break;default:Qe(),le=Mr}}function Or(e){switch(e){case 34:le=Dr;break;case 0:ve.push(65533);break;case 62:Qe(),le=Et,nt();break;case-1:Qe(),nt(),it();break;default:ve.push(e)}}function Ir(e){switch(e){case 39:le=Dr;break;case 0:ve.push(65533);break;case 62:Qe(),le=Et,nt();break;case-1:Qe(),nt(),it();break;default:ve.push(e)}}function Dr(e){switch(e){case 9:case 10:case 12:case 32:break;case 62:le=Et,nt();break;case-1:Qe(),nt(),it();break;default:le=Mr}}function Mr(e){switch(e){case 62:le=Et,nt();break;case-1:nt(),it()}}function qr(e){switch(e){case 93:le=Pr;break;case-1:it();break;case 0:Me=!0;default:tt(F)||De.push(e)}}function Pr(e){switch(e){case 93:le=Br;break;default:De.push(93),wt(0,qr)}}function Br(e){switch(e){case 93:De.push(93);break;case 62:Je(),le=Et;break;default:De.push(93),De.push(93),wt(0,qr)}}function Hr(e){switch(Ve(),de.push(38),e){case 9:case 10:case 12:case 32:case 60:case 38:case-1:wt(0,Yr);break;case 35:de.push(e),le=Fr;break;default:wt(0,Ur)}}function Ur(e){M.lastIndex=x;var t=M.exec(T);if(!t)throw new Error("should never happen");var r=t[1];if(r){switch(x+=r.length,l(de,function(e){for(var t=[],r=0;r<e.length;r++)t[r]=e.charCodeAt(r);return t}(r)),v){case nr:case ir:case ar:if(";"!==r[r.length-1]&&/[=A-Za-z0-9]/.test(T[x]))return void(le=Yr)}Ve();var n=D[r];"number"==typeof n?de.push(n):l(de,n),le=Yr}else le=Yr}function Fr(e){switch(_=0,e){case 120:case 88:de.push(e),le=jr;break;default:wt(0,zr)}}function jr(e){switch(e){case 48:case 49:case 50:case 51:case 52:case 53:case 54:case 55:case 56:case 57:case 65:case 66:case 67:case 68:case 69:case 70:case 97:case 98:case 99:case 100:case 101:case 102:wt(0,Vr);break;default:wt(0,Yr)}}function zr(e){switch(e){case 48:case 49:case 50:case 51:case 52:case 53:case 54:case 55:case 56:case 57:wt(0,Wr);break;default:wt(0,Yr)}}function Vr(e){switch(e){case 65:case 66:case 67:case 68:case 69:case 70:_*=16,_+=e-55;break;case 97:case 98:case 99:case 100:case 101:case 102:_*=16,_+=e-87;break;case 48:case 49:case 50:case 51:case 52:case 53:case 54:case 55:case 56:case 57:_*=16,_+=e-48;break;case 59:le=Gr;break;default:wt(0,Gr)}}function Wr(e){switch(e){case 48:case 49:case 50:case 51:case 52:case 53:case 54:case 55:case 56:case 57:_*=10,_+=e-48;break;case 59:le=Gr;break;default:wt(0,Gr)}}function Gr(e){_ in I?_=I[_]:(_>1114111||_>=55296&&_<57344)&&(_=65533),Ve(),_<=65535?de.push(_):(_-=65536,de.push(55296+(_>>10)),de.push(56320+(1023&_))),wt(0,Yr)}function Yr(e){switch(v){case nr:case ir:case ar:fe+=Z(de);break;default:l(De,de)}wt(0,v)}function $r(e,t,r,n){var i;switch(e){case 1:if(0===(t=t.replace(X,"")).length)return;break;case 5:return;case 4:return void Be._appendChild(Be.createComment(t));case 2:if("html"===t)return i=ct(Be,t,r),Te.push(i),Be.appendChild(i),void(we=Kr);break;case 3:switch(t){case"html":case"head":case"body":case"br":break;default:return}}i=ct(Be,"html",null),Te.push(i),Be.appendChild(i),(we=Kr)(e,t,r,n)}function Kr(e,t,r,n){switch(e){case 1:if(0===(t=t.replace(X,"")).length)return;break;case 5:return;case 4:return void ot(t);case 2:switch(t){case"html":return void Jr(e,t,r,n);case"head":var i=ut(t,r);return Ne=i,void(we=Xr)}break;case 3:switch(t){case"html":case"head":case"body":case"br":break;default:return}}Kr(2,"head",null),we(e,t,r,n)}function Xr(e,t,r,n){switch(e){case 1:var i=t.match(X);if(i&&(st(i[0]),t=t.substring(i[0].length)),0===t.length)return;break;case 4:return void ot(t);case 5:return;case 2:switch(t){case"html":return void Jr(e,t,r,n);case"meta":case"base":case"basefont":case"bgsound":case"link":return ut(t,r),void Te.pop();case"title":return ut(t,r),le=kt,Ee=we,void(we=en);case"noscript":if(!Ae)return ut(t,r),void(we=Qr);case"noframes":case"style":return void gt(t,r);case"script":return ht((function(e){var n=ct(e,t,r);return n._parser_inserted=!0,n._force_async=!1,xe&&(n._already_started=!0),Je(),n})),le=St,Ee=we,void(we=en);case"template":return ut(t,r),Se.insertMarker(),Oe=!1,we=hn,void ke.push(we);case"head":return}break;case 3:switch(t){case"head":return Te.pop(),void(we=Zr);case"body":case"html":case"br":break;case"template":if(!Te.contains("template"))return;return Te.generateImpliedEndTags(null,"thorough"),Te.popTag("template"),Se.clearToMarker(),ke.pop(),void mt();default:return}}Xr(3,"head",null),we(e,t,r,n)}function Qr(e,t,r,n){switch(e){case 5:return;case 4:return void Xr(e,t);case 1:var i=t.match(X);if(i&&(Xr(e,i[0]),t=t.substring(i[0].length)),0===t.length)return;break;case 2:switch(t){case"html":return void Jr(e,t,r,n);case"basefont":case"bgsound":case"link":case"meta":case"noframes":case"style":return void Xr(e,t,r);case"head":case"noscript":return}break;case 3:switch(t){case"noscript":return Te.pop(),void(we=Xr);case"br":break;default:return}}Qr(3,"noscript",null),we(e,t,r,n)}function Zr(e,t,r,n){switch(e){case 1:var i=t.match(X);if(i&&(st(i[0]),t=t.substring(i[0].length)),0===t.length)return;break;case 4:return void ot(t);case 5:return;case 2:switch(t){case"html":return void Jr(e,t,r,n);case"body":return ut(t,r),Oe=!1,void(we=Jr);case"frameset":return ut(t,r),void(we=pn);case"base":case"basefont":case"bgsound":case"link":case"meta":case"noframes":case"script":case"style":case"template":case"title":return Te.push(Ne),Xr(2,t,r),void Te.removeElement(Ne);case"head":return}break;case 3:switch(t){case"template":return Xr(e,t,r,n);case"body":case"html":case"br":break;default:return}}Zr(2,"body",null),Oe=!0,we(e,t,r,n)}function Jr(e,t,r,n){var i,a,s,l;switch(e){case 1:if(Me&&0===(t=t.replace(Q,"")).length)return;return Oe&&Y.test(t)&&(Oe=!1),vt(),void st(t);case 5:return;case 4:return void ot(t);case-1:return ke.length?hn(e):void yt();case 2:switch(t){case"html":if(Te.contains("template"))return;return void ae(r,Te.elements[0]);case"base":case"basefont":case"bgsound":case"link":case"meta":case"noframes":case"script":case"style":case"template":case"title":return void Xr(2,t,r);case"body":if(!(i=Te.elements[1])||!(i instanceof c.HTMLBodyElement)||Te.contains("template"))return;return Oe=!1,void ae(r,i);case"frameset":if(!Oe)return;if(!((i=Te.elements[1])&&i instanceof c.HTMLBodyElement))return;for(i.parentNode&&i.parentNode.removeChild(i);!(Te.top instanceof c.HTMLHtmlElement);)Te.pop();return ut(t,r),void(we=pn);case"address":case"article":case"aside":case"blockquote":case"center":case"details":case"dialog":case"dir":case"div":case"dl":case"fieldset":case"figcaption":case"figure":case"footer":case"header":case"hgroup":case"main":case"nav":case"ol":case"p":case"section":case"summary":case"ul":return Te.inButtonScope("p")&&Jr(3,"p"),void ut(t,r);case"menu":return Te.inButtonScope("p")&&Jr(3,"p"),J(Te.top,"menuitem")&&Te.pop(),void ut(t,r);case"h1":case"h2":case"h3":case"h4":case"h5":case"h6":return Te.inButtonScope("p")&&Jr(3,"p"),Te.top instanceof c.HTMLHeadingElement&&Te.pop(),void ut(t,r);case"pre":case"listing":return Te.inButtonScope("p")&&Jr(3,"p"),ut(t,r),qe=!0,void(Oe=!1);case"form":if(Ce&&!Te.contains("template"))return;return Te.inButtonScope("p")&&Jr(3,"p"),l=ut(t,r),void(Te.contains("template")||(Ce=l));case"li":for(Oe=!1,a=Te.elements.length-1;a>=0;a--){if((s=Te.elements[a])instanceof c.HTMLLIElement){Jr(3,"li");break}if(J(s,f)&&!J(s,m))break}return Te.inButtonScope("p")&&Jr(3,"p"),void ut(t,r);case"dd":case"dt":for(Oe=!1,a=Te.elements.length-1;a>=0;a--){if(J(s=Te.elements[a],g)){Jr(3,s.localName);break}if(J(s,f)&&!J(s,m))break}return Te.inButtonScope("p")&&Jr(3,"p"),void ut(t,r);case"plaintext":return Te.inButtonScope("p")&&Jr(3,"p"),ut(t,r),void(le=xt);case"button":return void(Te.inScope("button")?(Jr(3,"button"),we(e,t,r,n)):(vt(),ut(t,r),Oe=!1));case"a":var u=Se.findElementByTag("a");u&&(Jr(3,t),Se.remove(u),Te.removeElement(u));case"b":case"big":case"code":case"em":case"font":case"i":case"s":case"small":case"strike":case"strong":case"tt":case"u":return vt(),void Se.push(ut(t,r),r);case"nobr":return vt(),Te.inScope(t)&&(Jr(3,t),vt()),void Se.push(ut(t,r),r);case"applet":case"marquee":case"object":return vt(),ut(t,r),Se.insertMarker(),void(Oe=!1);case"table":return!Be._quirks&&Te.inButtonScope("p")&&Jr(3,"p"),ut(t,r),Oe=!1,void(we=tn);case"area":case"br":case"embed":case"img":case"keygen":case"wbr":return vt(),ut(t,r),Te.pop(),void(Oe=!1);case"input":vt(),l=ut(t,r),Te.pop();var h=l.getAttribute("type");return void(h&&"hidden"===h.toLowerCase()||(Oe=!1));case"param":case"source":case"track":return ut(t,r),void Te.pop();case"hr":return Te.inButtonScope("p")&&Jr(3,"p"),J(Te.top,"menuitem")&&Te.pop(),ut(t,r),Te.pop(),void(Oe=!1);case"image":return void Jr(2,"img",r,n);case"textarea":return ut(t,r),qe=!0,Oe=!1,le=kt,Ee=we,void(we=en);case"xmp":return Te.inButtonScope("p")&&Jr(3,"p"),vt(),Oe=!1,void gt(t,r);case"iframe":return Oe=!1,void gt(t,r);case"noembed":return void gt(t,r);case"noscript":if(Ae)return void gt(t,r);break;case"select":return vt(),ut(t,r),Oe=!1,void(we=we===tn||we===nn||we===on||we===sn||we===cn?un:ln);case"optgroup":case"option":return Te.top instanceof c.HTMLOptionElement&&Jr(3,"option"),vt(),void ut(t,r);case"menuitem":return J(Te.top,"menuitem")&&Te.pop(),vt(),void ut(t,r);case"rb":case"rtc":return Te.inScope("ruby")&&Te.generateImpliedEndTags(),void ut(t,r);case"rp":case"rt":return Te.inScope("ruby")&&Te.generateImpliedEndTags("rtc"),void ut(t,r);case"math":return vt(),ne(r),ie(r),dt(t,r,o.MATHML),void(n&&Te.pop());case"svg":return vt(),re(r),ie(r),dt(t,r,o.SVG),void(n&&Te.pop());case"caption":case"col":case"colgroup":case"frame":case"head":case"tbody":case"td":case"tfoot":case"th":case"thead":case"tr":return}return vt(),void ut(t,r);case 3:switch(t){case"template":return void Xr(3,t,r);case"body":if(!Te.inScope("body"))return;return void(we=dn);case"html":if(!Te.inScope("body"))return;return void(we=dn)(e,t,r);case"address":case"article":case"aside":case"blockquote":case"button":case"center":case"details":case"dialog":case"dir":case"div":case"dl":case"fieldset":case"figcaption":case"figure":case"footer":case"header":case"hgroup":case"listing":case"main":case"menu":case"nav":case"ol":case"pre":case"section":case"summary":case"ul":if(!Te.inScope(t))return;return Te.generateImpliedEndTags(),void Te.popTag(t);case"form":if(Te.contains("template")){if(!Te.inScope("form"))return;Te.generateImpliedEndTags(),Te.popTag("form")}else{var d=Ce;if(Ce=null,!d||!Te.elementInScope(d))return;Te.generateImpliedEndTags(),Te.removeElement(d)}return;case"p":return void(Te.inButtonScope(t)?(Te.generateImpliedEndTags(t),Te.popTag(t)):(Jr(2,t,null),we(e,t,r,n)));case"li":if(!Te.inListItemScope(t))return;return Te.generateImpliedEndTags(t),void Te.popTag(t);case"dd":case"dt":if(!Te.inScope(t))return;return Te.generateImpliedEndTags(t),void Te.popTag(t);case"h1":case"h2":case"h3":case"h4":case"h5":case"h6":if(!Te.elementTypeInScope(c.HTMLHeadingElement))return;return Te.generateImpliedEndTags(),void Te.popElementType(c.HTMLHeadingElement);case"sarcasm":break;case"a":case"b":case"big":case"code":case"em":case"font":case"i":case"nobr":case"s":case"small":case"strike":case"strong":case"tt":case"u":if(function(e){if(J(Te.top,e)&&-1===Se.indexOf(Te.top))return Te.pop(),!0;for(var t=0;t<8;){t++;var r=Se.findElementByTag(e);if(!r)return!1;var n=Te.elements.lastIndexOf(r);if(-1===n)return Se.remove(r),!0;if(!Te.elementInScope(r))return!0;for(var i,a=null,o=n+1;o<Te.elements.length;o++)if(J(Te.elements[o],f)){a=Te.elements[o],i=o;break}if(!a)return Te.popElement(r),Se.remove(r),!0;var s=Te.elements[n-1];Se.insertAfter(r,_t);for(var l,u=a,h=a,d=i,p=0;p++,(u=Te.elements[--d])!==r;)if(l=Se.indexOf(u),p>3&&-1!==l&&(Se.remove(u),l=-1),-1!==l){var m=bt(s.ownerDocument,l);Se.replace(u,m.elt,m.attrs),Te.elements[d]=m.elt,u=m.elt,h===a&&(Se.remove(_t),Se.insertAfter(m.elt,_t)),u._appendChild(h),h=u}else Te.removeElement(u);lt&&J(s,b)?ft((function(){return h})):s instanceof c.HTMLTemplateElement?s.content._appendChild(h):s._appendChild(h);for(var g=bt(a.ownerDocument,Se.indexOf(r));a.hasChildNodes();)g.elt._appendChild(a.firstChild);a._appendChild(g.elt),Se.remove(r),Se.replace(_t,g.elt,g.attrs),Te.removeElement(r);var v=Te.elements.lastIndexOf(a);Te.elements.splice(v+1,0,g.elt)}return!0}(t))return;break;case"applet":case"marquee":case"object":if(!Te.inScope(t))return;return Te.generateImpliedEndTags(),Te.popTag(t),void Se.clearToMarker();case"br":return void Jr(2,t,null)}for(a=Te.elements.length-1;a>=0;a--){if(J(s=Te.elements[a],t)){Te.generateImpliedEndTags(t),Te.popElement(s);break}if(J(s,f))return}return}}function en(e,t,r,n){switch(e){case 1:return void st(t);case-1:return Te.top instanceof c.HTMLScriptElement&&(Te.top._already_started=!0),Te.pop(),void(we=Ee)(e);case 3:return Te.pop(),void(we=Ee);default:return}}function tn(e,t,r,n){switch(e){case 1:if(Le)return void Jr(e,t,r,n);if(J(Te.top,b))return Re=[],Ee=we,void(we=rn)(e,t,r,n);break;case 4:return void ot(t);case 5:return;case 2:switch(t){case"caption":return Te.clearToContext(y),Se.insertMarker(),ut(t,r),void(we=nn);case"colgroup":return Te.clearToContext(y),ut(t,r),void(we=an);case"col":return tn(2,"colgroup",null),void we(e,t,r,n);case"tbody":case"tfoot":case"thead":return Te.clearToContext(y),ut(t,r),void(we=on);case"td":case"th":case"tr":return tn(2,"tbody",null),void we(e,t,r,n);case"table":if(!Te.inTableScope(t))return;return tn(3,t),void we(e,t,r,n);case"style":case"script":case"template":return void Xr(e,t,r,n);case"input":if("hidden"!==function(e){for(var t=0,r=e.length;t<r;t++)if("type"===e[t][0])return e[t][1].toLowerCase();return null}(r))break;return ut(t,r),void Te.pop();case"form":if(Ce||Te.contains("template"))return;return Ce=ut(t,r),void Te.popElement(Ce)}break;case 3:switch(t){case"table":if(!Te.inTableScope(t))return;return Te.popTag(t),void mt();case"body":case"caption":case"col":case"colgroup":case"html":case"tbody":case"td":case"tfoot":case"th":case"thead":case"tr":return;case"template":return void Xr(e,t,r,n)}break;case-1:return void Jr(e,t,r,n)}lt=!0,Jr(e,t,r,n),lt=!1}function rn(e,t,r,n){if(1===e){if(Me&&0===(t=t.replace(Q,"")).length)return;Re.push(t)}else{var i=Re.join("");Re.length=0,Y.test(i)?(lt=!0,Jr(1,i),lt=!1):st(i),(we=Ee)(e,t,r,n)}}function nn(e,t,r,n){function i(){return!!Te.inTableScope("caption")&&(Te.generateImpliedEndTags(),Te.popTag("caption"),Se.clearToMarker(),we=tn,!0)}switch(e){case 2:switch(t){case"caption":case"col":case"colgroup":case"tbody":case"td":case"tfoot":case"th":case"thead":case"tr":return void(i()&&we(e,t,r,n))}break;case 3:switch(t){case"caption":return void i();case"table":return void(i()&&we(e,t,r,n));case"body":case"col":case"colgroup":case"html":case"tbody":case"td":case"tfoot":case"th":case"thead":case"tr":return}}Jr(e,t,r,n)}function an(e,t,r,n){switch(e){case 1:var i=t.match(X);if(i&&(st(i[0]),t=t.substring(i[0].length)),0===t.length)return;break;case 4:return void ot(t);case 5:return;case 2:switch(t){case"html":return void Jr(e,t,r,n);case"col":return ut(t,r),void Te.pop();case"template":return void Xr(e,t,r,n)}break;case 3:switch(t){case"colgroup":if(!J(Te.top,"colgroup"))return;return Te.pop(),void(we=tn);case"col":return;case"template":return void Xr(e,t,r,n)}break;case-1:return void Jr(e,t,r,n)}J(Te.top,"colgroup")&&(an(3,"colgroup"),we(e,t,r,n))}function on(e,t,r,n){function i(){(Te.inTableScope("tbody")||Te.inTableScope("thead")||Te.inTableScope("tfoot"))&&(Te.clearToContext(w),on(3,Te.top.localName,null),we(e,t,r,n))}switch(e){case 2:switch(t){case"tr":return Te.clearToContext(w),ut(t,r),void(we=sn);case"th":case"td":return on(2,"tr",null),void we(e,t,r,n);case"caption":case"col":case"colgroup":case"tbody":case"tfoot":case"thead":return void i()}break;case 3:switch(t){case"table":return void i();case"tbody":case"tfoot":case"thead":return void(Te.inTableScope(t)&&(Te.clearToContext(w),Te.pop(),we=tn));case"body":case"caption":case"col":case"colgroup":case"html":case"td":case"th":case"tr":return}}tn(e,t,r,n)}function sn(e,t,r,n){function i(){return!!Te.inTableScope("tr")&&(Te.clearToContext(E),Te.pop(),we=on,!0)}switch(e){case 2:switch(t){case"th":case"td":return Te.clearToContext(E),ut(t,r),we=cn,void Se.insertMarker();case"caption":case"col":case"colgroup":case"tbody":case"tfoot":case"thead":case"tr":return void(i()&&we(e,t,r,n))}break;case 3:switch(t){case"tr":return void i();case"table":return void(i()&&we(e,t,r,n));case"tbody":case"tfoot":case"thead":return void(Te.inTableScope(t)&&i()&&we(e,t,r,n));case"body":case"caption":case"col":case"colgroup":case"html":case"td":case"th":return}}tn(e,t,r,n)}function cn(e,t,r,n){switch(e){case 2:switch(t){case"caption":case"col":case"colgroup":case"tbody":case"td":case"tfoot":case"th":case"thead":case"tr":return void(Te.inTableScope("td")?(cn(3,"td"),we(e,t,r,n)):Te.inTableScope("th")&&(cn(3,"th"),we(e,t,r,n)))}break;case 3:switch(t){case"td":case"th":if(!Te.inTableScope(t))return;return Te.generateImpliedEndTags(),Te.popTag(t),Se.clearToMarker(),void(we=sn);case"body":case"caption":case"col":case"colgroup":case"html":return;case"table":case"tbody":case"tfoot":case"thead":case"tr":if(!Te.inTableScope(t))return;return cn(3,Te.inTableScope("td")?"td":"th"),void we(e,t,r,n)}}Jr(e,t,r,n)}function ln(e,t,r,n){switch(e){case 1:if(Me&&0===(t=t.replace(Q,"")).length)return;return void st(t);case 4:return void ot(t);case 5:return;case-1:return void Jr(e,t,r,n);case 2:switch(t){case"html":return void Jr(e,t,r,n);case"option":return Te.top instanceof c.HTMLOptionElement&&ln(3,t),void ut(t,r);case"optgroup":return Te.top instanceof c.HTMLOptionElement&&ln(3,"option"),Te.top instanceof c.HTMLOptGroupElement&&ln(3,t),void ut(t,r);case"select":return void ln(3,t);case"input":case"keygen":case"textarea":if(!Te.inSelectScope("select"))return;return ln(3,"select"),void we(e,t,r,n);case"script":case"template":return void Xr(e,t,r,n)}break;case 3:switch(t){case"optgroup":return Te.top instanceof c.HTMLOptionElement&&Te.elements[Te.elements.length-2]instanceof c.HTMLOptGroupElement&&ln(3,"option"),void(Te.top instanceof c.HTMLOptGroupElement&&Te.pop());case"option":return void(Te.top instanceof c.HTMLOptionElement&&Te.pop());case"select":if(!Te.inSelectScope(t))return;return Te.popTag(t),void mt();case"template":return void Xr(e,t,r,n)}}}function un(e,t,r,n){switch(t){case"caption":case"table":case"tbody":case"tfoot":case"thead":case"tr":case"td":case"th":switch(e){case 2:return un(3,"select"),void we(e,t,r,n);case 3:return void(Te.inTableScope(t)&&(un(3,"select"),we(e,t,r,n)))}}ln(e,t,r,n)}function hn(e,t,r,n){function i(i){we=i,ke[ke.length-1]=we,we(e,t,r,n)}switch(e){case 1:case 4:case 5:return void Jr(e,t,r,n);case-1:return void(Te.contains("template")?(Te.popTag("template"),Se.clearToMarker(),ke.pop(),mt(),we(e,t,r,n)):yt());case 2:switch(t){case"base":case"basefont":case"bgsound":case"link":case"meta":case"noframes":case"script":case"style":case"template":case"title":return void Xr(e,t,r,n);case"caption":case"colgroup":case"tbody":case"tfoot":case"thead":return void i(tn);case"col":return void i(an);case"tr":return void i(on);case"td":case"th":return void i(sn)}return void i(Jr);case 3:switch(t){case"template":return void Xr(e,t,r,n);default:return}}}function dn(e,t,r,n){switch(e){case 1:if(Y.test(t))break;return void Jr(e,t);case 4:return void Te.elements[0]._appendChild(Be.createComment(t));case 5:return;case-1:return void yt();case 2:if("html"===t)return void Jr(e,t,r,n);break;case 3:if("html"===t){if(xe)return;return void(we=mn)}}(we=Jr)(e,t,r,n)}function pn(e,t,r,n){switch(e){case 1:return void((t=t.replace($,"")).length>0&&st(t));case 4:return void ot(t);case 5:return;case-1:return void yt();case 2:switch(t){case"html":return void Jr(e,t,r,n);case"frameset":return void ut(t,r);case"frame":return ut(t,r),void Te.pop();case"noframes":return void Xr(e,t,r,n)}break;case 3:if("frameset"===t){if(xe&&Te.top instanceof c.HTMLHtmlElement)return;return Te.pop(),void(xe||Te.top instanceof c.HTMLFrameSetElement||(we=fn))}}}function fn(e,t,r,n){switch(e){case 1:return void((t=t.replace($,"")).length>0&&st(t));case 4:return void ot(t);case 5:return;case-1:return void yt();case 2:switch(t){case"html":return void Jr(e,t,r,n);case"noframes":return void Xr(e,t,r,n)}break;case 3:if("html"===t)return void(we=gn)}}function mn(e,t,r,n){switch(e){case 1:if(Y.test(t))break;return void Jr(e,t,r,n);case 4:return void Be._appendChild(Be.createComment(t));case 5:return void Jr(e,t,r,n);case-1:return void yt();case 2:if("html"===t)return void Jr(e,t,r,n)}(we=Jr)(e,t,r,n)}function gn(e,t,r,n){switch(e){case 1:return void((t=t.replace($,"")).length>0&&Jr(e,t,r,n));case 4:return void Be._appendChild(Be.createComment(t));case 5:return void Jr(e,t,r,n);case-1:return void yt();case 2:switch(t){case"html":return void Jr(e,t,r,n);case"noframes":return void Xr(e,t,r,n)}}}function bn(e,r,n,i){var a,s;switch(e){case 1:return Oe&&K.test(r)&&(Oe=!1),Me&&(r=r.replace(Q,"�")),void st(r);case 4:return void ot(r);case 5:return;case 2:switch(r){case"font":if(!function(e){for(var t=0,r=e.length;t<r;t++)switch(e[t][0]){case"color":case"face":case"size":return!0}return!1}(n))break;case"b":case"big":case"blockquote":case"body":case"br":case"center":case"code":case"dd":case"div":case"dl":case"dt":case"em":case"embed":case"h1":case"h2":case"h3":case"h4":case"h5":case"h6":case"head":case"hr":case"i":case"img":case"li":case"listing":case"menu":case"meta":case"nobr":case"ol":case"p":case"pre":case"ruby":case"s":case"small":case"span":case"strong":case"strike":case"sub":case"sup":case"table":case"tt":case"u":case"ul":case"var":if(xe)break;do{Te.pop(),a=Te.top}while(a.namespaceURI!==o.HTML&&!ee(a)&&!te(a));return void at(e,r,n,i)}return(a=1===Te.elements.length&&xe?t:Te.top).namespaceURI===o.MATHML?ne(n):a.namespaceURI===o.SVG&&(r=(s=r)in O?O[s]:s,re(n)),ie(n),dt(r,n,a.namespaceURI),void(i&&("script"===r&&(a.namespaceURI,o.SVG),Te.pop()));case 3:if(a=Te.top,"script"===r&&a.namespaceURI===o.SVG&&"script"===a.localName)Te.pop();else for(var c=Te.elements.length-1,l=Te.elements[c];;){if(l.localName.toLowerCase()===r){Te.popElement(l);break}if((l=Te.elements[--c]).namespaceURI===o.HTML){we(e,r,n,i);break}}return}}return cr.lookahead=">",lr.lookahead=7,kr.lookahead=6,Ur.lookahead=-32,Pe.testTokenizer=function(e,t,r,n){var i=[];switch(t){case"PCDATA state":le=Et;break;case"RCDATA state":le=kt;break;case"RAWTEXT state":le=Tt;break;case"PLAINTEXT state":le=xt}if(r&&(he=r),at=function(e,t,r,n){switch(Je(),e){case 1:i.length>0&&"Character"===i[i.length-1][0]?i[i.length-1][1]+=t:i.push(["Character",t]);break;case 4:i.push(["Comment",t]);break;case 5:i.push(["DOCTYPE",t,void 0===r?null:r,void 0===n?null:n,!Ie]);break;case 2:for(var a=Object.create(null),o=0;o<r.length;o++){var s=r[o];1===s.length?a[s[0]]="":a[s[0]]=s[1]}var c=["StartTag",t,a];n&&c.push(!0),i.push(c);break;case 3:i.push(["EndTag",t])}},n){for(var a=0;a<e.length;a++)this.parse(e[a]);this.parse("",!0)}else this.parse(e,!0);return i},Pe}oe.ElementStack=function(){this.elements=[],this.top=null},oe.ElementStack.prototype.push=function(e){this.elements.push(e),this.top=e},oe.ElementStack.prototype.pop=function(e){this.elements.pop(),this.top=this.elements[this.elements.length-1]},oe.ElementStack.prototype.popTag=function(e){for(var t=this.elements.length-1;t>0;t--){if(J(this.elements[t],e))break}this.elements.length=t,this.top=this.elements[t-1]},oe.ElementStack.prototype.popElementType=function(e){for(var t=this.elements.length-1;t>0&&!(this.elements[t]instanceof e);t--);this.elements.length=t,this.top=this.elements[t-1]},oe.ElementStack.prototype.popElement=function(e){for(var t=this.elements.length-1;t>0&&this.elements[t]!==e;t--);this.elements.length=t,this.top=this.elements[t-1]},oe.ElementStack.prototype.removeElement=function(e){if(this.top===e)this.pop();else{var t=this.elements.lastIndexOf(e);-1!==t&&this.elements.splice(t,1)}},oe.ElementStack.prototype.clearToContext=function(e){for(var t=this.elements.length-1;t>0&&!J(this.elements[t],e);t--);this.elements.length=t+1,this.top=this.elements[t]},oe.ElementStack.prototype.contains=function(e){return this.inSpecificScope(e,Object.create(null))},oe.ElementStack.prototype.inSpecificScope=function(e,t){for(var r=this.elements.length-1;r>=0;r--){var n=this.elements[r];if(J(n,e))return!0;if(J(n,t))return!1}return!1},oe.ElementStack.prototype.elementInSpecificScope=function(e,t){for(var r=this.elements.length-1;r>=0;r--){var n=this.elements[r];if(n===e)return!0;if(J(n,t))return!1}return!1},oe.ElementStack.prototype.elementTypeInSpecificScope=function(e,t){for(var r=this.elements.length-1;r>=0;r--){var n=this.elements[r];if(n instanceof e)return!0;if(J(n,t))return!1}return!1},oe.ElementStack.prototype.inScope=function(e){return this.inSpecificScope(e,T)},oe.ElementStack.prototype.elementInScope=function(e){return this.elementInSpecificScope(e,T)},oe.ElementStack.prototype.elementTypeInScope=function(e){return this.elementTypeInSpecificScope(e,T)},oe.ElementStack.prototype.inButtonScope=function(e){return this.inSpecificScope(e,x)},oe.ElementStack.prototype.inListItemScope=function(e){return this.inSpecificScope(e,S)},oe.ElementStack.prototype.inTableScope=function(e){return this.inSpecificScope(e,N)},oe.ElementStack.prototype.inSelectScope=function(e){for(var t=this.elements.length-1;t>=0;t--){var r=this.elements[t];if(r.namespaceURI!==o.HTML)return!1;var n=r.localName;if(n===e)return!0;if("optgroup"!==n&&"option"!==n)return!1}return!1},oe.ElementStack.prototype.generateImpliedEndTags=function(e,t){for(var r=t?_:v,n=this.elements.length-1;n>=0;n--){var i=this.elements[n];if(e&&J(i,e))break;if(!J(this.elements[n],r))break}this.elements.length=n+1,this.top=this.elements[n]},oe.ActiveFormattingElements=function(){this.list=[],this.attrs=[]},oe.ActiveFormattingElements.prototype.MARKER={localName:"|"},oe.ActiveFormattingElements.prototype.insertMarker=function(){this.list.push(this.MARKER),this.attrs.push(this.MARKER)},oe.ActiveFormattingElements.prototype.push=function(e,t){for(var r=0,n=this.list.length-1;n>=0&&this.list[n]!==this.MARKER;n--)if(o(e,this.list[n],this.attrs[n])&&3===++r){this.list.splice(n,1),this.attrs.splice(n,1);break}this.list.push(e);for(var i=[],a=0;a<t.length;a++)i[a]=t[a];function o(e,t,r){if(e.localName!==t.localName)return!1;if(e._numattrs!==r.length)return!1;for(var n=0,i=r.length;n<i;n++){var a=r[n][0],o=r[n][1];if(!e.hasAttribute(a))return!1;if(e.getAttribute(a)!==o)return!1}return!0}this.attrs.push(i)},oe.ActiveFormattingElements.prototype.clearToMarker=function(){for(var e=this.list.length-1;e>=0&&this.list[e]!==this.MARKER;e--);e<0&&(e=0),this.list.length=e,this.attrs.length=e},oe.ActiveFormattingElements.prototype.findElementByTag=function(e){for(var t=this.list.length-1;t>=0;t--){var r=this.list[t];if(r===this.MARKER)break;if(r.localName===e)return r}return null},oe.ActiveFormattingElements.prototype.indexOf=function(e){return this.list.lastIndexOf(e)},oe.ActiveFormattingElements.prototype.remove=function(e){var t=this.list.lastIndexOf(e);-1!==t&&(this.list.splice(t,1),this.attrs.splice(t,1))},oe.ActiveFormattingElements.prototype.replace=function(e,t,r){var n=this.list.lastIndexOf(e);-1!==n&&(this.list[n]=t,this.attrs[n]=r)},oe.ActiveFormattingElements.prototype.insertAfter=function(e,t){var r=this.list.lastIndexOf(e);-1!==r&&(this.list.splice(r,0,t),this.attrs.splice(r,0,t))}},function(e,t,r){"use strict";var n=r(3),i=r(23),a=r(0);function o(){}e.exports=o,o.prototype={addEventListener:function(e,t,r){if(t){void 0===r&&(r=!1),this._listeners||(this._listeners=Object.create(null)),this._listeners[e]||(this._listeners[e]=[]);for(var n=this._listeners[e],i=0,a=n.length;i<a;i++){var o=n[i];if(o.listener===t&&o.capture===r)return}var s={listener:t,capture:r};"function"==typeof t&&(s.f=t),n.push(s)}},removeEventListener:function(e,t,r){if(void 0===r&&(r=!1),this._listeners){var n=this._listeners[e];if(n)for(var i=0,a=n.length;i<a;i++){var o=n[i];if(o.listener===t&&o.capture===r)return void(1===n.length?this._listeners[e]=void 0:n.splice(i,1))}}},dispatchEvent:function(e){return this._dispatchEvent(e,!1)},_dispatchEvent:function(e,t){function r(e,t){var r=t.type,i=t.eventPhase;if(t.currentTarget=e,i!==n.CAPTURING_PHASE&&e._handlers&&e._handlers[r]){var a,o=e._handlers[r];if("function"==typeof o)a=o.call(t.currentTarget,t);else{var s=o.handleEvent;if("function"!=typeof s)throw new TypeError("handleEvent property of event handler object isnot a function.");a=s.call(o,t)}switch(t.type){case"mouseover":!0===a&&t.preventDefault();break;case"beforeunload":default:!1===a&&t.preventDefault()}}var c=e._listeners&&e._listeners[r];if(c)for(var l=0,u=(c=c.slice()).length;l<u;l++){if(t._immediatePropagationStopped)return;var h=c[l];if(!(i===n.CAPTURING_PHASE&&!h.capture||i===n.BUBBLING_PHASE&&h.capture))if(h.f)h.f.call(t.currentTarget,t);else{var d=h.listener.handleEvent;if("function"!=typeof d)throw new TypeError("handleEvent property of event listener object is not a function.");d.call(h.listener,t)}}}"boolean"!=typeof t&&(t=!1),e._initialized&&!e._dispatching||a.InvalidStateError(),e.isTrusted=t,e._dispatching=!0,e.target=this;for(var o=[],s=this.parentNode;s;s=s.parentNode)o.push(s);e.eventPhase=n.CAPTURING_PHASE;for(var c=o.length-1;c>=0&&(r(o[c],e),!e._propagationStopped);c--);if(e._propagationStopped||(e.eventPhase=n.AT_TARGET,r(this,e)),e.bubbles&&!e._propagationStopped){e.eventPhase=n.BUBBLING_PHASE;for(var l=0,u=o.length;l<u&&(r(o[l],e),!e._propagationStopped);l++);}if(e._dispatching=!1,e.eventPhase=n.AT_TARGET,e.currentTarget=null,t&&!e.defaultPrevented&&e instanceof i)switch(e.type){case"mousedown":this._armed={x:e.clientX,y:e.clientY,t:e.timeStamp};break;case"mouseout":case"mouseover":this._armed=null;break;case"mouseup":this._isClick(e)&&this._doClick(e),this._armed=null}return!e.defaultPrevented},_isClick:function(e){return null!==this._armed&&"mouseup"===e.type&&e.isTrusted&&0===e.button&&e.timeStamp-this._armed.t<1e3&&Math.abs(e.clientX-this._armed.x)<10&&Math.abs(e.clientY-this._armed.Y)<10},_doClick:function(e){if(!this._click_in_progress){this._click_in_progress=!0;for(var t=this;t&&!t._post_click_activation_steps;)t=t.parentNode;t&&t._pre_click_activation_steps&&t._pre_click_activation_steps();var r=this.ownerDocument.createEvent("MouseEvent");r.initMouseEvent("click",!0,!0,this.ownerDocument.defaultView,1,e.screenX,e.screenY,e.clientX,e.clientY,e.ctrlKey,e.altKey,e.shiftKey,e.metaKey,e.button,null);var n=this._dispatchEvent(r,!0);t&&(n?t._post_click_activation_steps&&t._post_click_activation_steps(r):t._cancelled_activation_steps&&t._cancelled_activation_steps())}},_setEventHandler:function(e,t){this._handlers||(this._handlers=Object.create(null)),this._handlers[e]=t},_getEventHandler:function(e){return this._handlers&&this._handlers[e]||null}}},function(e,t,r){"use strict";var n=r(24);function i(){n.call(this),this.screenX=this.screenY=this.clientX=this.clientY=0,this.ctrlKey=this.altKey=this.shiftKey=this.metaKey=!1,this.button=0,this.buttons=1,this.relatedTarget=null}e.exports=i,i.prototype=Object.create(n.prototype,{constructor:{value:i},initMouseEvent:{value:function(e,t,r,n,i,a,o,s,c,l,u,h,d,p,f){switch(this.initEvent(e,t,r,n,i),this.screenX=a,this.screenY=o,this.clientX=s,this.clientY=c,this.ctrlKey=l,this.altKey=u,this.shiftKey=h,this.metaKey=d,this.button=p,p){case 0:this.buttons=1;break;case 1:this.buttons=4;break;case 2:this.buttons=2;break;default:this.buttons=0}this.relatedTarget=f}},getModifierState:{value:function(e){switch(e){case"Alt":return this.altKey;case"Control":return this.ctrlKey;case"Shift":return this.shiftKey;case"Meta":return this.metaKey;default:return!1}}}})},function(e,t,r){"use strict";var n=r(3);function i(){n.call(this),this.view=null,this.detail=0}e.exports=i,i.prototype=Object.create(n.prototype,{constructor:{value:i},initUIEvent:{value:function(e,t,r,n,i){this.initEvent(e,t,r),this.view=n,this.detail=i}}})},function(e,t,r){"use strict";var n=r(0),i=e.exports={valid:function(e){return n.assert(e,"list falsy"),n.assert(e._previousSibling,"previous falsy"),n.assert(e._nextSibling,"next falsy"),!0},insertBefore:function(e,t){n.assert(i.valid(e)&&i.valid(t));var r=e,a=e._previousSibling,o=t,s=t._previousSibling;r._previousSibling=s,a._nextSibling=o,s._nextSibling=r,o._previousSibling=a,n.assert(i.valid(e)&&i.valid(t))},replace:function(e,t){n.assert(i.valid(e)&&(null===t||i.valid(t))),null!==t&&i.insertBefore(t,e),i.remove(e),n.assert(i.valid(e)&&(null===t||i.valid(t)))},remove:function(e){n.assert(i.valid(e));var t=e._previousSibling;if(t!==e){var r=e._nextSibling;t._nextSibling=r,r._previousSibling=t,e._previousSibling=e._nextSibling=e,n.assert(i.valid(e))}}}},function(e,t,r){"use strict";e.exports={serializeOne:function(e,t){var r="";switch(e.nodeType){case 1:var u=e.namespaceURI,h=u===i.HTML,d=h||u===i.SVG||u===i.MATHML?e.localName:e.tagName;r+="<"+d;for(var p=0,f=e._numattrs;p<f;p++){var m=e._attr(p);r+=" "+l(m),void 0!==m.value&&(r+='="'+c(m.value)+'"')}if(r+=">",!h||!o[d]){var g=e.serialize();h&&s[d]&&"\n"===g.charAt(0)&&(r+="\n"),r+=g,r+="</"+d+">"}break;case 3:case 4:var b;b=1===t.nodeType&&t.namespaceURI===i.HTML?t.tagName:"",a[b]||"NOSCRIPT"===b&&t.ownerDocument._scripting_enabled?r+=e.data:r+=function(e){return e.replace(/[&<>\u00A0]/g,(function(e){switch(e){case"&":return"&amp;";case"<":return"&lt;";case">":return"&gt;";case" ":return"&nbsp;"}}))}(e.data);break;case 8:r+="\x3c!--"+e.data+"--\x3e";break;case 7:r+="<?"+e.target+" "+e.data+"?>";break;case 10:r+="<!DOCTYPE "+e.name,r+=">";break;default:n.InvalidStateError()}return r}};var n=r(0),i=n.NAMESPACE,a={STYLE:!0,SCRIPT:!0,XMP:!0,IFRAME:!0,NOEMBED:!0,NOFRAMES:!0,PLAINTEXT:!0},o={area:!0,base:!0,basefont:!0,bgsound:!0,br:!0,col:!0,embed:!0,frame:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0},s={};function c(e){var t=/[&"\u00A0]/g;return t.test(e)?e.replace(t,(function(e){switch(e){case"&":return"&amp;";case'"':return"&quot;";case" ":return"&nbsp;"}})):e}function l(e){var t=e.namespaceURI;return t?t===i.XML?"xml:"+e.localName:t===i.XLINK?"xlink:"+e.localName:t===i.XMLNS?"xmlns"===e.localName?"xmlns":"xmlns:"+e.localName:e.name:e.localName}},function(e,t,r){"use strict";var n=r(0);t.property=function(e){if(Array.isArray(e.type)){var t=Object.create(null);e.type.forEach((function(e){t[e.value||e]=e.alias||e}));var r=e.missing;void 0===r&&(r=null);var i=e.invalid;return void 0===i&&(i=r),{get:function(){var n=this._getattr(e.name);return null===n?r:void 0!==(n=t[n.toLowerCase()])?n:null!==i?i:n},set:function(t){this._setattr(e.name,t)}}}if(e.type===Boolean)return{get:function(){return this.hasAttribute(e.name)},set:function(t){t?this._setattr(e.name,""):this.removeAttribute(e.name)}};if(e.type===Number||"long"===e.type||"unsigned long"===e.type||"limited unsigned long with fallback"===e.type)return function(e){var t;t="function"==typeof e.default?e.default:"number"==typeof e.default?function(){return e.default}:function(){n.assert(!1,typeof e.default)};var r="unsigned long"===e.type,i="long"===e.type,a="limited unsigned long with fallback"===e.type,o=e.min,s=e.max,c=e.setmin;void 0===o&&(r&&(o=0),i&&(o=-2147483648),a&&(o=1));void 0===s&&(r||i||a)&&(s=2147483647);return{get:function(){var n=this._getattr(e.name),c=e.float?parseFloat(n):parseInt(n,10);if(null===n||!isFinite(c)||void 0!==o&&c<o||void 0!==s&&c>s)return t.call(this);if(r||i||a){if(!/^[ \t\n\f\r]*[-+]?[0-9]/.test(n))return t.call(this);c|=0}return c},set:function(o){e.float||(o=Math.floor(o)),void 0!==c&&o<c&&n.IndexSizeError(e.name+" set to "+o),r?o=o<0||o>2147483647?t.call(this):0|o:a?o=o<1||o>2147483647?t.call(this):0|o:i&&(o=o<-2147483648||o>2147483647?t.call(this):0|o),this._setattr(e.name,String(o))}}}(e);if(!e.type||e.type===String)return{get:function(){return this._getattr(e.name)||""},set:function(t){e.treatNullAsEmptyString&&null===t&&(t=""),this._setattr(e.name,t)}};if("function"==typeof e.type)return e.type(e.name,e);throw new Error("Invalid attribute definition")},t.registerChangeHandler=function(e,t,r){var n=e.prototype;Object.prototype.hasOwnProperty.call(n,"_attributeChangeHandlers")||(n._attributeChangeHandlers=Object.create(n._attributeChangeHandlers||null)),n._attributeChangeHandlers[t]=r}},function(e,t,r){"use strict";var n=r(0);function i(e,t){this._getString=e,this._setString=t,this._length=0,this._lastStringValue="",this._update()}function a(e,t){var r,n=e._length;for(e._length=t.length,r=0;r<t.length;r++)e[r]=t[r];for(;r<n;r++)e[r]=void 0}function o(e){return""===(e=String(e))&&n.SyntaxError(),/[ \t\r\n\f]/.test(e)&&n.InvalidCharacterError(),e}function s(e){var t=e._getString();if(t===e._lastStringValue)return function(e){for(var t=e._length,r=Array(t),n=0;n<t;n++)r[n]=e[n];return r}(e);var r=t.replace(/(^[ \t\r\n\f]+)|([ \t\r\n\f]+$)/g,"");if(""===r)return[];var n=Object.create(null);return r.split(/[ \t\r\n\f]+/g).filter((function(e){var t="$"+e;return!n[t]&&(n[t]=!0,!0)}))}e.exports=i,Object.defineProperties(i.prototype,{length:{get:function(){return this._length}},item:{value:function(e){var t=s(this);return e<0||e>=t.length?null:t[e]}},contains:{value:function(e){return e=String(e),s(this).indexOf(e)>-1}},add:{value:function(){for(var e=s(this),t=0,r=arguments.length;t<r;t++){var n=o(arguments[t]);e.indexOf(n)<0&&e.push(n)}this._update(e)}},remove:{value:function(){for(var e=s(this),t=0,r=arguments.length;t<r;t++){var n=o(arguments[t]),i=e.indexOf(n);i>-1&&e.splice(i,1)}this._update(e)}},toggle:{value:function(e,t){return e=o(e),this.contains(e)?void 0!==t&&!1!==t||(this.remove(e),!1):(void 0===t||!0===t)&&(this.add(e),!0)}},replace:{value:function(e,t){""===String(t)&&n.SyntaxError(),e=o(e),t=o(t);var r=s(this),i=r.indexOf(e);if(i<0)return!1;var a=r.indexOf(t);return a<0?r[i]=t:i<a?(r[i]=t,r.splice(a,1)):r.splice(i,1),this._update(r),!0}},toString:{value:function(){return this._getString()}},value:{get:function(){return this._getString()},set:function(e){this._setString(e),this._update()}},_update:{value:function(e){e?(a(this,e),this._setString(e.join(" ").trim())):a(this,s(this)),this._lastStringValue=this._getString()}}})},function(e,t,r){"use strict";var n=r(1),i={nextElementSibling:{get:function(){if(this.parentNode)for(var e=this.nextSibling;null!==e;e=e.nextSibling)if(e.nodeType===n.ELEMENT_NODE)return e;return null}},previousElementSibling:{get:function(){if(this.parentNode)for(var e=this.previousSibling;null!==e;e=e.previousSibling)if(e.nodeType===n.ELEMENT_NODE)return e;return null}}};e.exports=i},function(e,t,r){"use strict";e.exports=i;var n=r(0);function i(e){this.element=e}Object.defineProperties(i.prototype,{length:{get:n.shouldOverride},item:{value:n.shouldOverride},getNamedItem:{value:function(e){return this.element.getAttributeNode(e)}},getNamedItemNS:{value:function(e,t){return this.element.getAttributeNodeNS(e,t)}},setNamedItem:{value:n.nyi},setNamedItemNS:{value:n.nyi},removeNamedItem:{value:function(e){var t=this.element.getAttributeNode(e);if(t)return this.element.removeAttribute(e),t;n.NotFoundError()}},removeNamedItemNS:{value:function(e,t){var r=this.element.getAttributeNodeNS(e,t);if(r)return this.element.removeAttributeNS(e,t),r;n.NotFoundError()}}})},function(e,t,r){"use strict";e.exports=o;var n=r(0),i=r(1),a=r(6);function o(e,t){a.call(this),this.nodeType=i.TEXT_NODE,this.ownerDocument=e,this._data=t,this._index=void 0}var s={get:function(){return this._data},set:function(e){(e=null==e?"":String(e))!==this._data&&(this._data=e,this.rooted&&this.ownerDocument.mutateValue(this),this.parentNode&&this.parentNode._textchangehook&&this.parentNode._textchangehook(this))}};o.prototype=Object.create(a.prototype,{nodeName:{value:"#text"},nodeValue:s,textContent:s,data:{get:s.get,set:function(e){s.set.call(this,null===e?"":String(e))}},splitText:{value:function(e){(e>this._data.length||e<0)&&n.IndexSizeError();var t=this._data.substring(e),r=this.ownerDocument.createTextNode(t);this.data=this.data.substring(0,e);var i=this.parentNode;return null!==i&&i.insertBefore(r,this.nextSibling),r}},wholeText:{get:function(){for(var e=this.textContent,t=this.nextSibling;t&&t.nodeType===i.TEXT_NODE;t=t.nextSibling)e+=t.textContent;return e}},replaceWholeText:{value:n.nyi},clone:{value:function(){return new o(this.ownerDocument,this._data)}}})},function(e,t,r){"use strict";e.exports=c;var n=r(1),i=r(2),a=r(0),o=a.HierarchyRequestError,s=a.NotFoundError;function c(){n.call(this)}c.prototype=Object.create(n.prototype,{hasChildNodes:{value:function(){return!1}},firstChild:{value:null},lastChild:{value:null},insertBefore:{value:function(e,t){if(!e.nodeType)throw new TypeError("not a node");o()}},replaceChild:{value:function(e,t){if(!e.nodeType)throw new TypeError("not a node");o()}},removeChild:{value:function(e){if(!e.nodeType)throw new TypeError("not a node");s()}},removeChildren:{value:function(){}},childNodes:{get:function(){return this._childNodes||(this._childNodes=new i),this._childNodes}}})},function(e,t,r){"use strict";e.exports=a;var n=r(1),i=r(6);function a(e,t){i.call(this),this.nodeType=n.COMMENT_NODE,this.ownerDocument=e,this._data=t}var o={get:function(){return this._data},set:function(e){e=null==e?"":String(e),this._data=e,this.rooted&&this.ownerDocument.mutateValue(this)}};a.prototype=Object.create(i.prototype,{nodeName:{value:"#comment"},nodeValue:o,textContent:o,data:{get:o.get,set:function(e){o.set.call(this,null===e?"":String(e))}},clone:{value:function(){return new a(this.ownerDocument,this._data)}}})},function(e,t,r){"use strict";e.exports=l;var n=r(1),i=r(2),a=r(12),o=r(4),s=r(14),c=r(0);function l(e){a.call(this),this.nodeType=n.DOCUMENT_FRAGMENT_NODE,this.ownerDocument=e}l.prototype=Object.create(a.prototype,{nodeName:{value:"#document-fragment"},nodeValue:{get:function(){return null},set:function(){}},textContent:Object.getOwnPropertyDescriptor(o.prototype,"textContent"),querySelector:{value:function(e){var t=this.querySelectorAll(e);return t.length?t[0]:null}},querySelectorAll:{value:function(e){var t=Object.create(this);t.isHTML=!0,t.getElementsByTagName=o.prototype.getElementsByTagName,t.nextElement=Object.getOwnPropertyDescriptor(o.prototype,"firstElementChild").get;var r=s(e,t);return r.item?r:new i(r)}},clone:{value:function(){return new l(this.ownerDocument)}},isEqual:{value:function(e){return!0}},innerHTML:{get:function(){return this.serialize()},set:c.nyi},outerHTML:{get:function(){return this.serialize()},set:c.nyi}})},function(e,t,r){"use strict";e.exports=a;var n=r(1),i=r(6);function a(e,t,r){i.call(this),this.nodeType=n.PROCESSING_INSTRUCTION_NODE,this.ownerDocument=e,this.target=t,this._data=r}var o={get:function(){return this._data},set:function(e){e=null==e?"":String(e),this._data=e,this.rooted&&this.ownerDocument.mutateValue(this)}};a.prototype=Object.create(i.prototype,{nodeName:{get:function(){return this.target}},nodeValue:o,textContent:o,data:{get:o.get,set:function(e){o.set.call(this,null===e?"":String(e))}},clone:{value:function(){return new a(this.ownerDocument,this.target,this._data)}},isEqual:{value:function(e){return this.target===e.target&&this._data===e._data}}})},function(e,t,r){"use strict";e.exports={nextSkippingChildren:function(e,t){if(e===t)return null;if(null!==e.nextSibling)return e.nextSibling;return n(e,t)},nextAncestorSibling:n,next:function(e,t){var r;if(null!==(r=e.firstChild))return r;if(e===t)return null;if(null!==(r=e.nextSibling))return r;return n(e,t)},previous:function(e,t){var r;if(null!==(r=e.previousSibling))return i(r);if((r=e.parentNode)===t)return null;return r},deepLastChild:i};function n(e,t){for(e=e.parentNode;null!==e;e=e.parentNode){if(e===t)return null;if(null!==e.nextSibling)return e.nextSibling}return null}function i(e){for(;e.lastChild;)e=e.lastChild;return e}},function(e,t,r){"use strict";e.exports={Event:r(3),UIEvent:r(24),MouseEvent:r(23),CustomEvent:r(57)}},function(e,t,r){"use strict";var n=r(16);function i(){}e.exports=i,i.prototype=Object.create(Object.prototype,{_url:{get:function(){return new n(this.href)}},protocol:{get:function(){var e=this._url;return e&&e.scheme?e.scheme+":":":"},set:function(e){var t=this.href,r=new n(t);r.isAbsolute()&&(e=(e=e.replace(/:+$/,"")).replace(/[^-+\.a-zA-Z0-9]/g,n.percentEncode)).length>0&&(r.scheme=e,t=r.toString()),this.href=t}},host:{get:function(){var e=this._url;return e.isAbsolute()&&e.isAuthorityBased()?e.host+(e.port?":"+e.port:""):""},set:function(e){var t=this.href,r=new n(t);r.isAbsolute()&&r.isAuthorityBased()&&(e=e.replace(/[^-+\._~!$&'()*,;:=a-zA-Z0-9]/g,n.percentEncode)).length>0&&(r.host=e,delete r.port,t=r.toString()),this.href=t}},hostname:{get:function(){var e=this._url;return e.isAbsolute()&&e.isAuthorityBased()?e.host:""},set:function(e){var t=this.href,r=new n(t);r.isAbsolute()&&r.isAuthorityBased()&&(e=(e=e.replace(/^\/+/,"")).replace(/[^-+\._~!$&'()*,;:=a-zA-Z0-9]/g,n.percentEncode)).length>0&&(r.host=e,t=r.toString()),this.href=t}},port:{get:function(){var e=this._url;return e.isAbsolute()&&e.isAuthorityBased()&&void 0!==e.port?e.port:""},set:function(e){var t=this.href,r=new n(t);r.isAbsolute()&&r.isAuthorityBased()&&(0===(e=(e=(e=""+e).replace(/[^0-9].*$/,"")).replace(/^0+/,"")).length&&(e="0"),parseInt(e,10)<=65535&&(r.port=e,t=r.toString())),this.href=t}},pathname:{get:function(){var e=this._url;return e.isAbsolute()&&e.isHierarchical()?e.path:""},set:function(e){var t=this.href,r=new n(t);r.isAbsolute()&&r.isHierarchical()&&("/"!==e.charAt(0)&&(e="/"+e),e=e.replace(/[^-+\._~!$&'()*,;:=@\/a-zA-Z0-9]/g,n.percentEncode),r.path=e,t=r.toString()),this.href=t}},search:{get:function(){var e=this._url;return e.isAbsolute()&&e.isHierarchical()&&void 0!==e.query?"?"+e.query:""},set:function(e){var t=this.href,r=new n(t);r.isAbsolute()&&r.isHierarchical()&&("?"===e.charAt(0)&&(e=e.substring(1)),e=e.replace(/[^-+\._~!$&'()*,;:=@\/?a-zA-Z0-9]/g,n.percentEncode),r.query=e,t=r.toString()),this.href=t}},hash:{get:function(){var e=this._url;return null==e||null==e.fragment||""===e.fragment?"":"#"+e.fragment},set:function(e){var t=this.href,r=new n(t);"#"===e.charAt(0)&&(e=e.substring(1)),e=e.replace(/[^-+\._~!$&'()*,;:=@\/?a-zA-Z0-9]/g,n.percentEncode),r.fragment=e,t=r.toString(),this.href=t}},username:{get:function(){return this._url.username||""},set:function(e){var t=this.href,r=new n(t);r.isAbsolute()&&(e=e.replace(/[\x00-\x1F\x7F-\uFFFF "#<>?`\/@\\:]/g,n.percentEncode),r.username=e,t=r.toString()),this.href=t}},password:{get:function(){return this._url.password||""},set:function(e){var t=this.href,r=new n(t);r.isAbsolute()&&(""===e?r.password=null:(e=e.replace(/[\x00-\x1F\x7F-\uFFFF "#<>?`\/@\\]/g,n.percentEncode),r.password=e),t=r.toString()),this.href=t}},origin:{get:function(){var e=this._url;if(null==e)return"";var t=function(t){var r=[e.scheme,e.host,+e.port||t];return r[0]+"://"+r[1]+(r[2]===t?"":":"+r[2])};switch(e.scheme){case"ftp":return t(21);case"gopher":return t(70);case"http":case"ws":return t(80);case"https":case"wss":return t(443);default:return e.scheme+"://"}}}}),i._inherit=function(e){Object.getOwnPropertyNames(i.prototype).forEach((function(t){if("constructor"!==t&&"href"!==t){var r=Object.getOwnPropertyDescriptor(i.prototype,t);Object.defineProperty(e,t,r)}}))}},function(e,t,r){"use strict";var n=r(27),i=r(40),a=r(11).isApiWritable;function o(e,t,r,n){this.body=e,this.document=t,this.form=r,this.element=n}function s(e,t,r,n){var i=e.ownerDocument||Object.create(null),a=e.form||Object.create(null);e[t]=new o(n,i,a,e).build()}e.exports=function(e,t,r,i){var o=e.ctor;if(o){var c=e.props||{};if(e.attributes)for(var l in e.attributes){var u=e.attributes[l];("object"!=typeof u||Array.isArray(u))&&(u={type:u}),u.name||(u.name=l.toLowerCase()),c[l]=n.property(u)}c.constructor={value:o,writable:a},o.prototype=Object.create((e.superclass||t).prototype,c),e.events&&function(e,t){var r=e.prototype;t.forEach((function(t){Object.defineProperty(r,"on"+t,{get:function(){return this._getEventHandler(t)},set:function(e){this._setEventHandler(t,e)}}),n.registerChangeHandler(e,"on"+t,s)}))}(o,e.events),r[o.name]=o}else o=t;return(e.tags||e.tag&&[e.tag]||[]).forEach((function(e){i[e]=o})),o},o.prototype.build=i.EventHandlerBuilder_build},function(module,exports){module.exports={Window_run:function _run(code,file){with(file&&(code+="\n//@ sourceURL="+file),this)eval(code)},EventHandlerBuilder_build:function build(){try{with(this.document.defaultView||Object.create(null))with(this.document)with(this.form)with(this.element)return eval("(function(event){"+this.body+"})")}catch(err){return function(){throw err}}}}},function(e,t,r){"use strict";var n=r(0);t=e.exports={CSSStyleDeclaration:r(18),CharacterData:r(6),Comment:r(33),DOMException:r(10),DOMImplementation:r(5),DOMTokenList:r(28),Document:r(9),DocumentFragment:r(34),DocumentType:r(20),Element:r(4),HTMLParser:r(21),NamedNodeMap:r(30),Node:r(1),NodeList:r(2),NodeFilter:r(7),ProcessingInstruction:r(35),Text:r(31),Window:r(19)},n.merge(t,r(37)),n.merge(t,r(17).elements),n.merge(t,r(42).elements)},function(e,t,r){"use strict";var n=r(4),i=r(39),a=r(0),o=r(18),s=t.elements={},c=Object.create(null);function l(e){return i(e,u,s,c)}t.createElement=function(e,t,r){return new(c[t]||u)(e,t,r)};var u=l({superclass:n,ctor:function(e,t,r){n.call(this,e,t,a.NAMESPACE.SVG,r)},props:{style:{get:function(){return this._style||(this._style=new o(this)),this._style}}}});l({ctor:function(e,t,r){u.call(this,e,t,r)},tag:"svg",props:{createSVGRect:{value:function(){return t.createElement(this.ownerDocument,"rect",null)}}}}),l({tags:["a","altGlyph","altGlyphDef","altGlyphItem","animate","animateColor","animateMotion","animateTransform","circle","clipPath","color-profile","cursor","defs","desc","ellipse","feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence","filter","font","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignObject","g","glyph","glyphRef","hkern","image","line","linearGradient","marker","mask","metadata","missing-glyph","mpath","path","pattern","polygon","polyline","radialGradient","rect","script","set","stop","style","switch","symbol","text","textPath","title","tref","tspan","use","view","vkern"]})},function(e,t,r){"use strict";var n=this&&this.__awaiter||function(e,t,r,n){return new(r||(r=Promise))((function(i,a){function o(e){try{c(n.next(e))}catch(e){a(e)}}function s(e){try{c(n.throw(e))}catch(e){a(e)}}function c(e){var t;e.done?i(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(o,s)}c((n=n.apply(e,t||[])).next())}))};Object.defineProperty(t,"__esModule",{value:!0});const i=r(8),a=r(44),o=r(49);i.default.plugins.register({onStart:function(){return n(this,void 0,void 0,(function*(){console.info("Paste Special plugin started!"),yield o.default(),yield a.default()}))}})},function(e,t,r){"use strict";var n=this&&this.__awaiter||function(e,t,r,n){return new(r||(r=Promise))((function(i,a){function o(e){try{c(n.next(e))}catch(e){a(e)}}function s(e){try{c(n.throw(e))}catch(e){a(e)}}function c(e){var t;e.done?i(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(o,s)}c((n=n.apply(e,t||[])).next())}))};Object.defineProperty(t,"__esModule",{value:!0});const i=r(8),a=r(45),o=r(46);t.default=function(){return n(this,void 0,void 0,(function*(){yield i.default.commands.register({name:"pasteCsvAsTable",label:"CSV as Table",execute:()=>n(this,void 0,void 0,(function*(){let e=yield i.default.clipboard.readText();if(!(null==e?void 0:e.length))return;e=e.trim();const t=yield o.default(e);yield i.default.commands.execute("insertText",t),yield i.default.commands.execute("editor.focus")}))}),yield i.default.views.menus.create("pasteSpecial","Paste Special",[{label:"CSV as Table",commandName:"pasteCsvAsTable",accelerator:"Cmd+Shift+V"},{label:"HTML as Markdown",commandName:"pasteHtmlAsMarkdown",accelerator:"Alt+Shift+V"}],a.MenuItemLocation.Edit)}))}},function(e,t,r){"use strict";var n;Object.defineProperty(t,"__esModule",{value:!0}),t.ContentScriptType=t.SettingItemType=t.ToolbarButtonLocation=t.isContextMenuItemLocation=t.MenuItemLocation=t.ImportModuleOutputFormat=t.FileSystemItem=void 0,function(e){e.File="file",e.Directory="directory"}(t.FileSystemItem||(t.FileSystemItem={})),function(e){e.Markdown="md",e.Html="html"}(t.ImportModuleOutputFormat||(t.ImportModuleOutputFormat={})),function(e){e.File="file",e.Edit="edit",e.View="view",e.Note="note",e.Tools="tools",e.Help="help",e.Context="context",e.NoteListContextMenu="noteListContextMenu",e.EditorContextMenu="editorContextMenu",e.FolderContextMenu="folderContextMenu",e.TagContextMenu="tagContextMenu"}(n=t.MenuItemLocation||(t.MenuItemLocation={})),t.isContextMenuItemLocation=function(e){return[n.Context,n.NoteListContextMenu,n.EditorContextMenu,n.FolderContextMenu,n.TagContextMenu].includes(e)},function(e){e.NoteToolbar="noteToolbar",e.EditorToolbar="editorToolbar"}(t.ToolbarButtonLocation||(t.ToolbarButtonLocation={})),function(e){e[e.Int=1]="Int",e[e.String=2]="String",e[e.Bool=3]="Bool",e[e.Array=4]="Array",e[e.Object=5]="Object",e[e.Button=6]="Button"}(t.SettingItemType||(t.SettingItemType={})),function(e){e.MarkdownItPlugin="markdownItPlugin",e.CodeMirrorPlugin="codeMirrorPlugin"}(t.ContentScriptType||(t.ContentScriptType={}))},function(e,t,r){"use strict";var n=this&&this.__awaiter||function(e,t,r,n){return new(r||(r=Promise))((function(i,a){function o(e){try{c(n.next(e))}catch(e){a(e)}}function s(e){try{c(n.throw(e))}catch(e){a(e)}}function c(e){var t;e.done?i(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(o,s)}c((n=n.apply(e,t||[])).next())}))};Object.defineProperty(t,"__esModule",{value:!0});const i=r(47);t.default=function(e){return n(this,void 0,void 0,(function*(){let t,r;return r=yield(e=>n(void 0,void 0,void 0,(function*(){let t=void 0;return yield i.parse(e,{complete:e=>{(null==e?void 0:e.data)&&(t=e.data)},error:e=>{var t;console.info("PapaParse err: ",null===(t=e[0])||void 0===t?void 0:t.message)}}),console.log(t),t})))(e),t=(e=>{let t;if(e.forEach((t,r)=>{t.forEach((t,n)=>{e[r][n]=t.replace(/[|]|[\\]/g,"\\$&")})}),!e.length)return t=`| ${["   "].map(e=>"   ")} | \n| --- | `,t;const r=e[0],n=e.slice(1);return t=`| ${r.join(" | ")} | `,t=t.concat(`\n| ${r.map(e=>"---").join(" | ")} | `),n.length&&n.forEach(e=>{t=t.concat(`\n| ${e.map(e=>e).join(" | ")} | `)}),t})(r),t}))}},function(e,t,r){var n,i,a;
/* @license
Papa Parse
v5.3.1
https://github.com/mholt/PapaParse
License: MIT
*/i=[],void 0===(a="function"==typeof(n=function e(){"use strict";var t="undefined"!=typeof self?self:"undefined"!=typeof window?window:void 0!==t?t:{},n=!t.document&&!!t.postMessage,i=n&&/blob:/i.test((t.location||{}).protocol),a={},o=0,s={parse:function(r,n){var i=(n=n||{}).dynamicTyping||!1;if(k(i)&&(n.dynamicTypingFunction=i,i={}),n.dynamicTyping=i,n.transform=!!k(n.transform)&&n.transform,n.worker&&s.WORKERS_SUPPORTED){var c=function(){if(!s.WORKERS_SUPPORTED)return!1;var r,n,i=(r=t.URL||t.webkitURL||null,n=e.toString(),s.BLOB_URL||(s.BLOB_URL=r.createObjectURL(new Blob(["(",n,")();"],{type:"text/javascript"})))),c=new t.Worker(i);return c.onmessage=v,c.id=o++,a[c.id]=c,c}();return c.userStep=n.step,c.userChunk=n.chunk,c.userComplete=n.complete,c.userError=n.error,n.step=k(n.step),n.chunk=k(n.chunk),n.complete=k(n.complete),n.error=k(n.error),delete n.worker,void c.postMessage({input:r,config:n,workerId:c.id})}var l=null;return r===s.NODE_STREAM_INPUT&&"undefined"==typeof PAPA_BROWSER_CONTEXT?(l=new f(n)).getStream():("string"==typeof r?l=n.download?new u(n):new d(n):!0===r.readable&&k(r.read)&&k(r.on)?l=new p(n):(t.File&&r instanceof File||r instanceof Object)&&(l=new h(n)),l.stream(r))},unparse:function(e,t){var r=!1,n=!0,i=",",a="\r\n",o='"',c=o+o,l=!1,u=null,h=!1;!function(){if("object"==typeof t){if("string"!=typeof t.delimiter||s.BAD_DELIMITERS.filter((function(e){return-1!==t.delimiter.indexOf(e)})).length||(i=t.delimiter),("boolean"==typeof t.quotes||"function"==typeof t.quotes||Array.isArray(t.quotes))&&(r=t.quotes),"boolean"!=typeof t.skipEmptyLines&&"string"!=typeof t.skipEmptyLines||(l=t.skipEmptyLines),"string"==typeof t.newline&&(a=t.newline),"string"==typeof t.quoteChar&&(o=t.quoteChar),"boolean"==typeof t.header&&(n=t.header),Array.isArray(t.columns)){if(0===t.columns.length)throw new Error("Option columns is empty");u=t.columns}void 0!==t.escapeChar&&(c=t.escapeChar+o),"boolean"==typeof t.escapeFormulae&&(h=t.escapeFormulae)}}();var d=new RegExp(g(o),"g");if("string"==typeof e&&(e=JSON.parse(e)),Array.isArray(e)){if(!e.length||Array.isArray(e[0]))return p(null,e,l);if("object"==typeof e[0])return p(u||Object.keys(e[0]),e,l)}else if("object"==typeof e)return"string"==typeof e.data&&(e.data=JSON.parse(e.data)),Array.isArray(e.data)&&(e.fields||(e.fields=e.meta&&e.meta.fields),e.fields||(e.fields=Array.isArray(e.data[0])?e.fields:"object"==typeof e.data[0]?Object.keys(e.data[0]):[]),Array.isArray(e.data[0])||"object"==typeof e.data[0]||(e.data=[e.data])),p(e.fields||[],e.data||[],l);throw new Error("Unable to serialize unrecognized input");function p(e,t,r){var o="";"string"==typeof e&&(e=JSON.parse(e)),"string"==typeof t&&(t=JSON.parse(t));var s=Array.isArray(e)&&e.length>0,c=!Array.isArray(t[0]);if(s&&n){for(var l=0;l<e.length;l++)l>0&&(o+=i),o+=f(e[l],l);t.length>0&&(o+=a)}for(var u=0;u<t.length;u++){var h=s?e.length:t[u].length,d=!1,p=s?0===Object.keys(t[u]).length:0===t[u].length;if(r&&!s&&(d="greedy"===r?""===t[u].join("").trim():1===t[u].length&&0===t[u][0].length),"greedy"===r&&s){for(var m=[],g=0;g<h;g++){var b=c?e[g]:g;m.push(t[u][b])}d=""===m.join("").trim()}if(!d){for(var v=0;v<h;v++){v>0&&!p&&(o+=i);var _=s&&c?e[v]:v;o+=f(t[u][_],v)}u<t.length-1&&(!r||h>0&&!p)&&(o+=a)}}return o}function f(e,t){if(null==e)return"";if(e.constructor===Date)return JSON.stringify(e).slice(1,25);!0===h&&"string"==typeof e&&null!==e.match(/^[=+\-@].*$/)&&(e="'"+e);var n=e.toString().replace(d,c);return"boolean"==typeof r&&r||"function"==typeof r&&r(e,t)||Array.isArray(r)&&r[t]||function(e,t){for(var r=0;r<t.length;r++)if(e.indexOf(t[r])>-1)return!0;return!1}(n,s.BAD_DELIMITERS)||n.indexOf(i)>-1||" "===n.charAt(0)||" "===n.charAt(n.length-1)?o+n+o:n}}};if(s.RECORD_SEP=String.fromCharCode(30),s.UNIT_SEP=String.fromCharCode(31),s.BYTE_ORDER_MARK="\ufeff",s.BAD_DELIMITERS=["\r","\n",'"',s.BYTE_ORDER_MARK],s.WORKERS_SUPPORTED=!n&&!!t.Worker,s.NODE_STREAM_INPUT=1,s.LocalChunkSize=10485760,s.RemoteChunkSize=5242880,s.DefaultDelimiter=",",s.Parser=b,s.ParserHandle=m,s.NetworkStreamer=u,s.FileStreamer=h,s.StringStreamer=d,s.ReadableStreamStreamer=p,"undefined"==typeof PAPA_BROWSER_CONTEXT&&(s.DuplexStreamStreamer=f),t.jQuery){var c=t.jQuery;c.fn.parse=function(e){var r=e.config||{},n=[];return this.each((function(e){if("INPUT"!==c(this).prop("tagName").toUpperCase()||"file"!==c(this).attr("type").toLowerCase()||!t.FileReader||!this.files||0===this.files.length)return!0;for(var i=0;i<this.files.length;i++)n.push({file:this.files[i],inputElem:this,instanceConfig:c.extend({},r)})})),i(),this;function i(){if(0!==n.length){var t,r,i,o,l=n[0];if(k(e.before)){var u=e.before(l.file,l.inputElem);if("object"==typeof u){if("abort"===u.action)return t="AbortError",r=l.file,i=l.inputElem,o=u.reason,void(k(e.error)&&e.error({name:t},r,i,o));if("skip"===u.action)return void a();"object"==typeof u.config&&(l.instanceConfig=c.extend(l.instanceConfig,u.config))}else if("skip"===u)return void a()}var h=l.instanceConfig.complete;l.instanceConfig.complete=function(e){k(h)&&h(e,l.file,l.inputElem),a()},s.parse(l.file,l.instanceConfig)}else k(e.complete)&&e.complete()}function a(){n.splice(0,1),i()}}}function l(e){this._handle=null,this._finished=!1,this._completed=!1,this._halted=!1,this._input=null,this._baseIndex=0,this._partialLine="",this._rowCount=0,this._start=0,this._nextChunk=null,this.isFirstChunk=!0,this._completeResults={data:[],errors:[],meta:{}},function(e){var t=w(e);t.chunkSize=parseInt(t.chunkSize),e.step||e.chunk||(t.chunkSize=null),this._handle=new m(t),this._handle.streamer=this,this._config=t}.call(this,e),this.parseChunk=function(e,r){if(this.isFirstChunk&&k(this._config.beforeFirstChunk)){var n=this._config.beforeFirstChunk(e);void 0!==n&&(e=n)}this.isFirstChunk=!1,this._halted=!1;var a=this._partialLine+e;this._partialLine="";var o=this._handle.parse(a,this._baseIndex,!this._finished);if(!this._handle.paused()&&!this._handle.aborted()){var c=o.meta.cursor;this._finished||(this._partialLine=a.substring(c-this._baseIndex),this._baseIndex=c),o&&o.data&&(this._rowCount+=o.data.length);var l=this._finished||this._config.preview&&this._rowCount>=this._config.preview;if(i)t.postMessage({results:o,workerId:s.WORKER_ID,finished:l});else if(k(this._config.chunk)&&!r){if(this._config.chunk(o,this._handle),this._handle.paused()||this._handle.aborted())return void(this._halted=!0);o=void 0,this._completeResults=void 0}return this._config.step||this._config.chunk||(this._completeResults.data=this._completeResults.data.concat(o.data),this._completeResults.errors=this._completeResults.errors.concat(o.errors),this._completeResults.meta=o.meta),this._completed||!l||!k(this._config.complete)||o&&o.meta.aborted||(this._config.complete(this._completeResults,this._input),this._completed=!0),l||o&&o.meta.paused||this._nextChunk(),o}this._halted=!0},this._sendError=function(e){k(this._config.error)?this._config.error(e):i&&this._config.error&&t.postMessage({workerId:s.WORKER_ID,error:e,finished:!1})}}function u(e){var t;(e=e||{}).chunkSize||(e.chunkSize=s.RemoteChunkSize),l.call(this,e),this._nextChunk=n?function(){this._readChunk(),this._chunkLoaded()}:function(){this._readChunk()},this.stream=function(e){this._input=e,this._nextChunk()},this._readChunk=function(){if(this._finished)this._chunkLoaded();else{if(t=new XMLHttpRequest,this._config.withCredentials&&(t.withCredentials=this._config.withCredentials),n||(t.onload=E(this._chunkLoaded,this),t.onerror=E(this._chunkError,this)),t.open(this._config.downloadRequestBody?"POST":"GET",this._input,!n),this._config.downloadRequestHeaders){var e=this._config.downloadRequestHeaders;for(var r in e)t.setRequestHeader(r,e[r])}if(this._config.chunkSize){var i=this._start+this._config.chunkSize-1;t.setRequestHeader("Range","bytes="+this._start+"-"+i)}try{t.send(this._config.downloadRequestBody)}catch(e){this._chunkError(e.message)}n&&0===t.status&&this._chunkError()}},this._chunkLoaded=function(){4===t.readyState&&(t.status<200||t.status>=400?this._chunkError():(this._start+=this._config.chunkSize?this._config.chunkSize:t.responseText.length,this._finished=!this._config.chunkSize||this._start>=function(e){var t=e.getResponseHeader("Content-Range");return null===t?-1:parseInt(t.substring(t.lastIndexOf("/")+1))}(t),this.parseChunk(t.responseText)))},this._chunkError=function(e){var r=t.statusText||e;this._sendError(new Error(r))}}function h(e){var t,r;(e=e||{}).chunkSize||(e.chunkSize=s.LocalChunkSize),l.call(this,e);var n="undefined"!=typeof FileReader;this.stream=function(e){this._input=e,r=e.slice||e.webkitSlice||e.mozSlice,n?((t=new FileReader).onload=E(this._chunkLoaded,this),t.onerror=E(this._chunkError,this)):t=new FileReaderSync,this._nextChunk()},this._nextChunk=function(){this._finished||this._config.preview&&!(this._rowCount<this._config.preview)||this._readChunk()},this._readChunk=function(){var e=this._input;if(this._config.chunkSize){var i=Math.min(this._start+this._config.chunkSize,this._input.size);e=r.call(e,this._start,i)}var a=t.readAsText(e,this._config.encoding);n||this._chunkLoaded({target:{result:a}})},this._chunkLoaded=function(e){this._start+=this._config.chunkSize,this._finished=!this._config.chunkSize||this._start>=this._input.size,this.parseChunk(e.target.result)},this._chunkError=function(){this._sendError(t.error)}}function d(e){var t;e=e||{},l.call(this,e),this.stream=function(e){return t=e,this._nextChunk()},this._nextChunk=function(){if(!this._finished){var e,r=this._config.chunkSize;return r?(e=t.substring(0,r),t=t.substring(r)):(e=t,t=""),this._finished=!t,this.parseChunk(e)}}}function p(e){e=e||{},l.call(this,e);var t=[],r=!0,n=!1;this.pause=function(){l.prototype.pause.apply(this,arguments),this._input.pause()},this.resume=function(){l.prototype.resume.apply(this,arguments),this._input.resume()},this.stream=function(e){this._input=e,this._input.on("data",this._streamData),this._input.on("end",this._streamEnd),this._input.on("error",this._streamError)},this._checkIsFinished=function(){n&&1===t.length&&(this._finished=!0)},this._nextChunk=function(){this._checkIsFinished(),t.length?this.parseChunk(t.shift()):r=!0},this._streamData=E((function(e){try{t.push("string"==typeof e?e:e.toString(this._config.encoding)),r&&(r=!1,this._checkIsFinished(),this.parseChunk(t.shift()))}catch(e){this._streamError(e)}}),this),this._streamError=E((function(e){this._streamCleanUp(),this._sendError(e)}),this),this._streamEnd=E((function(){this._streamCleanUp(),n=!0,this._streamData("")}),this),this._streamCleanUp=E((function(){this._input.removeListener("data",this._streamData),this._input.removeListener("end",this._streamEnd),this._input.removeListener("error",this._streamError)}),this)}function f(e){var t=r(48).Duplex,n=w(e),i=!0,a=!1,o=[],s=null;this._onCsvData=function(e){var t=e.data;s.push(t)||this._handle.paused()||this._handle.pause()},this._onCsvComplete=function(){s.push(null)},n.step=E(this._onCsvData,this),n.complete=E(this._onCsvComplete,this),l.call(this,n),this._nextChunk=function(){a&&1===o.length&&(this._finished=!0),o.length?o.shift()():i=!0},this._addToParseQueue=function(e,t){o.push(E((function(){if(this.parseChunk("string"==typeof e?e:e.toString(n.encoding)),k(t))return t()}),this)),i&&(i=!1,this._nextChunk())},this._onRead=function(){this._handle.paused()&&this._handle.resume()},this._onWrite=function(e,t,r){this._addToParseQueue(e,r)},this._onWriteComplete=function(){a=!0,this._addToParseQueue("")},this.getStream=function(){return s},(s=new t({readableObjectMode:!0,decodeStrings:!1,read:E(this._onRead,this),write:E(this._onWrite,this)})).once("finish",E(this._onWriteComplete,this))}function m(e){var t,r,n,i=Math.pow(2,53),a=-i,o=/^\s*-?(\d+\.?|\.\d+|\d+\.\d+)([eE][-+]?\d+)?\s*$/,c=/^(\d{4}-[01]\d-[0-3]\dT[0-2]\d:[0-5]\d:[0-5]\d\.\d+([+-][0-2]\d:[0-5]\d|Z))|(\d{4}-[01]\d-[0-3]\dT[0-2]\d:[0-5]\d:[0-5]\d([+-][0-2]\d:[0-5]\d|Z))|(\d{4}-[01]\d-[0-3]\dT[0-2]\d:[0-5]\d([+-][0-2]\d:[0-5]\d|Z))$/,l=this,u=0,h=0,d=!1,p=!1,f=[],m={data:[],errors:[],meta:{}};if(k(e.step)){var v=e.step;e.step=function(t){if(m=t,E())y();else{if(y(),0===m.data.length)return;u+=t.data.length,e.preview&&u>e.preview?r.abort():(m.data=m.data[0],v(m,l))}}}function _(t){return"greedy"===e.skipEmptyLines?""===t.join("").trim():1===t.length&&0===t[0].length}function y(){if(m&&n&&(S("Delimiter","UndetectableDelimiter","Unable to auto-detect delimiting character; defaulted to '"+s.DefaultDelimiter+"'"),n=!1),e.skipEmptyLines)for(var t=0;t<m.data.length;t++)_(m.data[t])&&m.data.splice(t--,1);return E()&&function(){if(m)if(Array.isArray(m.data[0])){for(var t=0;E()&&t<m.data.length;t++)m.data[t].forEach(r);m.data.splice(0,1)}else m.data.forEach(r);function r(t,r){k(e.transformHeader)&&(t=e.transformHeader(t,r)),f.push(t)}}(),function(){if(!m||!e.header&&!e.dynamicTyping&&!e.transform)return m;function t(t,r){var n,i=e.header?{}:[];for(n=0;n<t.length;n++){var a=n,o=t[n];e.header&&(a=n>=f.length?"__parsed_extra":f[n]),e.transform&&(o=e.transform(o,a)),o=T(a,o),"__parsed_extra"===a?(i[a]=i[a]||[],i[a].push(o)):i[a]=o}return e.header&&(n>f.length?S("FieldMismatch","TooManyFields","Too many fields: expected "+f.length+" fields but parsed "+n,h+r):n<f.length&&S("FieldMismatch","TooFewFields","Too few fields: expected "+f.length+" fields but parsed "+n,h+r)),i}var r=1;return!m.data.length||Array.isArray(m.data[0])?(m.data=m.data.map(t),r=m.data.length):m.data=t(m.data,0),e.header&&m.meta&&(m.meta.fields=f),h+=r,m}()}function E(){return e.header&&0===f.length}function T(t,r){return function(t){return e.dynamicTypingFunction&&void 0===e.dynamicTyping[t]&&(e.dynamicTyping[t]=e.dynamicTypingFunction(t)),!0===(e.dynamicTyping[t]||e.dynamicTyping)}(t)?"true"===r||"TRUE"===r||"false"!==r&&"FALSE"!==r&&(function(e){if(o.test(e)){var t=parseFloat(e);if(t>a&&t<i)return!0}return!1}(r)?parseFloat(r):c.test(r)?new Date(r):""===r?null:r):r}function S(e,t,r,n){var i={type:e,code:t,message:r};void 0!==n&&(i.row=n),m.errors.push(i)}this.parse=function(i,a,o){var c=e.quoteChar||'"';if(e.newline||(e.newline=function(e,t){e=e.substring(0,1048576);var r=new RegExp(g(t)+"([^]*?)"+g(t),"gm"),n=(e=e.replace(r,"")).split("\r"),i=e.split("\n"),a=i.length>1&&i[0].length<n[0].length;if(1===n.length||a)return"\n";for(var o=0,s=0;s<n.length;s++)"\n"===n[s][0]&&o++;return o>=n.length/2?"\r\n":"\r"}(i,c)),n=!1,e.delimiter)k(e.delimiter)&&(e.delimiter=e.delimiter(i),m.meta.delimiter=e.delimiter);else{var l=function(t,r,n,i,a){var o,c,l,u;a=a||[",","\t","|",";",s.RECORD_SEP,s.UNIT_SEP];for(var h=0;h<a.length;h++){var d=a[h],p=0,f=0,m=0;l=void 0;for(var g=new b({comments:i,delimiter:d,newline:r,preview:10}).parse(t),v=0;v<g.data.length;v++)if(n&&_(g.data[v]))m++;else{var y=g.data[v].length;f+=y,void 0!==l?y>0&&(p+=Math.abs(y-l),l=y):l=y}g.data.length>0&&(f/=g.data.length-m),(void 0===c||p<=c)&&(void 0===u||f>u)&&f>1.99&&(c=p,o=d,u=f)}return e.delimiter=o,{successful:!!o,bestDelimiter:o}}(i,e.newline,e.skipEmptyLines,e.comments,e.delimitersToGuess);l.successful?e.delimiter=l.bestDelimiter:(n=!0,e.delimiter=s.DefaultDelimiter),m.meta.delimiter=e.delimiter}var u=w(e);return e.preview&&e.header&&u.preview++,t=i,r=new b(u),m=r.parse(t,a,o),y(),d?{meta:{paused:!0}}:m||{meta:{paused:!1}}},this.paused=function(){return d},this.pause=function(){d=!0,r.abort(),t=k(e.chunk)?"":t.substring(r.getCharIndex())},this.resume=function(){l.streamer._halted?(d=!1,l.streamer.parseChunk(t,!0)):setTimeout(l.resume,3)},this.aborted=function(){return p},this.abort=function(){p=!0,r.abort(),m.meta.aborted=!0,k(e.complete)&&e.complete(m),t=""}}function g(e){return e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}function b(e){var t,r=(e=e||{}).delimiter,n=e.newline,i=e.comments,a=e.step,o=e.preview,c=e.fastMode,l=t=void 0===e.quoteChar?'"':e.quoteChar;if(void 0!==e.escapeChar&&(l=e.escapeChar),("string"!=typeof r||s.BAD_DELIMITERS.indexOf(r)>-1)&&(r=","),i===r)throw new Error("Comment character same as delimiter");!0===i?i="#":("string"!=typeof i||s.BAD_DELIMITERS.indexOf(i)>-1)&&(i=!1),"\n"!==n&&"\r"!==n&&"\r\n"!==n&&(n="\n");var u=0,h=!1;this.parse=function(e,s,d){if("string"!=typeof e)throw new Error("Input must be a string");var p=e.length,f=r.length,m=n.length,b=i.length,v=k(a);u=0;var _=[],y=[],w=[],E=0;if(!e)return q();if(c||!1!==c&&-1===e.indexOf(t)){for(var T=e.split(n),S=0;S<T.length;S++){if(w=T[S],u+=w.length,S!==T.length-1)u+=n.length;else if(d)return q();if(!i||w.substring(0,b)!==i){if(v){if(_=[],O(w.split(r)),P(),h)return q()}else O(w.split(r));if(o&&S>=o)return _=_.slice(0,o),q(!0)}}return q()}for(var x=e.indexOf(r,u),N=e.indexOf(n,u),C=new RegExp(g(l)+g(t),"g"),A=e.indexOf(t,u);;)if(e[u]!==t)if(i&&0===w.length&&e.substring(u,u+b)===i){if(-1===N)return q();u=N+m,N=e.indexOf(n,u),x=e.indexOf(r,u)}else if(-1!==x&&(x<N||-1===N))w.push(e.substring(u,x)),u=x+f,x=e.indexOf(r,u);else{if(-1===N)break;if(w.push(e.substring(u,N)),M(N+m),v&&(P(),h))return q();if(o&&_.length>=o)return q(!0)}else for(A=u,u++;;){if(-1===(A=e.indexOf(t,A+1)))return d||y.push({type:"Quotes",code:"MissingQuotes",message:"Quoted field unterminated",row:_.length,index:u}),D();if(A===p-1)return D(e.substring(u,A).replace(C,t));if(t!==l||e[A+1]!==l){if(t===l||0===A||e[A-1]!==l){-1!==x&&x<A+1&&(x=e.indexOf(r,A+1)),-1!==N&&N<A+1&&(N=e.indexOf(n,A+1));var R=I(-1===N?x:Math.min(x,N));if(e[A+1+R]===r){w.push(e.substring(u,A).replace(C,t)),u=A+1+R+f,e[A+1+R+f]!==t&&(A=e.indexOf(t,u)),x=e.indexOf(r,u),N=e.indexOf(n,u);break}var L=I(N);if(e.substring(A+1+L,A+1+L+m)===n){if(w.push(e.substring(u,A).replace(C,t)),M(A+1+L+m),x=e.indexOf(r,u),A=e.indexOf(t,u),v&&(P(),h))return q();if(o&&_.length>=o)return q(!0);break}y.push({type:"Quotes",code:"InvalidQuotes",message:"Trailing quote on quoted field is malformed",row:_.length,index:u}),A++}}else A++}return D();function O(e){_.push(e),E=u}function I(t){var r=0;if(-1!==t){var n=e.substring(A+1,t);n&&""===n.trim()&&(r=n.length)}return r}function D(t){return d||(void 0===t&&(t=e.substring(u)),w.push(t),u=p,O(w),v&&P()),q()}function M(t){u=t,O(w),w=[],N=e.indexOf(n,u)}function q(e){return{data:_,errors:y,meta:{delimiter:r,linebreak:n,aborted:h,truncated:!!e,cursor:E+(s||0)}}}function P(){a(q()),_=[],y=[]}},this.abort=function(){h=!0},this.getCharIndex=function(){return u}}function v(e){var t=e.data,r=a[t.workerId],n=!1;if(t.error)r.userError(t.error,t.file);else if(t.results&&t.results.data){var i={abort:function(){n=!0,_(t.workerId,{data:[],errors:[],meta:{aborted:!0}})},pause:y,resume:y};if(k(r.userStep)){for(var o=0;o<t.results.data.length&&(r.userStep({data:t.results.data[o],errors:t.results.errors,meta:t.results.meta},i),!n);o++);delete t.results}else k(r.userChunk)&&(r.userChunk(t.results,i,t.file),delete t.results)}t.finished&&!n&&_(t.workerId,t.results)}function _(e,t){var r=a[e];k(r.userComplete)&&r.userComplete(t),r.terminate(),delete a[e]}function y(){throw new Error("Not implemented.")}function w(e){if("object"!=typeof e||null===e)return e;var t=Array.isArray(e)?[]:{};for(var r in e)t[r]=w(e[r]);return t}function E(e,t){return function(){e.apply(t,arguments)}}function k(e){return"function"==typeof e}return i&&(t.onmessage=function(e){var r=e.data;if(void 0===s.WORKER_ID&&r&&(s.WORKER_ID=r.workerId),"string"==typeof r.input)t.postMessage({workerId:s.WORKER_ID,results:s.parse(r.input,r.config),finished:!0});else if(t.File&&r.input instanceof File||r.input instanceof Object){var n=s.parse(r.input,r.config);n&&t.postMessage({workerId:s.WORKER_ID,results:n,finished:!0})}}),u.prototype=Object.create(l.prototype),u.prototype.constructor=u,h.prototype=Object.create(l.prototype),h.prototype.constructor=h,d.prototype=Object.create(d.prototype),d.prototype.constructor=d,p.prototype=Object.create(l.prototype),p.prototype.constructor=p,"undefined"==typeof PAPA_BROWSER_CONTEXT&&(f.prototype=Object.create(l.prototype),f.prototype.constructor=f),s})?n.apply(t,i):n)||(e.exports=a)},function(e,t){e.exports=require("stream")},function(e,t,r){"use strict";var n=this&&this.__awaiter||function(e,t,r,n){return new(r||(r=Promise))((function(i,a){function o(e){try{c(n.next(e))}catch(e){a(e)}}function s(e){try{c(n.throw(e))}catch(e){a(e)}}function c(e){var t;e.done?i(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(o,s)}c((n=n.apply(e,t||[])).next())}))};Object.defineProperty(t,"__esModule",{value:!0});const i=r(8),a=r(50),o=r(63);t.default=function(){return n(this,void 0,void 0,(function*(){yield i.default.commands.register({name:"pasteHtmlAsMarkdown",label:"HTML as Markdown",execute:()=>n(this,void 0,void 0,(function*(){let e=yield i.default.clipboard.readHtml();if(!(null==e?void 0:e.length))return;const t=new a.default;t.use(o.gfm);const r=t.turndown(e.toString());yield i.default.commands.execute("insertText",r),yield i.default.commands.execute("editor.focus")}))})}))}},function(e,t,r){"use strict";function n(e,t){return Array(t+1).join(e)}r.r(t);var i=["ADDRESS","ARTICLE","ASIDE","AUDIO","BLOCKQUOTE","BODY","CANVAS","CENTER","DD","DIR","DIV","DL","DT","FIELDSET","FIGCAPTION","FIGURE","FOOTER","FORM","FRAMESET","H1","H2","H3","H4","H5","H6","HEADER","HGROUP","HR","HTML","ISINDEX","LI","MAIN","MENU","NAV","NOFRAMES","NOSCRIPT","OL","OUTPUT","P","PRE","SECTION","TABLE","TBODY","TD","TFOOT","TH","THEAD","TR","UL"];function a(e){return l(e,i)}var o=["AREA","BASE","BR","COL","COMMAND","EMBED","HR","IMG","INPUT","KEYGEN","LINK","META","PARAM","SOURCE","TRACK","WBR"];function s(e){return l(e,o)}var c=["A","TABLE","THEAD","TBODY","TFOOT","TH","TD","IFRAME","SCRIPT","AUDIO","VIDEO"];function l(e,t){return t.indexOf(e.nodeName)>=0}function u(e,t){return e.getElementsByTagName&&t.some((function(t){return e.getElementsByTagName(t).length}))}var h={};function d(e){return e?e.replace(/(\n+\s*)+/g,"\n"):""}function p(e){for(var t in this.options=e,this._keep=[],this._remove=[],this.blankRule={replacement:e.blankReplacement},this.keepReplacement=e.keepReplacement,this.defaultRule={replacement:e.defaultReplacement},this.array=[],e.rules)this.array.push(e.rules[t])}function f(e,t,r){for(var n=0;n<e.length;n++){var i=e[n];if(m(i,t,r))return i}}function m(e,t,r){var n=e.filter;if("string"==typeof n){if(n===t.nodeName.toLowerCase())return!0}else if(Array.isArray(n)){if(n.indexOf(t.nodeName.toLowerCase())>-1)return!0}else{if("function"!=typeof n)throw new TypeError("`filter` needs to be a string, array, or function");if(n.call(e,t,r))return!0}}function g(e){var t=e.nextSibling||e.parentNode;return e.parentNode.removeChild(e),t}function b(e,t,r){return e&&e.parentNode===t||r(t)?t.nextSibling||t.parentNode:t.firstChild||t.nextSibling||t.parentNode}h.paragraph={filter:"p",replacement:function(e){return"\n\n"+e+"\n\n"}},h.lineBreak={filter:"br",replacement:function(e,t,r){return r.br+"\n"}},h.heading={filter:["h1","h2","h3","h4","h5","h6"],replacement:function(e,t,r){var i=Number(t.nodeName.charAt(1));return"setext"===r.headingStyle&&i<3?"\n\n"+e+"\n"+n(1===i?"=":"-",e.length)+"\n\n":"\n\n"+n("#",i)+" "+e+"\n\n"}},h.blockquote={filter:"blockquote",replacement:function(e){return"\n\n"+(e=(e=e.replace(/^\n+|\n+$/g,"")).replace(/^/gm,"> "))+"\n\n"}},h.list={filter:["ul","ol"],replacement:function(e,t){var r=t.parentNode;return"LI"===r.nodeName&&r.lastElementChild===t?"\n"+e:"\n\n"+e+"\n\n"}},h.listItem={filter:"li",replacement:function(e,t,r){e=e.replace(/^\n+/,"").replace(/\n+$/,"\n").replace(/\n/gm,"\n    ");var n=r.bulletListMarker+"   ",i=t.parentNode;if("OL"===i.nodeName){var a=i.getAttribute("start"),o=Array.prototype.indexOf.call(i.children,t);n=(a?Number(a)+o:o+1)+".  "}return n+e+(t.nextSibling&&!/\n$/.test(e)?"\n":"")}},h.indentedCodeBlock={filter:function(e,t){return"indented"===t.codeBlockStyle&&"PRE"===e.nodeName&&e.firstChild&&"CODE"===e.firstChild.nodeName},replacement:function(e,t,r){return"\n\n    "+t.firstChild.textContent.replace(/\n/g,"\n    ")+"\n\n"}},h.fencedCodeBlock={filter:function(e,t){return"fenced"===t.codeBlockStyle&&"PRE"===e.nodeName&&e.firstChild&&"CODE"===e.firstChild.nodeName},replacement:function(e,t,r){for(var i,a=((t.firstChild.getAttribute("class")||"").match(/language-(\S+)/)||[null,""])[1],o=t.firstChild.textContent,s=r.fence.charAt(0),c=3,l=new RegExp("^"+s+"{3,}","gm");i=l.exec(o);)i[0].length>=c&&(c=i[0].length+1);var u=n(s,c);return"\n\n"+u+a+"\n"+o.replace(/\n$/,"")+"\n"+u+"\n\n"}},h.horizontalRule={filter:"hr",replacement:function(e,t,r){return"\n\n"+r.hr+"\n\n"}},h.inlineLink={filter:function(e,t){return"inlined"===t.linkStyle&&"A"===e.nodeName&&e.getAttribute("href")},replacement:function(e,t){var r=t.getAttribute("href"),n=d(t.getAttribute("title"));return n&&(n=' "'+n+'"'),"["+e+"]("+r+n+")"}},h.referenceLink={filter:function(e,t){return"referenced"===t.linkStyle&&"A"===e.nodeName&&e.getAttribute("href")},replacement:function(e,t,r){var n,i,a=t.getAttribute("href"),o=d(t.getAttribute("title"));switch(o&&(o=' "'+o+'"'),r.linkReferenceStyle){case"collapsed":n="["+e+"][]",i="["+e+"]: "+a+o;break;case"shortcut":n="["+e+"]",i="["+e+"]: "+a+o;break;default:var s=this.references.length+1;n="["+e+"]["+s+"]",i="["+s+"]: "+a+o}return this.references.push(i),n},references:[],append:function(e){var t="";return this.references.length&&(t="\n\n"+this.references.join("\n")+"\n\n",this.references=[]),t}},h.emphasis={filter:["em","i"],replacement:function(e,t,r){return e.trim()?r.emDelimiter+e+r.emDelimiter:""}},h.strong={filter:["strong","b"],replacement:function(e,t,r){return e.trim()?r.strongDelimiter+e+r.strongDelimiter:""}},h.code={filter:function(e){var t=e.previousSibling||e.nextSibling,r="PRE"===e.parentNode.nodeName&&!t;return"CODE"===e.nodeName&&!r},replacement:function(e){if(!e)return"";e=e.replace(/\r?\n|\r/g," ");for(var t=/^`|^ .*?[^ ].* $|`$/.test(e)?" ":"",r="`",n=e.match(/`+/gm)||[];-1!==n.indexOf(r);)r+="`";return r+t+e+t+r}},h.image={filter:"img",replacement:function(e,t){var r=d(t.getAttribute("alt")),n=t.getAttribute("src")||"",i=d(t.getAttribute("title"));return n?"!["+r+"]("+n+(i?' "'+i+'"':"")+")":""}},p.prototype={add:function(e,t){this.array.unshift(t)},keep:function(e){this._keep.unshift({filter:e,replacement:this.keepReplacement})},remove:function(e){this._remove.unshift({filter:e,replacement:function(){return""}})},forNode:function(e){return e.isBlank?this.blankRule:(t=f(this.array,e,this.options))||(t=f(this._keep,e,this.options))||(t=f(this._remove,e,this.options))?t:this.defaultRule;var t},forEach:function(e){for(var t=0;t<this.array.length;t++)e(this.array[t],t)}};var v="undefined"!=typeof window?window:{};var _,y,w,E=function(){var e=v.DOMParser,t=!1;try{(new e).parseFromString("","text/html")&&(t=!0)}catch(e){}return t}()?v.DOMParser:(_=function(){},y=r(51),_.prototype.parseFromString=function(e){return y.createDocument(e)},_);function k(e,t){var r;"string"==typeof e?r=(w=w||new E).parseFromString('<x-turndown id="turndown-root">'+e+"</x-turndown>","text/html").getElementById("turndown-root"):r=e.cloneNode(!0);return function(e){var t=e.element,r=e.isBlock,n=e.isVoid,i=e.isPre||function(e){return"PRE"===e.nodeName};if(t.firstChild&&!i(t)){for(var a=null,o=!1,s=null,c=b(s,t,i);c!==t;){if(3===c.nodeType||4===c.nodeType){var l=c.data.replace(/[ \r\n\t]+/g," ");if(a&&!/ $/.test(a.data)||o||" "!==l[0]||(l=l.substr(1)),!l){c=g(c);continue}c.data=l,a=c}else{if(1!==c.nodeType){c=g(c);continue}r(c)||"BR"===c.nodeName?(a&&(a.data=a.data.replace(/ $/,"")),a=null,o=!1):n(c)||i(c)?(a=null,o=!0):a&&(o=!1)}var u=b(s,c,i);s=c,c=u}a&&(a.data=a.data.replace(/ $/,""),a.data||g(a))}}({element:r,isBlock:a,isVoid:s,isPre:t.preformattedCode?T:null}),r}function T(e){return"PRE"===e.nodeName||"CODE"===e.nodeName}function S(e,t){return e.isBlock=a(e),e.isCode="CODE"===e.nodeName||e.parentNode.isCode,e.isBlank=function(e){return!s(e)&&!function(e){return l(e,c)}(e)&&/^\s*$/i.test(e.textContent)&&!function(e){return u(e,o)}(e)&&!function(e){return u(e,c)}(e)}(e),e.flankingWhitespace=function(e,t){if(e.isBlock||t.preformattedCode&&e.isCode)return{leading:"",trailing:""};var r=(n=e.textContent,i=n.match(/^(([ \t\r\n]*)(\s*))[\s\S]*?((\s*?)([ \t\r\n]*))$/),{leading:i[1],leadingAscii:i[2],leadingNonAscii:i[3],trailing:i[4],trailingNonAscii:i[5],trailingAscii:i[6]});var n,i;r.leadingAscii&&x("left",e,t)&&(r.leading=r.leadingNonAscii);r.trailingAscii&&x("right",e,t)&&(r.trailing=r.trailingNonAscii);return{leading:r.leading,trailing:r.trailing}}(e,t),e}function x(e,t,r){var n,i,o;return"left"===e?(n=t.previousSibling,i=/ $/):(n=t.nextSibling,i=/^ /),n&&(3===n.nodeType?o=i.test(n.nodeValue):r.preformattedCode&&"CODE"===n.nodeName?o=!1:1!==n.nodeType||a(n)||(o=i.test(n.textContent))),o}var N=Array.prototype.reduce,C=[[/\\/g,"\\\\"],[/\*/g,"\\*"],[/^-/g,"\\-"],[/^\+ /g,"\\+ "],[/^(=+)/g,"\\$1"],[/^(#{1,6}) /g,"\\$1 "],[/`/g,"\\`"],[/^~~~/g,"\\~~~"],[/\[/g,"\\["],[/\]/g,"\\]"],[/^>/g,"\\>"],[/_/g,"\\_"],[/^(\d+)\. /g,"$1\\. "]];function A(e){if(!(this instanceof A))return new A(e);var t={rules:h,headingStyle:"setext",hr:"* * *",bulletListMarker:"*",codeBlockStyle:"indented",fence:"```",emDelimiter:"_",strongDelimiter:"**",linkStyle:"inlined",linkReferenceStyle:"full",br:"  ",preformattedCode:!1,blankReplacement:function(e,t){return t.isBlock?"\n\n":""},keepReplacement:function(e,t){return t.isBlock?"\n\n"+t.outerHTML+"\n\n":t.outerHTML},defaultReplacement:function(e,t){return t.isBlock?"\n\n"+e+"\n\n":e}};this.options=function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)r.hasOwnProperty(n)&&(e[n]=r[n])}return e}({},t,e),this.rules=new p(this.options)}function R(e){var t=this;return N.call(e.childNodes,(function(e,r){var n="";return 3===(r=new S(r,t.options)).nodeType?n=r.isCode?r.nodeValue:t.escape(r.nodeValue):1===r.nodeType&&(n=O.call(t,r)),I(e,n)}),"")}function L(e){var t=this;return this.rules.forEach((function(r){"function"==typeof r.append&&(e=I(e,r.append(t.options)))})),e.replace(/^[\t\r\n]+/,"").replace(/[\t\r\n\s]+$/,"")}function O(e){var t=this.rules.forNode(e),r=R.call(this,e),n=e.flankingWhitespace;return(n.leading||n.trailing)&&(r=r.trim()),n.leading+t.replacement(r,e,this.options)+n.trailing}function I(e,t){var r=function(e){for(var t=e.length;t>0&&"\n"===e[t-1];)t--;return e.substring(0,t)}(e),n=t.replace(/^\n*/,""),i=Math.max(e.length-r.length,t.length-n.length);return r+"\n\n".substring(0,i)+n}A.prototype={turndown:function(e){if(!function(e){return null!=e&&("string"==typeof e||e.nodeType&&(1===e.nodeType||9===e.nodeType||11===e.nodeType))}(e))throw new TypeError(e+" is not a string, or an element/document/fragment node.");if(""===e)return"";var t=R.call(this,new k(e,this.options));return L.call(this,t)},use:function(e){if(Array.isArray(e))for(var t=0;t<e.length;t++)this.use(e[t]);else{if("function"!=typeof e)throw new TypeError("plugin must be a Function or an Array of Functions");e(this)}return this},addRule:function(e,t){return this.rules.add(e,t),this},keep:function(e){return this.rules.keep(e),this},remove:function(e){return this.rules.remove(e),this},escape:function(e){return C.reduce((function(e,t){return e.replace(t[0],t[1])}),e)}},t.default=A},function(e,t,r){"use strict";var n=r(5),i=r(21),a=r(19);t.createDOMImplementation=function(){return new n(null)},t.createDocument=function(e,t){if(e||t){var r=new i;return r.parse(e||"",!0),r.document()}return new n(null).createHTMLDocument("")},t.createIncrementalHTMLParser=function(){var e=new i;return{write:function(t){t.length>0&&e.parse(t,!1,(function(){return!0}))},end:function(t){e.parse(t||"",!0,(function(){return!0}))},process:function(t){return e.parse("",!1,t)},document:function(){return e.document()}}},t.createWindow=function(e,r){var n=t.createDocument(e);return void 0!==r&&(n._address=r),new a(n)},t.impl=r(41)},function(e,t,r){"use strict";e.exports=class extends Array{constructor(e){if(super(e&&e.length||0),e)for(var t in e)this[t]=e[t]}item(e){return this[e]||null}}},function(e,t,r){"use strict";function n(e){return this[e]||null}e.exports=function(e){return e||(e=[]),e.item=n,e}},function(e,t,r){"use strict";e.exports=i;var n=r(1);function i(e,t){this.root=e,this.filter=t,this.lastModTime=e.lastModTime,this.done=!1,this.cache=[],this.traverse()}i.prototype=Object.create(Object.prototype,{length:{get:function(){return this.checkcache(),this.done||this.traverse(),this.cache.length}},item:{value:function(e){return this.checkcache(),!this.done&&e>=this.cache.length&&this.traverse(),this.cache[e]}},checkcache:{value:function(){if(this.lastModTime!==this.root.lastModTime){for(var e=this.cache.length-1;e>=0;e--)this[e]=void 0;this.cache.length=0,this.done=!1,this.lastModTime=this.root.lastModTime}}},traverse:{value:function(e){var t;for(void 0!==e&&e++;null!==(t=this.next());)if(this[this.cache.length]=t,this.cache.push(t),e&&this.cache.length===e)return;this.done=!0}},next:{value:function(){var e,t=0===this.cache.length?this.root:this.cache[this.cache.length-1];for(e=t.nodeType===n.DOCUMENT_NODE?t.documentElement:t.nextElement(this.root);e;){if(this.filter(e))return e;e=e.nextElement(this.root)}return null}}})},function(e,t,r){"use strict";e.exports=h;var n=r(1),i=r(7),a=r(36),o=r(0),s={first:"firstChild",last:"lastChild",next:"firstChild",previous:"lastChild"},c={first:"nextSibling",last:"previousSibling",next:"nextSibling",previous:"previousSibling"};function l(e,t){var r,n,a,o,l;for(n=e._currentNode[s[t]];null!==n;){if((o=e._internalFilter(n))===i.FILTER_ACCEPT)return e._currentNode=n,n;if(o!==i.FILTER_SKIP||null===(r=n[s[t]]))for(;null!==n;){if(null!==(l=n[c[t]])){n=l;break}if(null===(a=n.parentNode)||a===e.root||a===e._currentNode)return null;n=a}else n=r}return null}function u(e,t){var r,n,a;if((r=e._currentNode)===e.root)return null;for(;;){for(a=r[c[t]];null!==a;){if(r=a,(n=e._internalFilter(r))===i.FILTER_ACCEPT)return e._currentNode=r,r;a=r[s[t]],n!==i.FILTER_REJECT&&null!==a||(a=r[c[t]])}if(null===(r=r.parentNode)||r===e.root)return null;if(e._internalFilter(r)===i.FILTER_ACCEPT)return null}}function h(e,t,r){e&&e.nodeType||o.NotSupportedError(),this._root=e,this._whatToShow=Number(t)||0,this._filter=r||null,this._active=!1,this._currentNode=e}Object.defineProperties(h.prototype,{root:{get:function(){return this._root}},whatToShow:{get:function(){return this._whatToShow}},filter:{get:function(){return this._filter}},currentNode:{get:function(){return this._currentNode},set:function(e){if(!(e instanceof n))throw new TypeError("Not a Node");this._currentNode=e}},_internalFilter:{value:function(e){var t,r;if(this._active&&o.InvalidStateError(),!(1<<e.nodeType-1&this._whatToShow))return i.FILTER_SKIP;if(null===(r=this._filter))t=i.FILTER_ACCEPT;else{this._active=!0;try{t="function"==typeof r?r(e):r.acceptNode(e)}finally{this._active=!1}}return+t}},parentNode:{value:function(){for(var e=this._currentNode;e!==this.root;){if(null===(e=e.parentNode))return null;if(this._internalFilter(e)===i.FILTER_ACCEPT)return this._currentNode=e,e}return null}},firstChild:{value:function(){return l(this,"first")}},lastChild:{value:function(){return l(this,"last")}},previousSibling:{value:function(){return u(this,"previous")}},nextSibling:{value:function(){return u(this,"next")}},previousNode:{value:function(){var e,t,r,n;for(e=this._currentNode;e!==this._root;){for(r=e.previousSibling;r;r=e.previousSibling)if(e=r,(t=this._internalFilter(e))!==i.FILTER_REJECT){for(n=e.lastChild;n&&(e=n,(t=this._internalFilter(e))!==i.FILTER_REJECT);n=e.lastChild);if(t===i.FILTER_ACCEPT)return this._currentNode=e,e}if(e===this.root||null===e.parentNode)return null;if(e=e.parentNode,this._internalFilter(e)===i.FILTER_ACCEPT)return this._currentNode=e,e}return null}},nextNode:{value:function(){var e,t,r,n;e=this._currentNode,t=i.FILTER_ACCEPT;e:for(;;){for(r=e.firstChild;r;r=e.firstChild){if(e=r,(t=this._internalFilter(e))===i.FILTER_ACCEPT)return this._currentNode=e,e;if(t===i.FILTER_REJECT)break}for(n=a.nextSkippingChildren(e,this.root);n;n=a.nextSkippingChildren(e,this.root)){if(e=n,(t=this._internalFilter(e))===i.FILTER_ACCEPT)return this._currentNode=e,e;if(t===i.FILTER_SKIP)continue e}return null}}},toString:{value:function(){return"[object TreeWalker]"}}})},function(e,t,r){"use strict";e.exports=l;var n=r(7),i=r(36),a=r(0);function o(e,t,r){return r?i.next(e,t):e===t?null:i.previous(e,null)}function s(e,t){for(;t;t=t.parentNode)if(e===t)return!0;return!1}function c(e,t){var r,i;for(r=e._referenceNode,i=e._pointerBeforeReferenceNode;;){if(i===t)i=!i;else if(null===(r=o(r,e._root,t)))return null;if(e._internalFilter(r)===n.FILTER_ACCEPT)break}return e._referenceNode=r,e._pointerBeforeReferenceNode=i,r}function l(e,t,r){e&&e.nodeType||a.NotSupportedError(),this._root=e,this._referenceNode=e,this._pointerBeforeReferenceNode=!0,this._whatToShow=Number(t)||0,this._filter=r||null,this._active=!1,e.doc._attachNodeIterator(this)}Object.defineProperties(l.prototype,{root:{get:function(){return this._root}},referenceNode:{get:function(){return this._referenceNode}},pointerBeforeReferenceNode:{get:function(){return this._pointerBeforeReferenceNode}},whatToShow:{get:function(){return this._whatToShow}},filter:{get:function(){return this._filter}},_internalFilter:{value:function(e){var t,r;if(this._active&&a.InvalidStateError(),!(1<<e.nodeType-1&this._whatToShow))return n.FILTER_SKIP;if(null===(r=this._filter))t=n.FILTER_ACCEPT;else{this._active=!0;try{t="function"==typeof r?r(e):r.acceptNode(e)}finally{this._active=!1}}return+t}},_preremove:{value:function(e){if(!s(e,this._root)&&s(e,this._referenceNode)){if(this._pointerBeforeReferenceNode){for(var t=e;t.lastChild;)t=t.lastChild;if(t=i.next(t,this.root))return void(this._referenceNode=t);this._pointerBeforeReferenceNode=!1}var r;if(null===e.previousSibling)this._referenceNode=e.parentNode;else for(this._referenceNode=e.previousSibling,r=this._referenceNode.lastChild;r;r=this._referenceNode.lastChild)this._referenceNode=r}}},nextNode:{value:function(){return c(this,!0)}},previousNode:{value:function(){return c(this,!1)}},detach:{value:function(){}},toString:{value:function(){return"[object NodeIterator]"}}})},function(e,t,r){"use strict";e.exports=i;var n=r(3);function i(e,t){n.call(this,e,t)}i.prototype=Object.create(n.prototype,{constructor:{value:i}})},function(e,t,r){"use strict";
/*!
Parser-Lib
Copyright (c) 2009-2011 Nicholas C. Zakas. All rights reserved.

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in
all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
THE SOFTWARE.

*/var n=Object.create(null);!function(){function e(){this._listeners=Object.create(null)}function t(e){this._input=e.replace(/(\r|\n){1,2}/g,"\n"),this._line=1,this._col=1,this._cursor=0}function r(e,t,r){Error.call(this),this.name=this.constructor.name,this.col=r,this.line=t,this.message=e}function i(e,t,r,n){this.col=r,this.line=t,this.text=e,this.type=n}function a(e,r){this._reader=e?new t(e.toString()):null,this._token=null,this._tokenData=r,this._lt=[],this._ltIndex=0,this._ltIndexCache=[]}e.prototype={constructor:e,addListener:function(e,t){this._listeners[e]||(this._listeners[e]=[]),this._listeners[e].push(t)},fire:function(e){if("string"==typeof e&&(e={type:e}),void 0!==e.target&&(e.target=this),void 0===e.type)throw new Error("Event object missing 'type' property.");if(this._listeners[e.type])for(var t=this._listeners[e.type].concat(),r=0,n=t.length;r<n;r++)t[r].call(this,e)},removeListener:function(e,t){if(this._listeners[e])for(var r=this._listeners[e],n=0,i=r.length;n<i;n++)if(r[n]===t){r.splice(n,1);break}}},t.prototype={constructor:t,getCol:function(){return this._col},getLine:function(){return this._line},eof:function(){return this._cursor===this._input.length},peek:function(e){var t=null;return e=void 0===e?1:e,this._cursor<this._input.length&&(t=this._input.charAt(this._cursor+e-1)),t},read:function(){var e=null;return this._cursor<this._input.length&&("\n"===this._input.charAt(this._cursor)?(this._line++,this._col=1):this._col++,e=this._input.charAt(this._cursor++)),e},mark:function(){this._bookmark={cursor:this._cursor,line:this._line,col:this._col}},reset:function(){this._bookmark&&(this._cursor=this._bookmark.cursor,this._line=this._bookmark.line,this._col=this._bookmark.col,delete this._bookmark)},readTo:function(e){for(var t,r="";r.length<e.length||r.lastIndexOf(e)!==r.length-e.length;){if(!(t=this.read()))throw new Error('Expected "'+e+'" at line '+this._line+", col "+this._col+".");r+=t}return r},readWhile:function(e){for(var t="",r=this.read();null!==r&&e(r);)t+=r,r=this.read();return t},readMatch:function(e){var t=this._input.substring(this._cursor),r=null;return"string"==typeof e?0===t.indexOf(e)&&(r=this.readCount(e.length)):e instanceof RegExp&&e.test(t)&&(r=this.readCount(RegExp.lastMatch.length)),r},readCount:function(e){for(var t="";e--;)t+=this.read();return t}},r.prototype=Object.create(Error.prototype),r.prototype.constructor=r,i.fromToken=function(e){return new i(e.value,e.startLine,e.startCol)},i.prototype={constructor:i,valueOf:function(){return this.toString()},toString:function(){return this.text}},a.createTokenData=function(e){var t=[],r=Object.create(null),n=e.concat([]),i=0,a=n.length+1;for(n.UNKNOWN=-1,n.unshift({name:"EOF"});i<a;i++)t.push(n[i].name),n[n[i].name]=i,n[i].text&&(r[n[i].text]=i);return n.name=function(e){return t[e]},n.type=function(e){return r[e]},n},a.prototype={constructor:a,match:function(e,t){e instanceof Array||(e=[e]);for(var r=this.get(t),n=0,i=e.length;n<i;)if(r===e[n++])return!0;return this.unget(),!1},mustMatch:function(e,t){var n;if(e instanceof Array||(e=[e]),!this.match.apply(this,arguments))throw n=this.LT(1),new r("Expected "+this._tokenData[e[0]].name+" at line "+n.startLine+", col "+n.startCol+".",n.startLine,n.startCol)},advance:function(e,t){for(;0!==this.LA(0)&&!this.match(e,t);)this.get();return this.LA(0)},get:function(e){var t,r,n=this._tokenData,i=0;if(this._lt.length&&this._ltIndex>=0&&this._ltIndex<this._lt.length){for(i++,this._token=this._lt[this._ltIndex++],r=n[this._token.type];void 0!==r.channel&&e!==r.channel&&this._ltIndex<this._lt.length;)this._token=this._lt[this._ltIndex++],r=n[this._token.type],i++;if((void 0===r.channel||e===r.channel)&&this._ltIndex<=this._lt.length)return this._ltIndexCache.push(i),this._token.type}return(t=this._getToken()).type>-1&&!n[t.type].hide&&(t.channel=n[t.type].channel,this._token=t,this._lt.push(t),this._ltIndexCache.push(this._lt.length-this._ltIndex+i),this._lt.length>5&&this._lt.shift(),this._ltIndexCache.length>5&&this._ltIndexCache.shift(),this._ltIndex=this._lt.length),(r=n[t.type])&&(r.hide||void 0!==r.channel&&e!==r.channel)?this.get(e):t.type},LA:function(e){var t,r=e;if(e>0){if(e>5)throw new Error("Too much lookahead.");for(;r;)t=this.get(),r--;for(;r<e;)this.unget(),r++}else if(e<0){if(!this._lt[this._ltIndex+e])throw new Error("Too much lookbehind.");t=this._lt[this._ltIndex+e].type}else t=this._token.type;return t},LT:function(e){return this.LA(e),this._lt[this._ltIndex+e-1]},peek:function(){return this.LA(1)},token:function(){return this._token},tokenName:function(e){return e<0||e>this._tokenData.length?"UNKNOWN_TOKEN":this._tokenData[e].name},tokenType:function(e){return this._tokenData[e]||-1},unget:function(){if(!this._ltIndexCache.length)throw new Error("Too much lookahead.");this._ltIndex-=this._ltIndexCache.pop(),this._token=this._lt[this._ltIndex-1]}},n.util={__proto__:null,StringReader:t,SyntaxError:r,SyntaxUnit:i,EventTarget:e,TokenStreamBase:a}}(),function(){var e=n.util.EventTarget,t=n.util.TokenStreamBase,r=(n.util.StringReader,n.util.SyntaxError),i=n.util.SyntaxUnit,a={__proto__:null,aliceblue:"#f0f8ff",antiquewhite:"#faebd7",aqua:"#00ffff",aquamarine:"#7fffd4",azure:"#f0ffff",beige:"#f5f5dc",bisque:"#ffe4c4",black:"#000000",blanchedalmond:"#ffebcd",blue:"#0000ff",blueviolet:"#8a2be2",brown:"#a52a2a",burlywood:"#deb887",cadetblue:"#5f9ea0",chartreuse:"#7fff00",chocolate:"#d2691e",coral:"#ff7f50",cornflowerblue:"#6495ed",cornsilk:"#fff8dc",crimson:"#dc143c",cyan:"#00ffff",darkblue:"#00008b",darkcyan:"#008b8b",darkgoldenrod:"#b8860b",darkgray:"#a9a9a9",darkgrey:"#a9a9a9",darkgreen:"#006400",darkkhaki:"#bdb76b",darkmagenta:"#8b008b",darkolivegreen:"#556b2f",darkorange:"#ff8c00",darkorchid:"#9932cc",darkred:"#8b0000",darksalmon:"#e9967a",darkseagreen:"#8fbc8f",darkslateblue:"#483d8b",darkslategray:"#2f4f4f",darkslategrey:"#2f4f4f",darkturquoise:"#00ced1",darkviolet:"#9400d3",deeppink:"#ff1493",deepskyblue:"#00bfff",dimgray:"#696969",dimgrey:"#696969",dodgerblue:"#1e90ff",firebrick:"#b22222",floralwhite:"#fffaf0",forestgreen:"#228b22",fuchsia:"#ff00ff",gainsboro:"#dcdcdc",ghostwhite:"#f8f8ff",gold:"#ffd700",goldenrod:"#daa520",gray:"#808080",grey:"#808080",green:"#008000",greenyellow:"#adff2f",honeydew:"#f0fff0",hotpink:"#ff69b4",indianred:"#cd5c5c",indigo:"#4b0082",ivory:"#fffff0",khaki:"#f0e68c",lavender:"#e6e6fa",lavenderblush:"#fff0f5",lawngreen:"#7cfc00",lemonchiffon:"#fffacd",lightblue:"#add8e6",lightcoral:"#f08080",lightcyan:"#e0ffff",lightgoldenrodyellow:"#fafad2",lightgray:"#d3d3d3",lightgrey:"#d3d3d3",lightgreen:"#90ee90",lightpink:"#ffb6c1",lightsalmon:"#ffa07a",lightseagreen:"#20b2aa",lightskyblue:"#87cefa",lightslategray:"#778899",lightslategrey:"#778899",lightsteelblue:"#b0c4de",lightyellow:"#ffffe0",lime:"#00ff00",limegreen:"#32cd32",linen:"#faf0e6",magenta:"#ff00ff",maroon:"#800000",mediumaquamarine:"#66cdaa",mediumblue:"#0000cd",mediumorchid:"#ba55d3",mediumpurple:"#9370d8",mediumseagreen:"#3cb371",mediumslateblue:"#7b68ee",mediumspringgreen:"#00fa9a",mediumturquoise:"#48d1cc",mediumvioletred:"#c71585",midnightblue:"#191970",mintcream:"#f5fffa",mistyrose:"#ffe4e1",moccasin:"#ffe4b5",navajowhite:"#ffdead",navy:"#000080",oldlace:"#fdf5e6",olive:"#808000",olivedrab:"#6b8e23",orange:"#ffa500",orangered:"#ff4500",orchid:"#da70d6",palegoldenrod:"#eee8aa",palegreen:"#98fb98",paleturquoise:"#afeeee",palevioletred:"#d87093",papayawhip:"#ffefd5",peachpuff:"#ffdab9",peru:"#cd853f",pink:"#ffc0cb",plum:"#dda0dd",powderblue:"#b0e0e6",purple:"#800080",red:"#ff0000",rosybrown:"#bc8f8f",royalblue:"#4169e1",saddlebrown:"#8b4513",salmon:"#fa8072",sandybrown:"#f4a460",seagreen:"#2e8b57",seashell:"#fff5ee",sienna:"#a0522d",silver:"#c0c0c0",skyblue:"#87ceeb",slateblue:"#6a5acd",slategray:"#708090",slategrey:"#708090",snow:"#fffafa",springgreen:"#00ff7f",steelblue:"#4682b4",tan:"#d2b48c",teal:"#008080",thistle:"#d8bfd8",tomato:"#ff6347",turquoise:"#40e0d0",violet:"#ee82ee",wheat:"#f5deb3",white:"#ffffff",whitesmoke:"#f5f5f5",yellow:"#ffff00",yellowgreen:"#9acd32",currentColor:"The value of the 'color' property.",activeBorder:"Active window border.",activecaption:"Active window caption.",appworkspace:"Background color of multiple document interface.",background:"Desktop background.",buttonface:"The face background color for 3-D elements that appear 3-D due to one layer of surrounding border.",buttonhighlight:"The color of the border facing the light source for 3-D elements that appear 3-D due to one layer of surrounding border.",buttonshadow:"The color of the border away from the light source for 3-D elements that appear 3-D due to one layer of surrounding border.",buttontext:"Text on push buttons.",captiontext:"Text in caption, size box, and scrollbar arrow box.",graytext:"Grayed (disabled) text. This color is set to #000 if the current display driver does not support a solid gray color.",greytext:"Greyed (disabled) text. This color is set to #000 if the current display driver does not support a solid grey color.",highlight:"Item(s) selected in a control.",highlighttext:"Text of item(s) selected in a control.",inactiveborder:"Inactive window border.",inactivecaption:"Inactive window caption.",inactivecaptiontext:"Color of text in an inactive caption.",infobackground:"Background color for tooltip controls.",infotext:"Text color for tooltip controls.",menu:"Menu background.",menutext:"Text in menus.",scrollbar:"Scroll bar gray area.",threeddarkshadow:"The color of the darker (generally outer) of the two borders away from the light source for 3-D elements that appear 3-D due to two concentric layers of surrounding border.",threedface:"The face background color for 3-D elements that appear 3-D due to two concentric layers of surrounding border.",threedhighlight:"The color of the lighter (generally outer) of the two borders facing the light source for 3-D elements that appear 3-D due to two concentric layers of surrounding border.",threedlightshadow:"The color of the darker (generally inner) of the two borders facing the light source for 3-D elements that appear 3-D due to two concentric layers of surrounding border.",threedshadow:"The color of the lighter (generally inner) of the two borders away from the light source for 3-D elements that appear 3-D due to two concentric layers of surrounding border.",window:"Window background.",windowframe:"Window frame.",windowtext:"Text in windows."};function o(e,t,r){i.call(this,e,t,r,l.COMBINATOR_TYPE),this.type="unknown",/^\s+$/.test(e)?this.type="descendant":">"===e?this.type="child":"+"===e?this.type="adjacent-sibling":"~"===e&&(this.type="sibling")}function s(e,t){i.call(this,"("+e+(null!==t?":"+t:"")+")",e.startLine,e.startCol,l.MEDIA_FEATURE_TYPE),this.name=e,this.value=t}function c(e,t,r,n,a){i.call(this,(e?e+" ":"")+(t||"")+(t&&r.length>0?" and ":"")+r.join(" and "),n,a,l.MEDIA_QUERY_TYPE),this.modifier=e,this.mediaType=t,this.features=r}function l(t){e.call(this),this.options=t||{},this._tokenStream=null}o.prototype=new i,o.prototype.constructor=o,s.prototype=new i,s.prototype.constructor=s,c.prototype=new i,c.prototype.constructor=c,l.DEFAULT_TYPE=0,l.COMBINATOR_TYPE=1,l.MEDIA_FEATURE_TYPE=2,l.MEDIA_QUERY_TYPE=3,l.PROPERTY_NAME_TYPE=4,l.PROPERTY_VALUE_TYPE=5,l.PROPERTY_VALUE_PART_TYPE=6,l.SELECTOR_TYPE=7,l.SELECTOR_PART_TYPE=8,l.SELECTOR_SUB_PART_TYPE=9,l.prototype=function(){var t,n=new e,a={__proto__:null,constructor:l,DEFAULT_TYPE:0,COMBINATOR_TYPE:1,MEDIA_FEATURE_TYPE:2,MEDIA_QUERY_TYPE:3,PROPERTY_NAME_TYPE:4,PROPERTY_VALUE_TYPE:5,PROPERTY_VALUE_PART_TYPE:6,SELECTOR_TYPE:7,SELECTOR_PART_TYPE:8,SELECTOR_SUB_PART_TYPE:9,_stylesheet:function(){var e,t,n,i=this._tokenStream;for(this.fire("startstylesheet"),this._charset(),this._skipCruft();i.peek()===R.IMPORT_SYM;)this._import(),this._skipCruft();for(;i.peek()===R.NAMESPACE_SYM;)this._namespace(),this._skipCruft();for(n=i.peek();n>R.EOF;){try{switch(n){case R.MEDIA_SYM:this._media(),this._skipCruft();break;case R.PAGE_SYM:this._page(),this._skipCruft();break;case R.FONT_FACE_SYM:this._font_face(),this._skipCruft();break;case R.KEYFRAMES_SYM:this._keyframes(),this._skipCruft();break;case R.VIEWPORT_SYM:this._viewport(),this._skipCruft();break;case R.DOCUMENT_SYM:this._document(),this._skipCruft();break;case R.UNKNOWN_SYM:if(i.get(),this.options.strict)throw new r("Unknown @ rule.",i.LT(0).startLine,i.LT(0).startCol);for(this.fire({type:"error",error:null,message:"Unknown @ rule: "+i.LT(0).value+".",line:i.LT(0).startLine,col:i.LT(0).startCol}),e=0;i.advance([R.LBRACE,R.RBRACE])===R.LBRACE;)e++;for(;e;)i.advance([R.RBRACE]),e--;break;case R.S:this._readWhitespace();break;default:if(!this._ruleset())switch(n){case R.CHARSET_SYM:throw t=i.LT(1),this._charset(!1),new r("@charset not allowed here.",t.startLine,t.startCol);case R.IMPORT_SYM:throw t=i.LT(1),this._import(!1),new r("@import not allowed here.",t.startLine,t.startCol);case R.NAMESPACE_SYM:throw t=i.LT(1),this._namespace(!1),new r("@namespace not allowed here.",t.startLine,t.startCol);default:i.get(),this._unexpectedToken(i.token())}}}catch(e){if(!(e instanceof r)||this.options.strict)throw e;this.fire({type:"error",error:e,message:e.message,line:e.line,col:e.col})}n=i.peek()}n!==R.EOF&&this._unexpectedToken(i.token()),this.fire("endstylesheet")},_charset:function(e){var t,r,n,i=this._tokenStream;i.match(R.CHARSET_SYM)&&(r=i.token().startLine,n=i.token().startCol,this._readWhitespace(),i.mustMatch(R.STRING),t=i.token().value,this._readWhitespace(),i.mustMatch(R.SEMICOLON),!1!==e&&this.fire({type:"charset",charset:t,line:r,col:n}))},_import:function(e){var t,r,n,i=this._tokenStream;i.mustMatch(R.IMPORT_SYM),r=i.token(),this._readWhitespace(),i.mustMatch([R.STRING,R.URI]),t=i.token().value.replace(/^(?:url\()?["']?([^"']+?)["']?\)?$/,"$1"),this._readWhitespace(),n=this._media_query_list(),i.mustMatch(R.SEMICOLON),this._readWhitespace(),!1!==e&&this.fire({type:"import",uri:t,media:n,line:r.startLine,col:r.startCol})},_namespace:function(e){var t,r,n,i,a=this._tokenStream;a.mustMatch(R.NAMESPACE_SYM),t=a.token().startLine,r=a.token().startCol,this._readWhitespace(),a.match(R.IDENT)&&(n=a.token().value,this._readWhitespace()),a.mustMatch([R.STRING,R.URI]),i=a.token().value.replace(/(?:url\()?["']([^"']+)["']\)?/,"$1"),this._readWhitespace(),a.mustMatch(R.SEMICOLON),this._readWhitespace(),!1!==e&&this.fire({type:"namespace",prefix:n,uri:i,line:t,col:r})},_media:function(){var e,t,r,n=this._tokenStream;for(n.mustMatch(R.MEDIA_SYM),e=n.token().startLine,t=n.token().startCol,this._readWhitespace(),r=this._media_query_list(),n.mustMatch(R.LBRACE),this._readWhitespace(),this.fire({type:"startmedia",media:r,line:e,col:t});;)if(n.peek()===R.PAGE_SYM)this._page();else if(n.peek()===R.FONT_FACE_SYM)this._font_face();else if(n.peek()===R.VIEWPORT_SYM)this._viewport();else if(n.peek()===R.DOCUMENT_SYM)this._document();else if(!this._ruleset())break;n.mustMatch(R.RBRACE),this._readWhitespace(),this.fire({type:"endmedia",media:r,line:e,col:t})},_media_query_list:function(){var e=this._tokenStream,t=[];for(this._readWhitespace(),e.peek()!==R.IDENT&&e.peek()!==R.LPAREN||t.push(this._media_query());e.match(R.COMMA);)this._readWhitespace(),t.push(this._media_query());return t},_media_query:function(){var e=this._tokenStream,t=null,r=null,n=null,i=[];if(e.match(R.IDENT)&&("only"!==(r=e.token().value.toLowerCase())&&"not"!==r?(e.unget(),r=null):n=e.token()),this._readWhitespace(),e.peek()===R.IDENT?(t=this._media_type(),null===n&&(n=e.token())):e.peek()===R.LPAREN&&(null===n&&(n=e.LT(1)),i.push(this._media_expression())),null===t&&0===i.length)return null;for(this._readWhitespace();e.match(R.IDENT);)"and"!==e.token().value.toLowerCase()&&this._unexpectedToken(e.token()),this._readWhitespace(),i.push(this._media_expression());return new c(r,t,i,n.startLine,n.startCol)},_media_type:function(){return this._media_feature()},_media_expression:function(){var e,t,r=this._tokenStream,n=null;return r.mustMatch(R.LPAREN),e=this._media_feature(),this._readWhitespace(),r.match(R.COLON)&&(this._readWhitespace(),t=r.LT(1),n=this._expression()),r.mustMatch(R.RPAREN),this._readWhitespace(),new s(e,n?new i(n,t.startLine,t.startCol):null)},_media_feature:function(){var e=this._tokenStream;return this._readWhitespace(),e.mustMatch(R.IDENT),i.fromToken(e.token())},_page:function(){var e,t,r=this._tokenStream,n=null,i=null;r.mustMatch(R.PAGE_SYM),e=r.token().startLine,t=r.token().startCol,this._readWhitespace(),r.match(R.IDENT)&&"auto"===(n=r.token().value).toLowerCase()&&this._unexpectedToken(r.token()),r.peek()===R.COLON&&(i=this._pseudo_page()),this._readWhitespace(),this.fire({type:"startpage",id:n,pseudo:i,line:e,col:t}),this._readDeclarations(!0,!0),this.fire({type:"endpage",id:n,pseudo:i,line:e,col:t})},_margin:function(){var e,t,r=this._tokenStream,n=this._margin_sym();return!!n&&(e=r.token().startLine,t=r.token().startCol,this.fire({type:"startpagemargin",margin:n,line:e,col:t}),this._readDeclarations(!0),this.fire({type:"endpagemargin",margin:n,line:e,col:t}),!0)},_margin_sym:function(){var e=this._tokenStream;return e.match([R.TOPLEFTCORNER_SYM,R.TOPLEFT_SYM,R.TOPCENTER_SYM,R.TOPRIGHT_SYM,R.TOPRIGHTCORNER_SYM,R.BOTTOMLEFTCORNER_SYM,R.BOTTOMLEFT_SYM,R.BOTTOMCENTER_SYM,R.BOTTOMRIGHT_SYM,R.BOTTOMRIGHTCORNER_SYM,R.LEFTTOP_SYM,R.LEFTMIDDLE_SYM,R.LEFTBOTTOM_SYM,R.RIGHTTOP_SYM,R.RIGHTMIDDLE_SYM,R.RIGHTBOTTOM_SYM])?i.fromToken(e.token()):null},_pseudo_page:function(){var e=this._tokenStream;return e.mustMatch(R.COLON),e.mustMatch(R.IDENT),e.token().value},_font_face:function(){var e,t,r=this._tokenStream;r.mustMatch(R.FONT_FACE_SYM),e=r.token().startLine,t=r.token().startCol,this._readWhitespace(),this.fire({type:"startfontface",line:e,col:t}),this._readDeclarations(!0),this.fire({type:"endfontface",line:e,col:t})},_viewport:function(){var e,t,r=this._tokenStream;r.mustMatch(R.VIEWPORT_SYM),e=r.token().startLine,t=r.token().startCol,this._readWhitespace(),this.fire({type:"startviewport",line:e,col:t}),this._readDeclarations(!0),this.fire({type:"endviewport",line:e,col:t})},_document:function(){var e,t=this._tokenStream,r=[],n="";for(t.mustMatch(R.DOCUMENT_SYM),e=t.token(),/^@\-([^\-]+)\-/.test(e.value)&&(n=RegExp.$1),this._readWhitespace(),r.push(this._document_function());t.match(R.COMMA);)this._readWhitespace(),r.push(this._document_function());for(t.mustMatch(R.LBRACE),this._readWhitespace(),this.fire({type:"startdocument",functions:r,prefix:n,line:e.startLine,col:e.startCol});;)if(t.peek()===R.PAGE_SYM)this._page();else if(t.peek()===R.FONT_FACE_SYM)this._font_face();else if(t.peek()===R.VIEWPORT_SYM)this._viewport();else if(t.peek()===R.MEDIA_SYM)this._media();else if(!this._ruleset())break;t.mustMatch(R.RBRACE),this._readWhitespace(),this.fire({type:"enddocument",functions:r,prefix:n,line:e.startLine,col:e.startCol})},_document_function:function(){var e,t=this._tokenStream;return t.match(R.URI)?(e=t.token().value,this._readWhitespace()):e=this._function(),e},_operator:function(e){var t=this._tokenStream,r=null;return(t.match([R.SLASH,R.COMMA])||e&&t.match([R.PLUS,R.STAR,R.MINUS]))&&(r=t.token(),this._readWhitespace()),r?f.fromToken(r):null},_combinator:function(){var e,t=this._tokenStream,r=null;return t.match([R.PLUS,R.GREATER,R.TILDE])&&(r=new o((e=t.token()).value,e.startLine,e.startCol),this._readWhitespace()),r},_unary_operator:function(){var e=this._tokenStream;return e.match([R.MINUS,R.PLUS])?e.token().value:null},_property:function(){var e,t,r,n,i=this._tokenStream,a=null,o=null;return i.peek()===R.STAR&&this.options.starHack&&(i.get(),o=(t=i.token()).value,r=t.startLine,n=t.startCol),i.match(R.IDENT)&&("_"===(e=(t=i.token()).value).charAt(0)&&this.options.underscoreHack&&(o="_",e=e.substring(1)),a=new h(e,o,r||t.startLine,n||t.startCol),this._readWhitespace()),a},_ruleset:function(){var e,t=this._tokenStream;try{e=this._selectors_group()}catch(e){if(!(e instanceof r)||this.options.strict)throw e;if(this.fire({type:"error",error:e,message:e.message,line:e.line,col:e.col}),t.advance([R.RBRACE])!==R.RBRACE)throw e;return!0}return e&&(this.fire({type:"startrule",selectors:e,line:e[0].line,col:e[0].col}),this._readDeclarations(!0),this.fire({type:"endrule",selectors:e,line:e[0].line,col:e[0].col})),e},_selectors_group:function(){var e,t=this._tokenStream,r=[];if(null!==(e=this._selector()))for(r.push(e);t.match(R.COMMA);)this._readWhitespace(),null!==(e=this._selector())?r.push(e):this._unexpectedToken(t.LT(1));return r.length?r:null},_selector:function(){var e=this._tokenStream,t=[],r=null,n=null,i=null;if(null===(r=this._simple_selector_sequence()))return null;for(t.push(r);;)if(null!==(n=this._combinator()))t.push(n),null===(r=this._simple_selector_sequence())?this._unexpectedToken(e.LT(1)):t.push(r);else{if(!this._readWhitespace())break;i=new o(e.token().value,e.token().startLine,e.token().startCol),n=this._combinator(),null===(r=this._simple_selector_sequence())?null!==n&&this._unexpectedToken(e.LT(1)):(null!==n?t.push(n):t.push(i),t.push(r))}return new g(t,t[0].line,t[0].col)},_simple_selector_sequence:function(){var e,t,r=this._tokenStream,n=null,i=[],a="",o=[function(){return r.match(R.HASH)?new v(r.token().value,"id",r.token().startLine,r.token().startCol):null},this._class,this._attrib,this._pseudo,this._negation],s=0,c=o.length,l=null;for(e=r.LT(1).startLine,t=r.LT(1).startCol,(n=this._type_selector())||(n=this._universal()),null!==n&&(a+=n);r.peek()!==R.S;){for(;s<c&&null===l;)l=o[s++].call(this);if(null===l){if(""===a)return null;break}s=0,i.push(l),a+=l.toString(),l=null}return""!==a?new b(n,i,a,e,t):null},_type_selector:function(){var e=this._tokenStream,t=this._namespace_prefix(),r=this._element_name();return r?(t&&(r.text=t+r.text,r.col-=t.length),r):(t&&(e.unget(),t.length>1&&e.unget()),null)},_class:function(){var e,t=this._tokenStream;return t.match(R.DOT)?(t.mustMatch(R.IDENT),new v("."+(e=t.token()).value,"class",e.startLine,e.startCol-1)):null},_element_name:function(){var e,t=this._tokenStream;return t.match(R.IDENT)?new v((e=t.token()).value,"elementName",e.startLine,e.startCol):null},_namespace_prefix:function(){var e=this._tokenStream,t="";return e.LA(1)!==R.PIPE&&e.LA(2)!==R.PIPE||(e.match([R.IDENT,R.STAR])&&(t+=e.token().value),e.mustMatch(R.PIPE),t+="|"),t.length?t:null},_universal:function(){var e,t=this._tokenStream,r="";return(e=this._namespace_prefix())&&(r+=e),t.match(R.STAR)&&(r+="*"),r.length?r:null},_attrib:function(){var e,t,r=this._tokenStream,n=null;return r.match(R.LBRACKET)?(n=(t=r.token()).value,n+=this._readWhitespace(),(e=this._namespace_prefix())&&(n+=e),r.mustMatch(R.IDENT),n+=r.token().value,n+=this._readWhitespace(),r.match([R.PREFIXMATCH,R.SUFFIXMATCH,R.SUBSTRINGMATCH,R.EQUALS,R.INCLUDES,R.DASHMATCH])&&(n+=r.token().value,n+=this._readWhitespace(),r.mustMatch([R.IDENT,R.STRING]),n+=r.token().value,n+=this._readWhitespace()),r.mustMatch(R.RBRACKET),new v(n+"]","attribute",t.startLine,t.startCol)):null},_pseudo:function(){var e,t,r=this._tokenStream,n=null,i=":";return r.match(R.COLON)&&(r.match(R.COLON)&&(i+=":"),r.match(R.IDENT)?(n=r.token().value,e=r.token().startLine,t=r.token().startCol-i.length):r.peek()===R.FUNCTION&&(e=r.LT(1).startLine,t=r.LT(1).startCol-i.length,n=this._functional_pseudo()),n&&(n=new v(i+n,"pseudo",e,t))),n},_functional_pseudo:function(){var e=this._tokenStream,t=null;return e.match(R.FUNCTION)&&(t=e.token().value,t+=this._readWhitespace(),t+=this._expression(),e.mustMatch(R.RPAREN),t+=")"),t},_expression:function(){for(var e=this._tokenStream,t="";e.match([R.PLUS,R.MINUS,R.DIMENSION,R.NUMBER,R.STRING,R.IDENT,R.LENGTH,R.FREQ,R.ANGLE,R.TIME,R.RESOLUTION,R.SLASH]);)t+=e.token().value,t+=this._readWhitespace();return t.length?t:null},_negation:function(){var e,t,r,n=this._tokenStream,i="",a=null;return n.match(R.NOT)&&(i=n.token().value,e=n.token().startLine,t=n.token().startCol,i+=this._readWhitespace(),i+=r=this._negation_arg(),i+=this._readWhitespace(),n.match(R.RPAREN),(a=new v(i+=n.token().value,"not",e,t)).args.push(r)),a},_negation_arg:function(){var e,t,r=this._tokenStream,n=[this._type_selector,this._universal,function(){return r.match(R.HASH)?new v(r.token().value,"id",r.token().startLine,r.token().startCol):null},this._class,this._attrib,this._pseudo],i=null,a=0,o=n.length;for(e=r.LT(1).startLine,t=r.LT(1).startCol;a<o&&null===i;)i=n[a].call(this),a++;return null===i&&this._unexpectedToken(r.LT(1)),"elementName"===i.type?new b(i,[],i.toString(),e,t):new b(null,[i],i.toString(),e,t)},_declaration:function(){var e=this._tokenStream,t=null,r=null,n=null,i=null,a="";if(null!==(t=this._property())){e.mustMatch(R.COLON),this._readWhitespace(),(r=this._expr())&&0!==r.length||this._unexpectedToken(e.LT(1)),n=this._prio(),a=t.toString(),(this.options.starHack&&"*"===t.hack||this.options.underscoreHack&&"_"===t.hack)&&(a=t.text);try{this._validateProperty(a,r)}catch(e){i=e}return this.fire({type:"property",property:t,value:r,important:n,line:t.line,col:t.col,invalid:i}),!0}return!1},_prio:function(){var e=this._tokenStream.match(R.IMPORTANT_SYM);return this._readWhitespace(),e},_expr:function(e){var t=[],r=null,n=null;if(null!==(r=this._term(e)))for(t.push(r);;){if((n=this._operator(e))&&t.push(n),null===(r=this._term(e)))break;t.push(r)}return t.length>0?new d(t,t[0].line,t[0].col):null},_term:function(e){var t,r,n,i,a=this._tokenStream,o=null,s=null;return null!==(t=this._unary_operator())&&(n=a.token().startLine,i=a.token().startCol),a.peek()===R.IE_FUNCTION&&this.options.ieFilters?(o=this._ie_function(),null===t&&(n=a.token().startLine,i=a.token().startCol)):e&&a.match([R.LPAREN,R.LBRACE,R.LBRACKET])?(s=(r=a.token()).endChar,o=r.value+this._expr(e).text,null===t&&(n=a.token().startLine,i=a.token().startCol),a.mustMatch(R.type(s)),o+=s,this._readWhitespace()):a.match([R.NUMBER,R.PERCENTAGE,R.LENGTH,R.ANGLE,R.TIME,R.FREQ,R.STRING,R.IDENT,R.URI,R.UNICODE_RANGE])?(o=a.token().value,null===t&&(n=a.token().startLine,i=a.token().startCol),this._readWhitespace()):null===(r=this._hexcolor())?(null===t&&(n=a.LT(1).startLine,i=a.LT(1).startCol),null===o&&(o=a.LA(3)===R.EQUALS&&this.options.ieFilters?this._ie_function():this._function())):(o=r.value,null===t&&(n=r.startLine,i=r.startCol)),null!==o?new f(null!==t?t+o:o,n,i):null},_function:function(){var e,t=this._tokenStream,r=null;if(t.match(R.FUNCTION)){if(r=t.token().value,this._readWhitespace(),r+=this._expr(!0),this.options.ieFilters&&t.peek()===R.EQUALS)do{for(this._readWhitespace()&&(r+=t.token().value),t.LA(0)===R.COMMA&&(r+=t.token().value),t.match(R.IDENT),r+=t.token().value,t.match(R.EQUALS),r+=t.token().value,e=t.peek();e!==R.COMMA&&e!==R.S&&e!==R.RPAREN;)t.get(),r+=t.token().value,e=t.peek()}while(t.match([R.COMMA,R.S]));t.match(R.RPAREN),r+=")",this._readWhitespace()}return r},_ie_function:function(){var e,t=this._tokenStream,r=null;if(t.match([R.IE_FUNCTION,R.FUNCTION])){r=t.token().value;do{for(this._readWhitespace()&&(r+=t.token().value),t.LA(0)===R.COMMA&&(r+=t.token().value),t.match(R.IDENT),r+=t.token().value,t.match(R.EQUALS),r+=t.token().value,e=t.peek();e!==R.COMMA&&e!==R.S&&e!==R.RPAREN;)t.get(),r+=t.token().value,e=t.peek()}while(t.match([R.COMMA,R.S]));t.match(R.RPAREN),r+=")",this._readWhitespace()}return r},_hexcolor:function(){var e,t=this._tokenStream,n=null;if(t.match(R.HASH)){if(e=(n=t.token()).value,!/#[a-f0-9]{3,6}/i.test(e))throw new r("Expected a hex color but found '"+e+"' at line "+n.startLine+", col "+n.startCol+".",n.startLine,n.startCol);this._readWhitespace()}return n},_keyframes:function(){var e,t,r,n=this._tokenStream,i="";for(n.mustMatch(R.KEYFRAMES_SYM),e=n.token(),/^@\-([^\-]+)\-/.test(e.value)&&(i=RegExp.$1),this._readWhitespace(),r=this._keyframe_name(),this._readWhitespace(),n.mustMatch(R.LBRACE),this.fire({type:"startkeyframes",name:r,prefix:i,line:e.startLine,col:e.startCol}),this._readWhitespace(),t=n.peek();t===R.IDENT||t===R.PERCENTAGE;)this._keyframe_rule(),this._readWhitespace(),t=n.peek();this.fire({type:"endkeyframes",name:r,prefix:i,line:e.startLine,col:e.startCol}),this._readWhitespace(),n.mustMatch(R.RBRACE)},_keyframe_name:function(){var e=this._tokenStream;return e.mustMatch([R.IDENT,R.STRING]),i.fromToken(e.token())},_keyframe_rule:function(){var e=this._key_list();this.fire({type:"startkeyframerule",keys:e,line:e[0].line,col:e[0].col}),this._readDeclarations(!0),this.fire({type:"endkeyframerule",keys:e,line:e[0].line,col:e[0].col})},_key_list:function(){var e=this._tokenStream,t=[];for(t.push(this._key()),this._readWhitespace();e.match(R.COMMA);)this._readWhitespace(),t.push(this._key()),this._readWhitespace();return t},_key:function(){var e,t=this._tokenStream;if(t.match(R.PERCENTAGE))return i.fromToken(t.token());if(t.match(R.IDENT)){if(e=t.token(),/from|to/i.test(e.value))return i.fromToken(e);t.unget()}this._unexpectedToken(t.LT(1))},_skipCruft:function(){for(;this._tokenStream.match([R.S,R.CDO,R.CDC]););},_readDeclarations:function(e,t){var n,i=this._tokenStream;this._readWhitespace(),e&&i.mustMatch(R.LBRACE),this._readWhitespace();try{for(;;){if(i.match(R.SEMICOLON)||t&&this._margin());else{if(!this._declaration())break;if(!i.match(R.SEMICOLON))break}this._readWhitespace()}i.mustMatch(R.RBRACE),this._readWhitespace()}catch(e){if(!(e instanceof r)||this.options.strict)throw e;if(this.fire({type:"error",error:e,message:e.message,line:e.line,col:e.col}),(n=i.advance([R.SEMICOLON,R.RBRACE]))===R.SEMICOLON)this._readDeclarations(!1,t);else if(n!==R.RBRACE)throw e}},_readWhitespace:function(){for(var e=this._tokenStream,t="";e.match(R.S);)t+=e.token().value;return t},_unexpectedToken:function(e){throw new r("Unexpected token '"+e.value+"' at line "+e.startLine+", col "+e.startCol+".",e.startLine,e.startCol)},_verifyEnd:function(){this._tokenStream.LA(1)!==R.EOF&&this._unexpectedToken(this._tokenStream.LT(1))},_validateProperty:function(e,t){L.validate(e,t)},parse:function(e){this._tokenStream=new A(e,R),this._stylesheet()},parseStyleSheet:function(e){return this.parse(e)},parseMediaQuery:function(e){this._tokenStream=new A(e,R);var t=this._media_query();return this._verifyEnd(),t},parsePropertyValue:function(e){this._tokenStream=new A(e,R),this._readWhitespace();var t=this._expr();return this._readWhitespace(),this._verifyEnd(),t},parseRule:function(e){this._tokenStream=new A(e,R),this._readWhitespace();var t=this._ruleset();return this._readWhitespace(),this._verifyEnd(),t},parseSelector:function(e){this._tokenStream=new A(e,R),this._readWhitespace();var t=this._selector();return this._readWhitespace(),this._verifyEnd(),t},parseStyleAttribute:function(e){e+="}",this._tokenStream=new A(e,R),this._readDeclarations()}};for(t in a)Object.prototype.hasOwnProperty.call(a,t)&&(n[t]=a[t]);return n}();var u={__proto__:null,"align-items":"flex-start | flex-end | center | baseline | stretch","align-content":"flex-start | flex-end | center | space-between | space-around | stretch","align-self":"auto | flex-start | flex-end | center | baseline | stretch","-webkit-align-items":"flex-start | flex-end | center | baseline | stretch","-webkit-align-content":"flex-start | flex-end | center | space-between | space-around | stretch","-webkit-align-self":"auto | flex-start | flex-end | center | baseline | stretch","alignment-adjust":"auto | baseline | before-edge | text-before-edge | middle | central | after-edge | text-after-edge | ideographic | alphabetic | hanging | mathematical | <percentage> | <length>","alignment-baseline":"baseline | use-script | before-edge | text-before-edge | after-edge | text-after-edge | central | middle | ideographic | alphabetic | hanging | mathematical",animation:1,"animation-delay":{multi:"<time>",comma:!0},"animation-direction":{multi:"normal | alternate",comma:!0},"animation-duration":{multi:"<time>",comma:!0},"animation-fill-mode":{multi:"none | forwards | backwards | both",comma:!0},"animation-iteration-count":{multi:"<number> | infinite",comma:!0},"animation-name":{multi:"none | <ident>",comma:!0},"animation-play-state":{multi:"running | paused",comma:!0},"animation-timing-function":1,"-moz-animation-delay":{multi:"<time>",comma:!0},"-moz-animation-direction":{multi:"normal | alternate",comma:!0},"-moz-animation-duration":{multi:"<time>",comma:!0},"-moz-animation-iteration-count":{multi:"<number> | infinite",comma:!0},"-moz-animation-name":{multi:"none | <ident>",comma:!0},"-moz-animation-play-state":{multi:"running | paused",comma:!0},"-ms-animation-delay":{multi:"<time>",comma:!0},"-ms-animation-direction":{multi:"normal | alternate",comma:!0},"-ms-animation-duration":{multi:"<time>",comma:!0},"-ms-animation-iteration-count":{multi:"<number> | infinite",comma:!0},"-ms-animation-name":{multi:"none | <ident>",comma:!0},"-ms-animation-play-state":{multi:"running | paused",comma:!0},"-webkit-animation-delay":{multi:"<time>",comma:!0},"-webkit-animation-direction":{multi:"normal | alternate",comma:!0},"-webkit-animation-duration":{multi:"<time>",comma:!0},"-webkit-animation-fill-mode":{multi:"none | forwards | backwards | both",comma:!0},"-webkit-animation-iteration-count":{multi:"<number> | infinite",comma:!0},"-webkit-animation-name":{multi:"none | <ident>",comma:!0},"-webkit-animation-play-state":{multi:"running | paused",comma:!0},"-o-animation-delay":{multi:"<time>",comma:!0},"-o-animation-direction":{multi:"normal | alternate",comma:!0},"-o-animation-duration":{multi:"<time>",comma:!0},"-o-animation-iteration-count":{multi:"<number> | infinite",comma:!0},"-o-animation-name":{multi:"none | <ident>",comma:!0},"-o-animation-play-state":{multi:"running | paused",comma:!0},appearance:"icon | window | desktop | workspace | document | tooltip | dialog | button | push-button | hyperlink | radio | radio-button | checkbox | menu-item | tab | menu | menubar | pull-down-menu | pop-up-menu | list-menu | radio-group | checkbox-group | outline-tree | range | field | combo-box | signature | password | normal | none | inherit",azimuth:function(e){var t,r=!1,n=!1;if(I.isAny(e,"<angle> | leftwards | rightwards | inherit")||(I.isAny(e,"behind")&&(r=!0,n=!0),I.isAny(e,"left-side | far-left | left | center-left | center | center-right | right | far-right | right-side")&&(n=!0,r||I.isAny(e,"behind"))),e.hasNext())throw t=e.next(),new O(n?"Expected end of value but found '"+t+"'.":"Expected (<'azimuth'>) but found '"+t+"'.",t.line,t.col)},"backface-visibility":"visible | hidden",background:1,"background-attachment":{multi:"<attachment>",comma:!0},"background-clip":{multi:"<box>",comma:!0},"background-color":"<color> | inherit","background-image":{multi:"<bg-image>",comma:!0},"background-origin":{multi:"<box>",comma:!0},"background-position":{multi:"<bg-position>",comma:!0},"background-repeat":{multi:"<repeat-style>"},"background-size":{multi:"<bg-size>",comma:!0},"baseline-shift":"baseline | sub | super | <percentage> | <length>",behavior:1,binding:1,bleed:"<length>","bookmark-label":"<content> | <attr> | <string>","bookmark-level":"none | <integer>","bookmark-state":"open | closed","bookmark-target":"none | <uri> | <attr>",border:"<border-width> || <border-style> || <color>","border-bottom":"<border-width> || <border-style> || <color>","border-bottom-color":"<color> | inherit","border-bottom-left-radius":"<x-one-radius>","border-bottom-right-radius":"<x-one-radius>","border-bottom-style":"<border-style>","border-bottom-width":"<border-width>","border-collapse":"collapse | separate | inherit","border-color":{multi:"<color> | inherit",max:4},"border-image":1,"border-image-outset":{multi:"<length> | <number>",max:4},"border-image-repeat":{multi:"stretch | repeat | round",max:2},"border-image-slice":function(e){var t,r=!1,n=!1,i=0;for(I.isAny(e,"fill")&&(n=!0,r=!0);e.hasNext()&&i<4&&(r=I.isAny(e,"<number> | <percentage>"));)i++;if(n?r=!0:I.isAny(e,"fill"),e.hasNext())throw t=e.next(),new O(r?"Expected end of value but found '"+t+"'.":"Expected ([<number> | <percentage>]{1,4} && fill?) but found '"+t+"'.",t.line,t.col)},"border-image-source":"<image> | none","border-image-width":{multi:"<length> | <percentage> | <number> | auto",max:4},"border-left":"<border-width> || <border-style> || <color>","border-left-color":"<color> | inherit","border-left-style":"<border-style>","border-left-width":"<border-width>","border-radius":function(e){for(var t,r=!1,n=!1,i=0,a=8;e.hasNext()&&i<a;){if(!(r=I.isAny(e,"<length> | <percentage> | inherit"))){if(!("/"===String(e.peek())&&i>0)||n)break;n=!0,a=i+5,e.next()}i++}if(e.hasNext())throw t=e.next(),new O(r?"Expected end of value but found '"+t+"'.":"Expected (<'border-radius'>) but found '"+t+"'.",t.line,t.col)},"border-right":"<border-width> || <border-style> || <color>","border-right-color":"<color> | inherit","border-right-style":"<border-style>","border-right-width":"<border-width>","border-spacing":{multi:"<length> | inherit",max:2},"border-style":{multi:"<border-style>",max:4},"border-top":"<border-width> || <border-style> || <color>","border-top-color":"<color> | inherit","border-top-left-radius":"<x-one-radius>","border-top-right-radius":"<x-one-radius>","border-top-style":"<border-style>","border-top-width":"<border-width>","border-width":{multi:"<border-width>",max:4},bottom:"<margin-width> | inherit","-moz-box-align":"start | end | center | baseline | stretch","-moz-box-decoration-break":"slice |clone","-moz-box-direction":"normal | reverse | inherit","-moz-box-flex":"<number>","-moz-box-flex-group":"<integer>","-moz-box-lines":"single | multiple","-moz-box-ordinal-group":"<integer>","-moz-box-orient":"horizontal | vertical | inline-axis | block-axis | inherit","-moz-box-pack":"start | end | center | justify","-o-box-decoration-break":"slice | clone","-webkit-box-align":"start | end | center | baseline | stretch","-webkit-box-decoration-break":"slice |clone","-webkit-box-direction":"normal | reverse | inherit","-webkit-box-flex":"<number>","-webkit-box-flex-group":"<integer>","-webkit-box-lines":"single | multiple","-webkit-box-ordinal-group":"<integer>","-webkit-box-orient":"horizontal | vertical | inline-axis | block-axis | inherit","-webkit-box-pack":"start | end | center | justify","box-decoration-break":"slice | clone","box-shadow":function(e){var t;if(I.isAny(e,"none")){if(e.hasNext())throw new O("Expected end of value but found '"+(t=e.next())+"'.",t.line,t.col)}else L.multiProperty("<shadow>",e,!0,1/0)},"box-sizing":"content-box | border-box | inherit","break-after":"auto | always | avoid | left | right | page | column | avoid-page | avoid-column","break-before":"auto | always | avoid | left | right | page | column | avoid-page | avoid-column","break-inside":"auto | avoid | avoid-page | avoid-column","caption-side":"top | bottom | inherit",clear:"none | right | left | both | inherit",clip:1,color:"<color> | inherit","color-profile":1,"column-count":"<integer> | auto","column-fill":"auto | balance","column-gap":"<length> | normal","column-rule":"<border-width> || <border-style> || <color>","column-rule-color":"<color>","column-rule-style":"<border-style>","column-rule-width":"<border-width>","column-span":"none | all","column-width":"<length> | auto",columns:1,content:1,"counter-increment":1,"counter-reset":1,crop:"<shape> | auto",cue:"cue-after | cue-before | inherit","cue-after":1,"cue-before":1,cursor:1,direction:"ltr | rtl | inherit",display:"inline | block | list-item | inline-block | table | inline-table | table-row-group | table-header-group | table-footer-group | table-row | table-column-group | table-column | table-cell | table-caption | grid | inline-grid | run-in | ruby | ruby-base | ruby-text | ruby-base-container | ruby-text-container | contents | none | inherit | -moz-box | -moz-inline-block | -moz-inline-box | -moz-inline-grid | -moz-inline-stack | -moz-inline-table | -moz-grid | -moz-grid-group | -moz-grid-line | -moz-groupbox | -moz-deck | -moz-popup | -moz-stack | -moz-marker | -webkit-box | -webkit-inline-box | -ms-flexbox | -ms-inline-flexbox | flex | -webkit-flex | inline-flex | -webkit-inline-flex","dominant-baseline":1,"drop-initial-after-adjust":"central | middle | after-edge | text-after-edge | ideographic | alphabetic | mathematical | <percentage> | <length>","drop-initial-after-align":"baseline | use-script | before-edge | text-before-edge | after-edge | text-after-edge | central | middle | ideographic | alphabetic | hanging | mathematical","drop-initial-before-adjust":"before-edge | text-before-edge | central | middle | hanging | mathematical | <percentage> | <length>","drop-initial-before-align":"caps-height | baseline | use-script | before-edge | text-before-edge | after-edge | text-after-edge | central | middle | ideographic | alphabetic | hanging | mathematical","drop-initial-size":"auto | line | <length> | <percentage>","drop-initial-value":"initial | <integer>",elevation:"<angle> | below | level | above | higher | lower | inherit","empty-cells":"show | hide | inherit",filter:1,fit:"fill | hidden | meet | slice","fit-position":1,flex:"<flex>","flex-basis":"<width>","flex-direction":"row | row-reverse | column | column-reverse","flex-flow":"<flex-direction> || <flex-wrap>","flex-grow":"<number>","flex-shrink":"<number>","flex-wrap":"nowrap | wrap | wrap-reverse","-webkit-flex":"<flex>","-webkit-flex-basis":"<width>","-webkit-flex-direction":"row | row-reverse | column | column-reverse","-webkit-flex-flow":"<flex-direction> || <flex-wrap>","-webkit-flex-grow":"<number>","-webkit-flex-shrink":"<number>","-webkit-flex-wrap":"nowrap | wrap | wrap-reverse","-ms-flex":"<flex>","-ms-flex-align":"start | end | center | stretch | baseline","-ms-flex-direction":"row | row-reverse | column | column-reverse | inherit","-ms-flex-order":"<number>","-ms-flex-pack":"start | end | center | justify","-ms-flex-wrap":"nowrap | wrap | wrap-reverse",float:"left | right | none | inherit","float-offset":1,font:1,"font-family":1,"font-feature-settings":"<feature-tag-value> | normal | inherit","font-kerning":"auto | normal | none | initial | inherit | unset","font-size":"<absolute-size> | <relative-size> | <length> | <percentage> | inherit","font-size-adjust":"<number> | none | inherit","font-stretch":"normal | ultra-condensed | extra-condensed | condensed | semi-condensed | semi-expanded | expanded | extra-expanded | ultra-expanded | inherit","font-style":"normal | italic | oblique | inherit","font-variant":"normal | small-caps | inherit","font-variant-caps":"normal | small-caps | all-small-caps | petite-caps | all-petite-caps | unicase | titling-caps","font-variant-position":"normal | sub | super | inherit | initial | unset","font-weight":"normal | bold | bolder | lighter | 100 | 200 | 300 | 400 | 500 | 600 | 700 | 800 | 900 | inherit",grid:1,"grid-area":1,"grid-auto-columns":1,"grid-auto-flow":1,"grid-auto-position":1,"grid-auto-rows":1,"grid-cell-stacking":"columns | rows | layer","grid-column":1,"grid-columns":1,"grid-column-align":"start | end | center | stretch","grid-column-sizing":1,"grid-column-start":1,"grid-column-end":1,"grid-column-span":"<integer>","grid-flow":"none | rows | columns","grid-layer":"<integer>","grid-row":1,"grid-rows":1,"grid-row-align":"start | end | center | stretch","grid-row-start":1,"grid-row-end":1,"grid-row-span":"<integer>","grid-row-sizing":1,"grid-template":1,"grid-template-areas":1,"grid-template-columns":1,"grid-template-rows":1,"hanging-punctuation":1,height:"<margin-width> | <content-sizing> | inherit","hyphenate-after":"<integer> | auto","hyphenate-before":"<integer> | auto","hyphenate-character":"<string> | auto","hyphenate-lines":"no-limit | <integer>","hyphenate-resource":1,hyphens:"none | manual | auto",icon:1,"image-orientation":"angle | auto","image-rendering":1,"image-resolution":1,"ime-mode":"auto | normal | active | inactive | disabled | inherit","inline-box-align":"initial | last | <integer>","justify-content":"flex-start | flex-end | center | space-between | space-around","-webkit-justify-content":"flex-start | flex-end | center | space-between | space-around",left:"<margin-width> | inherit","letter-spacing":"<length> | normal | inherit","line-height":"<number> | <length> | <percentage> | normal | inherit","line-break":"auto | loose | normal | strict","line-stacking":1,"line-stacking-ruby":"exclude-ruby | include-ruby","line-stacking-shift":"consider-shifts | disregard-shifts","line-stacking-strategy":"inline-line-height | block-line-height | max-height | grid-height","list-style":1,"list-style-image":"<uri> | none | inherit","list-style-position":"inside | outside | inherit","list-style-type":"disc | circle | square | decimal | decimal-leading-zero | lower-roman | upper-roman | lower-greek | lower-latin | upper-latin | armenian | georgian | lower-alpha | upper-alpha | none | inherit",margin:{multi:"<margin-width> | inherit",max:4},"margin-bottom":"<margin-width> | inherit","margin-left":"<margin-width> | inherit","margin-right":"<margin-width> | inherit","margin-top":"<margin-width> | inherit",mark:1,"mark-after":1,"mark-before":1,marks:1,"marquee-direction":1,"marquee-play-count":1,"marquee-speed":1,"marquee-style":1,"max-height":"<length> | <percentage> | <content-sizing> | none | inherit","max-width":"<length> | <percentage> | <content-sizing> | none | inherit","min-height":"<length> | <percentage> | <content-sizing> | contain-floats | -moz-contain-floats | -webkit-contain-floats | inherit","min-width":"<length> | <percentage> | <content-sizing> | contain-floats | -moz-contain-floats | -webkit-contain-floats | inherit","move-to":1,"nav-down":1,"nav-index":1,"nav-left":1,"nav-right":1,"nav-up":1,"object-fit":"fill | contain | cover | none | scale-down","object-position":"<bg-position>",opacity:"<number> | inherit",order:"<integer>","-webkit-order":"<integer>",orphans:"<integer> | inherit",outline:1,"outline-color":"<color> | invert | inherit","outline-offset":1,"outline-style":"<border-style> | inherit","outline-width":"<border-width> | inherit",overflow:"visible | hidden | scroll | auto | inherit","overflow-style":1,"overflow-wrap":"normal | break-word","overflow-x":1,"overflow-y":1,padding:{multi:"<padding-width> | inherit",max:4},"padding-bottom":"<padding-width> | inherit","padding-left":"<padding-width> | inherit","padding-right":"<padding-width> | inherit","padding-top":"<padding-width> | inherit",page:1,"page-break-after":"auto | always | avoid | left | right | inherit","page-break-before":"auto | always | avoid | left | right | inherit","page-break-inside":"auto | avoid | inherit","page-policy":1,pause:1,"pause-after":1,"pause-before":1,perspective:1,"perspective-origin":1,phonemes:1,pitch:1,"pitch-range":1,"play-during":1,"pointer-events":"auto | none | visiblePainted | visibleFill | visibleStroke | visible | painted | fill | stroke | all | inherit",position:"static | relative | absolute | fixed | inherit","presentation-level":1,"punctuation-trim":1,quotes:1,"rendering-intent":1,resize:1,rest:1,"rest-after":1,"rest-before":1,richness:1,right:"<margin-width> | inherit",rotation:1,"rotation-point":1,"ruby-align":1,"ruby-overhang":1,"ruby-position":1,"ruby-span":1,size:1,speak:"normal | none | spell-out | inherit","speak-header":"once | always | inherit","speak-numeral":"digits | continuous | inherit","speak-punctuation":"code | none | inherit","speech-rate":1,src:1,stress:1,"string-set":1,"table-layout":"auto | fixed | inherit","tab-size":"<integer> | <length>",target:1,"target-name":1,"target-new":1,"target-position":1,"text-align":"left | right | center | justify | match-parent | start | end | inherit","text-align-last":1,"text-decoration":1,"text-emphasis":1,"text-height":1,"text-indent":"<length> | <percentage> | inherit","text-justify":"auto | none | inter-word | inter-ideograph | inter-cluster | distribute | kashida","text-outline":1,"text-overflow":1,"text-rendering":"auto | optimizeSpeed | optimizeLegibility | geometricPrecision | inherit","text-shadow":1,"text-transform":"capitalize | uppercase | lowercase | none | inherit","text-wrap":"normal | none | avoid",top:"<margin-width> | inherit","-ms-touch-action":"auto | none | pan-x | pan-y | pan-left | pan-right | pan-up | pan-down | manipulation","touch-action":"auto | none | pan-x | pan-y | pan-left | pan-right | pan-up | pan-down | manipulation",transform:1,"transform-origin":1,"transform-style":1,transition:1,"transition-delay":1,"transition-duration":1,"transition-property":1,"transition-timing-function":1,"unicode-bidi":"normal | embed | isolate | bidi-override | isolate-override | plaintext | inherit","user-modify":"read-only | read-write | write-only | inherit","user-select":"none | text | toggle | element | elements | all | inherit","vertical-align":"auto | use-script | baseline | sub | super | top | text-top | central | middle | bottom | text-bottom | <percentage> | <length> | inherit",visibility:"visible | hidden | collapse | inherit","voice-balance":1,"voice-duration":1,"voice-family":1,"voice-pitch":1,"voice-pitch-range":1,"voice-rate":1,"voice-stress":1,"voice-volume":1,volume:1,"white-space":"normal | pre | nowrap | pre-wrap | pre-line | inherit | -pre-wrap | -o-pre-wrap | -moz-pre-wrap | -hp-pre-wrap","white-space-collapse":1,widows:"<integer> | inherit",width:"<length> | <percentage> | <content-sizing> | auto | inherit","will-change":{multi:"<ident>",comma:!0},"word-break":"normal | keep-all | break-all","word-spacing":"<length> | normal | inherit","word-wrap":"normal | break-word","writing-mode":"horizontal-tb | vertical-rl | vertical-lr | lr-tb | rl-tb | tb-rl | bt-rl | tb-lr | bt-lr | lr-bt | rl-bt | lr | rl | tb | inherit","z-index":"<integer> | auto | inherit",zoom:"<number> | <percentage> | normal"};function h(e,t,r,n){i.call(this,e,r,n,l.PROPERTY_NAME_TYPE),this.hack=t}function d(e,t,r){i.call(this,e.join(" "),t,r,l.PROPERTY_VALUE_TYPE),this.parts=e}function p(e){this._i=0,this._parts=e.parts,this._marks=[],this.value=e}function f(e,t,r){var n;if(i.call(this,e,t,r,l.PROPERTY_VALUE_PART_TYPE),this.type="unknown",/^([+\-]?[\d\.]+)([a-z]+)$/i.test(e))switch(this.type="dimension",this.value=+RegExp.$1,this.units=RegExp.$2,this.units.toLowerCase()){case"em":case"rem":case"ex":case"px":case"cm":case"mm":case"in":case"pt":case"pc":case"ch":case"vh":case"vw":case"vmax":case"vmin":this.type="length";break;case"fr":this.type="grid";break;case"deg":case"rad":case"grad":this.type="angle";break;case"ms":case"s":this.type="time";break;case"hz":case"khz":this.type="frequency";break;case"dpi":case"dpcm":this.type="resolution"}else/^([+\-]?[\d\.]+)%$/i.test(e)?(this.type="percentage",this.value=+RegExp.$1):/^([+\-]?\d+)$/i.test(e)?(this.type="integer",this.value=+RegExp.$1):/^([+\-]?[\d\.]+)$/i.test(e)?(this.type="number",this.value=+RegExp.$1):/^#([a-f0-9]{3,6})/i.test(e)?(this.type="color",3===(n=RegExp.$1).length?(this.red=parseInt(n.charAt(0)+n.charAt(0),16),this.green=parseInt(n.charAt(1)+n.charAt(1),16),this.blue=parseInt(n.charAt(2)+n.charAt(2),16)):(this.red=parseInt(n.substring(0,2),16),this.green=parseInt(n.substring(2,4),16),this.blue=parseInt(n.substring(4,6),16))):/^rgb\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*\)/i.test(e)?(this.type="color",this.red=+RegExp.$1,this.green=+RegExp.$2,this.blue=+RegExp.$3):/^rgb\(\s*(\d+)%\s*,\s*(\d+)%\s*,\s*(\d+)%\s*\)/i.test(e)?(this.type="color",this.red=255*+RegExp.$1/100,this.green=255*+RegExp.$2/100,this.blue=255*+RegExp.$3/100):/^rgba\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*,\s*([\d\.]+)\s*\)/i.test(e)?(this.type="color",this.red=+RegExp.$1,this.green=+RegExp.$2,this.blue=+RegExp.$3,this.alpha=+RegExp.$4):/^rgba\(\s*(\d+)%\s*,\s*(\d+)%\s*,\s*(\d+)%\s*,\s*([\d\.]+)\s*\)/i.test(e)?(this.type="color",this.red=255*+RegExp.$1/100,this.green=255*+RegExp.$2/100,this.blue=255*+RegExp.$3/100,this.alpha=+RegExp.$4):/^hsl\(\s*(\d+)\s*,\s*(\d+)%\s*,\s*(\d+)%\s*\)/i.test(e)?(this.type="color",this.hue=+RegExp.$1,this.saturation=+RegExp.$2/100,this.lightness=+RegExp.$3/100):/^hsla\(\s*(\d+)\s*,\s*(\d+)%\s*,\s*(\d+)%\s*,\s*([\d\.]+)\s*\)/i.test(e)?(this.type="color",this.hue=+RegExp.$1,this.saturation=+RegExp.$2/100,this.lightness=+RegExp.$3/100,this.alpha=+RegExp.$4):/^url\(["']?([^\)"']+)["']?\)/i.test(e)?(this.type="uri",this.uri=RegExp.$1):/^([^\(]+)\(/i.test(e)?(this.type="function",this.name=RegExp.$1,this.value=e):/^"([^\n\r\f\\"]|\\\r\n|\\[^\r0-9a-f]|\\[0-9a-f]{1,6}(\r\n|[ \n\r\t\f])?)*"/i.test(e)||/^'([^\n\r\f\\']|\\\r\n|\\[^\r0-9a-f]|\\[0-9a-f]{1,6}(\r\n|[ \n\r\t\f])?)*'/i.test(e)?(this.type="string",this.value=f.parseString(e)):a[e.toLowerCase()]?(this.type="color",n=a[e.toLowerCase()].substring(1),this.red=parseInt(n.substring(0,2),16),this.green=parseInt(n.substring(2,4),16),this.blue=parseInt(n.substring(4,6),16)):/^[\,\/]$/.test(e)?(this.type="operator",this.value=e):/^[a-z\-_\u0080-\uFFFF][a-z0-9\-_\u0080-\uFFFF]*$/i.test(e)&&(this.type="identifier",this.value=e)}h.prototype=new i,h.prototype.constructor=h,h.prototype.toString=function(){return(this.hack?this.hack:"")+this.text},d.prototype=new i,d.prototype.constructor=d,p.prototype.count=function(){return this._parts.length},p.prototype.isFirst=function(){return 0===this._i},p.prototype.hasNext=function(){return this._i<this._parts.length},p.prototype.mark=function(){this._marks.push(this._i)},p.prototype.peek=function(e){return this.hasNext()?this._parts[this._i+(e||0)]:null},p.prototype.next=function(){return this.hasNext()?this._parts[this._i++]:null},p.prototype.previous=function(){return this._i>0?this._parts[--this._i]:null},p.prototype.restore=function(){this._marks.length&&(this._i=this._marks.pop())},f.prototype=new i,f.prototype.constructor=f,f.parseString=function(e){return(e=e.slice(1,-1)).replace(/\\(\r\n|[^\r0-9a-f]|[0-9a-f]{1,6}(\r\n|[ \n\r\t\f])?)/gi,(function(e,t){if(/^(\n|\r\n|\r|\f)$/.test(t))return"";var r=/^[0-9a-f]{1,6}/i.exec(t);if(r){var n=parseInt(r[0],16);return String.fromCodePoint?String.fromCodePoint(n):String.fromCharCode(n)}return t}))},f.serializeString=function(e){return'"'+e.replace(/["\r\n\f]/g,(function(e,t){return'"'===t?"\\"+t:"\\"+(String.codePointAt?String.codePointAt(0):String.charCodeAt(0)).toString(16)+" "}))+'"'},f.fromToken=function(e){return new f(e.value,e.startLine,e.startCol)};var m={__proto__:null,":first-letter":1,":first-line":1,":before":1,":after":1};function g(e,t,r){i.call(this,e.join(" "),t,r,l.SELECTOR_TYPE),this.parts=e,this.specificity=_.calculate(this)}function b(e,t,r,n,a){i.call(this,r,n,a,l.SELECTOR_PART_TYPE),this.elementName=e,this.modifiers=t}function v(e,t,r,n){i.call(this,e,r,n,l.SELECTOR_SUB_PART_TYPE),this.type=t,this.args=[]}function _(e,t,r,n){this.a=e,this.b=t,this.c=r,this.d=n}m.ELEMENT=1,m.CLASS=2,m.isElement=function(e){return 0===e.indexOf("::")||m[e.toLowerCase()]===m.ELEMENT},g.prototype=new i,g.prototype.constructor=g,b.prototype=new i,b.prototype.constructor=b,v.prototype=new i,v.prototype.constructor=v,_.prototype={constructor:_,compare:function(e){var t,r,n=["a","b","c","d"];for(t=0,r=n.length;t<r;t++){if(this[n[t]]<e[n[t]])return-1;if(this[n[t]]>e[n[t]])return 1}return 0},valueOf:function(){return 1e3*this.a+100*this.b+10*this.c+this.d},toString:function(){return this.a+","+this.b+","+this.c+","+this.d}},_.calculate=function(e){var t,r,n,i=0,a=0,o=0;function s(e){var t,r,n,c,l,u=e.elementName?e.elementName.text:"";for(u&&"*"!==u.charAt(u.length-1)&&o++,t=0,n=e.modifiers.length;t<n;t++)switch((l=e.modifiers[t]).type){case"class":case"attribute":a++;break;case"id":i++;break;case"pseudo":m.isElement(l.text)?o++:a++;break;case"not":for(r=0,c=l.args.length;r<c;r++)s(l.args[r])}}for(t=0,r=e.parts.length;t<r;t++)(n=e.parts[t])instanceof b&&s(n);return new _(0,i,a,o)};var y=/^[0-9a-fA-F]$/,w=/\n|\r\n|\r|\f/;function E(e){return null!==e&&y.test(e)}function k(e){return null!==e&&/\d/.test(e)}function T(e){return null!==e&&/\s/.test(e)}function S(e){return null!==e&&w.test(e)}function x(e){return null!==e&&/[a-z_\u0080-\uFFFF\\]/i.test(e)}function N(e){return null!==e&&(x(e)||/[0-9\-\\]/.test(e))}function C(e){return null!==e&&(x(e)||/\-\\/.test(e))}function A(e){t.call(this,e,R)}A.prototype=function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e}(new t,{_getToken:function(e){var t,r=this._reader,n=null,i=r.getLine(),a=r.getCol();for(t=r.read();t;){switch(t){case"/":n="*"===r.peek()?this.commentToken(t,i,a):this.charToken(t,i,a);break;case"|":case"~":case"^":case"$":case"*":n="="===r.peek()?this.comparisonToken(t,i,a):this.charToken(t,i,a);break;case'"':case"'":n=this.stringToken(t,i,a);break;case"#":n=N(r.peek())?this.hashToken(t,i,a):this.charToken(t,i,a);break;case".":n=k(r.peek())?this.numberToken(t,i,a):this.charToken(t,i,a);break;case"-":n="-"===r.peek()?this.htmlCommentEndToken(t,i,a):x(r.peek())?this.identOrFunctionToken(t,i,a):this.charToken(t,i,a);break;case"!":n=this.importantToken(t,i,a);break;case"@":n=this.atRuleToken(t,i,a);break;case":":n=this.notToken(t,i,a);break;case"<":n=this.htmlCommentStartToken(t,i,a);break;case"U":case"u":if("+"===r.peek()){n=this.unicodeRangeToken(t,i,a);break}default:n=k(t)?this.numberToken(t,i,a):T(t)?this.whitespaceToken(t,i,a):C(t)?this.identOrFunctionToken(t,i,a):this.charToken(t,i,a)}break}return n||null!==t||(n=this.createToken(R.EOF,null,i,a)),n},createToken:function(e,t,r,n,i){var a=this._reader;return{value:t,type:e,channel:(i=i||{}).channel,endChar:i.endChar,hide:i.hide||!1,startLine:r,startCol:n,endLine:a.getLine(),endCol:a.getCol()}},atRuleToken:function(e,t,r){var n=e,i=this._reader,a=R.CHAR;return i.mark(),n=e+this.readName(),(a=R.type(n.toLowerCase()))!==R.CHAR&&a!==R.UNKNOWN||(n.length>1?a=R.UNKNOWN_SYM:(a=R.CHAR,n=e,i.reset())),this.createToken(a,n,t,r)},charToken:function(e,t,r){var n=R.type(e),i={};return-1===n?n=R.CHAR:i.endChar=R[n].endChar,this.createToken(n,e,t,r,i)},commentToken:function(e,t,r){var n=this.readComment(e);return this.createToken(R.COMMENT,n,t,r)},comparisonToken:function(e,t,r){var n=e+this._reader.read(),i=R.type(n)||R.CHAR;return this.createToken(i,n,t,r)},hashToken:function(e,t,r){var n=this.readName(e);return this.createToken(R.HASH,n,t,r)},htmlCommentStartToken:function(e,t,r){var n=this._reader,i=e;return n.mark(),"\x3c!--"===(i+=n.readCount(3))?this.createToken(R.CDO,i,t,r):(n.reset(),this.charToken(e,t,r))},htmlCommentEndToken:function(e,t,r){var n=this._reader,i=e;return n.mark(),"--\x3e"===(i+=n.readCount(2))?this.createToken(R.CDC,i,t,r):(n.reset(),this.charToken(e,t,r))},identOrFunctionToken:function(e,t,r){var n=this._reader,i=this.readName(e),a=R.IDENT,o=["url(","url-prefix(","domain("];return"("===n.peek()?(i+=n.read(),o.indexOf(i.toLowerCase())>-1?(a=R.URI,i=this.readURI(i),o.indexOf(i.toLowerCase())>-1&&(a=R.FUNCTION)):a=R.FUNCTION):":"===n.peek()&&"progid"===i.toLowerCase()&&(i+=n.readTo("("),a=R.IE_FUNCTION),this.createToken(a,i,t,r)},importantToken:function(e,t,r){var n,i,a=this._reader,o=e,s=R.CHAR;for(a.mark(),i=a.read();i;){if("/"===i){if("*"!==a.peek())break;if(""===(n=this.readComment(i)))break}else{if(!T(i)){if(/i/i.test(i)){n=a.readCount(8),/mportant/i.test(n)&&(o+=i+n,s=R.IMPORTANT_SYM);break}break}o+=i+this.readWhitespace()}i=a.read()}return s===R.CHAR?(a.reset(),this.charToken(e,t,r)):this.createToken(s,o,t,r)},notToken:function(e,t,r){var n=this._reader,i=e;return n.mark(),":not("===(i+=n.readCount(4)).toLowerCase()?this.createToken(R.NOT,i,t,r):(n.reset(),this.charToken(e,t,r))},numberToken:function(e,t,r){var n,i=this._reader,a=this.readNumber(e),o=R.NUMBER,s=i.peek();return C(s)?(a+=n=this.readName(i.read()),o=/^em$|^ex$|^px$|^gd$|^rem$|^vw$|^vh$|^vmax$|^vmin$|^ch$|^cm$|^mm$|^in$|^pt$|^pc$/i.test(n)?R.LENGTH:/^deg|^rad$|^grad$/i.test(n)?R.ANGLE:/^ms$|^s$/i.test(n)?R.TIME:/^hz$|^khz$/i.test(n)?R.FREQ:/^dpi$|^dpcm$/i.test(n)?R.RESOLUTION:R.DIMENSION):"%"===s&&(a+=i.read(),o=R.PERCENTAGE),this.createToken(o,a,t,r)},stringToken:function(e,t,r){for(var n=e,i=e,a=this._reader,o=e,s=R.STRING,c=a.read();c&&(i+=c,c!==n||"\\"===o);){if(S(a.peek())&&"\\"!==c){s=R.INVALID;break}o=c,c=a.read()}return null===c&&(s=R.INVALID),this.createToken(s,i,t,r)},unicodeRangeToken:function(e,t,r){var n,i=this._reader,a=e,o=R.CHAR;return"+"===i.peek()&&(i.mark(),a+=i.read(),2===(a+=this.readUnicodeRangePart(!0)).length?i.reset():(o=R.UNICODE_RANGE,-1===a.indexOf("?")&&"-"===i.peek()&&(i.mark(),n=i.read(),1===(n+=this.readUnicodeRangePart(!1)).length?i.reset():a+=n))),this.createToken(o,a,t,r)},whitespaceToken:function(e,t,r){var n=e+this.readWhitespace();return this.createToken(R.S,n,t,r)},readUnicodeRangePart:function(e){for(var t=this._reader,r="",n=t.peek();E(n)&&r.length<6;)t.read(),r+=n,n=t.peek();if(e)for(;"?"===n&&r.length<6;)t.read(),r+=n,n=t.peek();return r},readWhitespace:function(){for(var e=this._reader,t="",r=e.peek();T(r);)e.read(),t+=r,r=e.peek();return t},readNumber:function(e){for(var t=this._reader,r=e,n="."===e,i=t.peek();i;){if(k(i))r+=t.read();else{if("."!==i)break;if(n)break;n=!0,r+=t.read()}i=t.peek()}return r},readString:function(){for(var e=this._reader,t=e.read(),r=t,n=t,i=e.peek();i&&(r+=i=e.read(),i!==t||"\\"===n);){if(S(e.peek())&&"\\"!==i){r="";break}n=i,i=e.peek()}return null===i&&(r=""),r},readURI:function(e){var t=this._reader,r=e,n="",i=t.peek();for(t.mark();i&&T(i);)t.read(),i=t.peek();for(n="'"===i||'"'===i?this.readString():this.readURL(),i=t.peek();i&&T(i);)t.read(),i=t.peek();return""===n||")"!==i?(r=e,t.reset()):r+=n+t.read(),r},readURL:function(){for(var e=this._reader,t="",r=e.peek();/^[!#$%&\\*-~]$/.test(r);)t+=e.read(),r=e.peek();return t},readName:function(e){for(var t=this._reader,r=e||"",n=t.peek();;)if("\\"===n)r+=this.readEscape(t.read()),n=t.peek();else{if(!n||!N(n))break;r+=t.read(),n=t.peek()}return r},readEscape:function(e){var t=this._reader,r=e||"",n=0,i=t.peek();if(E(i))do{r+=t.read(),i=t.peek()}while(i&&E(i)&&++n<6);return 3===r.length&&/\s/.test(i)||7===r.length||1===r.length?t.read():i="",r+i},readComment:function(e){var t=this._reader,r=e||"",n=t.read();if("*"===n){for(;n;){if((r+=n).length>2&&"*"===n&&"/"===t.peek()){r+=t.read();break}n=t.read()}return r}return""}});var R=[{name:"CDO"},{name:"CDC"},{name:"S",whitespace:!0},{name:"COMMENT",comment:!0,hide:!0,channel:"comment"},{name:"INCLUDES",text:"~="},{name:"DASHMATCH",text:"|="},{name:"PREFIXMATCH",text:"^="},{name:"SUFFIXMATCH",text:"$="},{name:"SUBSTRINGMATCH",text:"*="},{name:"STRING"},{name:"IDENT"},{name:"HASH"},{name:"IMPORT_SYM",text:"@import"},{name:"PAGE_SYM",text:"@page"},{name:"MEDIA_SYM",text:"@media"},{name:"FONT_FACE_SYM",text:"@font-face"},{name:"CHARSET_SYM",text:"@charset"},{name:"NAMESPACE_SYM",text:"@namespace"},{name:"VIEWPORT_SYM",text:["@viewport","@-ms-viewport","@-o-viewport"]},{name:"DOCUMENT_SYM",text:["@document","@-moz-document"]},{name:"UNKNOWN_SYM"},{name:"KEYFRAMES_SYM",text:["@keyframes","@-webkit-keyframes","@-moz-keyframes","@-o-keyframes"]},{name:"IMPORTANT_SYM"},{name:"LENGTH"},{name:"ANGLE"},{name:"TIME"},{name:"FREQ"},{name:"DIMENSION"},{name:"PERCENTAGE"},{name:"NUMBER"},{name:"URI"},{name:"FUNCTION"},{name:"UNICODE_RANGE"},{name:"INVALID"},{name:"PLUS",text:"+"},{name:"GREATER",text:">"},{name:"COMMA",text:","},{name:"TILDE",text:"~"},{name:"NOT"},{name:"TOPLEFTCORNER_SYM",text:"@top-left-corner"},{name:"TOPLEFT_SYM",text:"@top-left"},{name:"TOPCENTER_SYM",text:"@top-center"},{name:"TOPRIGHT_SYM",text:"@top-right"},{name:"TOPRIGHTCORNER_SYM",text:"@top-right-corner"},{name:"BOTTOMLEFTCORNER_SYM",text:"@bottom-left-corner"},{name:"BOTTOMLEFT_SYM",text:"@bottom-left"},{name:"BOTTOMCENTER_SYM",text:"@bottom-center"},{name:"BOTTOMRIGHT_SYM",text:"@bottom-right"},{name:"BOTTOMRIGHTCORNER_SYM",text:"@bottom-right-corner"},{name:"LEFTTOP_SYM",text:"@left-top"},{name:"LEFTMIDDLE_SYM",text:"@left-middle"},{name:"LEFTBOTTOM_SYM",text:"@left-bottom"},{name:"RIGHTTOP_SYM",text:"@right-top"},{name:"RIGHTMIDDLE_SYM",text:"@right-middle"},{name:"RIGHTBOTTOM_SYM",text:"@right-bottom"},{name:"RESOLUTION",state:"media"},{name:"IE_FUNCTION"},{name:"CHAR"},{name:"PIPE",text:"|"},{name:"SLASH",text:"/"},{name:"MINUS",text:"-"},{name:"STAR",text:"*"},{name:"LBRACE",endChar:"}",text:"{"},{name:"RBRACE",text:"}"},{name:"LBRACKET",endChar:"]",text:"["},{name:"RBRACKET",text:"]"},{name:"EQUALS",text:"="},{name:"COLON",text:":"},{name:"SEMICOLON",text:";"},{name:"LPAREN",endChar:")",text:"("},{name:"RPAREN",text:")"},{name:"DOT",text:"."}];!function(){var e=[],t=Object.create(null);R.UNKNOWN=-1,R.unshift({name:"EOF"});for(var r=0,n=R.length;r<n;r++)if(e.push(R[r].name),R[R[r].name]=r,R[r].text)if(R[r].text instanceof Array)for(var i=0;i<R[r].text.length;i++)t[R[r].text[i]]=r;else t[R[r].text]=r;R.name=function(t){return e[t]},R.type=function(e){return t[e]||-1}}();var L={validate:function(e,t){var r=e.toString().toLowerCase(),n=new p(t),i=u[r];if(i)"number"!=typeof i&&("string"==typeof i?i.indexOf("||")>-1?this.groupProperty(i,n):this.singleProperty(i,n,1):i.multi?this.multiProperty(i.multi,n,i.comma,i.max||1/0):"function"==typeof i&&i(n));else if(0!==r.indexOf("-"))throw new O("Unknown property '"+e+"'.",e.line,e.col)},singleProperty:function(e,t,r,n){for(var i,a=!1,o=t.value,s=0;t.hasNext()&&s<r&&(a=I.isAny(t,e));)s++;if(!a)throw t.hasNext()&&!t.isFirst()?new O("Expected end of value but found '"+(i=t.peek())+"'.",i.line,i.col):new O("Expected ("+e+") but found '"+o+"'.",o.line,o.col);if(t.hasNext())throw new O("Expected end of value but found '"+(i=t.next())+"'.",i.line,i.col)},multiProperty:function(e,t,r,n){for(var i,a=!1,o=t.value,s=0;t.hasNext()&&!a&&s<n&&I.isAny(t,e);)if(s++,t.hasNext()){if(r){if(","!==String(t.peek()))break;i=t.next()}}else a=!0;if(!a)throw t.hasNext()&&!t.isFirst()?new O("Expected end of value but found '"+(i=t.peek())+"'.",i.line,i.col):(i=t.previous(),r&&","===String(i)?new O("Expected end of value but found '"+i+"'.",i.line,i.col):new O("Expected ("+e+") but found '"+o+"'.",o.line,o.col));if(t.hasNext())throw new O("Expected end of value but found '"+(i=t.next())+"'.",i.line,i.col)},groupProperty:function(e,t,r){for(var n,i,a=!1,o=t.value,s=e.split("||").length,c={count:0},l=!1;t.hasNext()&&!a&&(n=I.isAnyOfGroup(t,e))&&!c[n];)c[n]=1,c.count++,l=!0,c.count!==s&&t.hasNext()||(a=!0);if(!a)throw l&&t.hasNext()?new O("Expected end of value but found '"+(i=t.peek())+"'.",i.line,i.col):new O("Expected ("+e+") but found '"+o+"'.",o.line,o.col);if(t.hasNext())throw new O("Expected end of value but found '"+(i=t.next())+"'.",i.line,i.col)}};function O(e,t,r){this.col=r,this.line=t,this.message=e}O.prototype=new Error;var I={isLiteral:function(e,t){var r,n,i=e.text.toString().toLowerCase(),a=t.split(" | "),o=!1;for(r=0,n=a.length;r<n&&!o;r++)i===a[r].toLowerCase()&&(o=!0);return o},isSimple:function(e){return!!this.simple[e]},isComplex:function(e){return!!this.complex[e]},isAny:function(e,t){var r,n,i=t.split(" | "),a=!1;for(r=0,n=i.length;r<n&&!a&&e.hasNext();r++)a=this.isType(e,i[r]);return a},isAnyOfGroup:function(e,t){var r,n,i=t.split(" || "),a=!1;for(r=0,n=i.length;r<n&&!a;r++)a=this.isType(e,i[r]);return!!a&&i[r-1]},isType:function(e,t){var r=e.peek(),n=!1;return"<"!==t.charAt(0)?(n=this.isLiteral(r,t))&&e.next():this.simple[t]?(n=this.simple[t](r))&&e.next():n=this.complex[t](e),n},simple:{__proto__:null,"<absolute-size>":function(e){return I.isLiteral(e,"xx-small | x-small | small | medium | large | x-large | xx-large")},"<attachment>":function(e){return I.isLiteral(e,"scroll | fixed | local")},"<attr>":function(e){return"function"===e.type&&"attr"===e.name},"<bg-image>":function(e){return this["<image>"](e)||this["<gradient>"](e)||"none"===String(e)},"<gradient>":function(e){return"function"===e.type&&/^(?:\-(?:ms|moz|o|webkit)\-)?(?:repeating\-)?(?:radial\-|linear\-)?gradient/i.test(e)},"<box>":function(e){return I.isLiteral(e,"padding-box | border-box | content-box")},"<content>":function(e){return"function"===e.type&&"content"===e.name},"<relative-size>":function(e){return I.isLiteral(e,"smaller | larger")},"<ident>":function(e){return"identifier"===e.type},"<length>":function(e){return!("function"!==e.type||!/^(?:\-(?:ms|moz|o|webkit)\-)?calc/i.test(e))||("length"===e.type||"number"===e.type||"integer"===e.type||"0"===String(e))},"<color>":function(e){return"color"===e.type||"transparent"===String(e)||"currentColor"===String(e)},"<number>":function(e){return"number"===e.type||this["<integer>"](e)},"<integer>":function(e){return"integer"===e.type},"<line>":function(e){return"integer"===e.type},"<angle>":function(e){return"angle"===e.type},"<uri>":function(e){return"uri"===e.type},"<image>":function(e){return this["<uri>"](e)},"<percentage>":function(e){return"percentage"===e.type||"0"===String(e)},"<border-width>":function(e){return this["<length>"](e)||I.isLiteral(e,"thin | medium | thick")},"<border-style>":function(e){return I.isLiteral(e,"none | hidden | dotted | dashed | solid | double | groove | ridge | inset | outset")},"<content-sizing>":function(e){return I.isLiteral(e,"fill-available | -moz-available | -webkit-fill-available | max-content | -moz-max-content | -webkit-max-content | min-content | -moz-min-content | -webkit-min-content | fit-content | -moz-fit-content | -webkit-fit-content")},"<margin-width>":function(e){return this["<length>"](e)||this["<percentage>"](e)||I.isLiteral(e,"auto")},"<padding-width>":function(e){return this["<length>"](e)||this["<percentage>"](e)},"<shape>":function(e){return"function"===e.type&&("rect"===e.name||"inset-rect"===e.name)},"<time>":function(e){return"time"===e.type},"<flex-grow>":function(e){return this["<number>"](e)},"<flex-shrink>":function(e){return this["<number>"](e)},"<width>":function(e){return this["<margin-width>"](e)},"<flex-basis>":function(e){return this["<width>"](e)},"<flex-direction>":function(e){return I.isLiteral(e,"row | row-reverse | column | column-reverse")},"<flex-wrap>":function(e){return I.isLiteral(e,"nowrap | wrap | wrap-reverse")},"<feature-tag-value>":function(e){return"function"===e.type&&/^[A-Z0-9]{4}$/i.test(e)}},complex:{__proto__:null,"<bg-position>":function(e){for(var t=!1,r="<percentage> | <length>",n="left | right",i="top | bottom",a=0;e.peek(a)&&","!==e.peek(a).text;)a++;return a<3?I.isAny(e,n+" | center | "+r)?(t=!0,I.isAny(e,i+" | center | "+r)):I.isAny(e,i)&&(t=!0,I.isAny(e,n+" | center")):I.isAny(e,n)?I.isAny(e,i)?(t=!0,I.isAny(e,r)):I.isAny(e,r)&&(I.isAny(e,i)?(t=!0,I.isAny(e,r)):I.isAny(e,"center")&&(t=!0)):I.isAny(e,i)?I.isAny(e,n)?(t=!0,I.isAny(e,r)):I.isAny(e,r)&&(I.isAny(e,n)?(t=!0,I.isAny(e,r)):I.isAny(e,"center")&&(t=!0)):I.isAny(e,"center")&&I.isAny(e,n+" | "+i)&&(t=!0,I.isAny(e,r)),t},"<bg-size>":function(e){var t=!1,r="<percentage> | <length> | auto";return I.isAny(e,"cover | contain")?t=!0:I.isAny(e,r)&&(t=!0,I.isAny(e,r)),t},"<repeat-style>":function(e){var t,r=!1,n="repeat | space | round | no-repeat";return e.hasNext()&&(t=e.next(),I.isLiteral(t,"repeat-x | repeat-y")?r=!0:I.isLiteral(t,n)&&(r=!0,e.hasNext()&&I.isLiteral(e.peek(),n)&&e.next())),r},"<shadow>":function(e){var t=!1,r=0,n=!1,i=!1;if(e.hasNext()){for(I.isAny(e,"inset")&&(n=!0),I.isAny(e,"<color>")&&(i=!0);I.isAny(e,"<length>")&&r<4;)r++;e.hasNext()&&(i||I.isAny(e,"<color>"),n||I.isAny(e,"inset")),t=r>=2&&r<=4}return t},"<x-one-radius>":function(e){var t=!1,r="<length> | <percentage> | inherit";return I.isAny(e,r)&&(t=!0,I.isAny(e,r)),t},"<flex>":function(e){var t,r=!1;if(I.isAny(e,"none | inherit")?r=!0:I.isType(e,"<flex-grow>")?e.peek()?I.isType(e,"<flex-shrink>")?r=!e.peek()||I.isType(e,"<flex-basis>"):I.isType(e,"<flex-basis>")&&(r=null===e.peek()):r=!0:I.isType(e,"<flex-basis>")&&(r=!0),!r)throw t=e.peek(),new O("Expected (none | [ <flex-grow> <flex-shrink>? || <flex-basis> ]) but found '"+e.value.text+"'.",t.line,t.col);return r}}};n.css={__proto__:null,Colors:a,Combinator:o,Parser:l,PropertyName:h,PropertyValue:d,PropertyValuePart:f,MediaFeature:s,MediaQuery:c,Selector:g,SelectorPart:b,SelectorSubPart:v,Specificity:_,TokenStream:A,Tokens:R,ValidationError:O}}(),function(){for(var e in n)t[e]=n[e]}()},function(e,t,r){"use strict";var n=r(16),i=r(38);function a(e,t){this._window=e,this._href=t}e.exports=a,a.prototype=Object.create(i.prototype,{constructor:{value:a},href:{get:function(){return this._href},set:function(e){this.assign(e)}},assign:{value:function(e){var t=new n(this._href).resolve(e);this._href=t}},replace:{value:function(e){this.assign(e)}},reload:{value:function(){this.assign(this.href)}},toString:{value:function(){return this.href}}})},function(e,t,r){"use strict";var n=Object.create(null,{appCodeName:{value:"Mozilla"},appName:{value:"Netscape"},appVersion:{value:"4.0"},platform:{value:""},product:{value:"Gecko"},productSub:{value:"20100101"},userAgent:{value:""},vendor:{value:""},vendorSub:{value:""},taintEnabled:{value:function(){return!1}}});e.exports=n},function(e,t,r){"use strict";var n={setTimeout:setTimeout,clearTimeout:clearTimeout,setInterval:setInterval,clearInterval:clearInterval};e.exports=n},function(e,t,r){"use strict";e.exports={VALUE:1,ATTR:2,REMOVE_ATTR:3,REMOVE:4,MOVE:5,INSERT:6}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=/highlight-(?:text|source)-([a-z0-9]+)/;function i(e){e.addRule("highlightedCodeBlock",{filter:function(e){var t=e.firstChild;return"DIV"===e.nodeName&&n.test(e.className)&&t&&"PRE"===t.nodeName},replacement:function(e,t,r){var i=((t.className||"").match(n)||[null,""])[1];return"\n\n"+r.fence+i+"\n"+t.firstChild.textContent+"\n"+r.fence+"\n\n"}})}function a(e){e.addRule("strikethrough",{filter:["del","s","strike"],replacement:function(e){return"~"+e+"~"}})}var o=Array.prototype.indexOf,s=Array.prototype.every,c={};function l(e){var t,r,n=e.parentNode;return"THEAD"===n.nodeName||n.firstChild===e&&("TABLE"===n.nodeName||(r=(t=n).previousSibling,"TBODY"===t.nodeName&&(!r||"THEAD"===r.nodeName&&/^\s*$/i.test(r.textContent))))&&s.call(e.childNodes,(function(e){return"TH"===e.nodeName}))}function u(e,t){var r=" ";return 0===o.call(t.parentNode.childNodes,t)&&(r="| "),r+e+" |"}function h(e){for(var t in e.keep((function(e){return"TABLE"===e.nodeName&&!l(e.rows[0])})),c)e.addRule(t,c[t])}function d(e){e.addRule("taskListItems",{filter:function(e){return"checkbox"===e.type&&"LI"===e.parentNode.nodeName},replacement:function(e,t){return(t.checked?"[x]":"[ ]")+" "}})}c.tableCell={filter:["th","td"],replacement:function(e,t){return u(e,t)}},c.tableRow={filter:"tr",replacement:function(e,t){var r="",n={left:":--",right:"--:",center:":-:"};if(l(t))for(var i=0;i<t.childNodes.length;i++){var a="---",o=(t.childNodes[i].getAttribute("align")||"").toLowerCase();o&&(a=n[o]||a),r+=u(a,t.childNodes[i])}return"\n"+e+(r?"\n"+r:"")}},c.table={filter:function(e){return"TABLE"===e.nodeName&&l(e.rows[0])},replacement:function(e){return"\n\n"+(e=e.replace("\n\n","\n"))+"\n\n"}},c.tableSection={filter:["thead","tbody","tfoot"],replacement:function(e){return e}},t.gfm=function(e){e.use([i,a,h,d])},t.highlightedCodeBlock=i,t.strikethrough=a,t.tables=h,t.taskListItems=d}]);