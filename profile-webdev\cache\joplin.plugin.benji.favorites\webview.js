let editStarted=!1,sourceIdx="";function cancelDefault(e){return e.preventDefault(),e.stopPropagation(),!1}function getDataId(e){if(e&&"favorite"===e.id)return e.dataset.id}function getDataIdx(e){if(e&&"favorite"===e.id)return e.dataset.idx}function openFav(e){const t=getDataIdx(e);t&&webviewApi.postMessage({name:"favsOpen",index:t})}function deleteFav(e){const t=getDataIdx(e);t&&webviewApi.postMessage({name:"favsDelete",index:t})}function openDialog(e){if(!editStarted){const t=getDataIdx(e.currentTarget);t&&webviewApi.postMessage({name:"favsEdit",index:t})}}function enableEdit(e,t){editStarted=t,e.readOnly=!t,e.focus(),e.select()}function editFav(e){const t=e.getElementsByTagName("input")[0];t&&enableEdit(t,!0)}function clickFav(e){cancelDefault(e),editStarted||(e.target.classList.contains("rename")?editFav(e.currentTarget):e.target.classList.contains("delete")?deleteFav(e.currentTarget):openFav(e.currentTarget))}function setBackground(e,t){e.currentTarget.style.background=t}function resetBackground(e){e.dataset.bg&&(e.style.background=e.dataset.bg)}function resetTabBackgrounds(){document.querySelectorAll("#favorite").forEach((e=>{resetBackground(e)})),container=document.querySelector("#favs-container"),container&&(container.style.background="none")}function dragStart(e){const t=getDataIdx(e.currentTarget);t&&(e.dataTransfer.setData("text/x-plugin-favorites-idx",t),sourceIdx=t);const a=getDataId(e.currentTarget);a&&e.dataTransfer.setData("text/x-plugin-favorites-id",a)}function dragEnd(e){resetTabBackgrounds(),cancelDefault(e),sourceIdx=""}function dragOver(e,t){resetTabBackgrounds(),cancelDefault(e),sourceIdx!==getDataIdx(e.currentTarget)&&setBackground(e,t)}function dragLeave(e){cancelDefault(e)}function drop(e){resetTabBackgrounds(),cancelDefault(e);const t=getDataIdx(e.currentTarget),a=e.dataTransfer.getData("text/x-plugin-favorites-idx");if(a&&t!==sourceIdx)return void webviewApi.postMessage({name:"favsDrag",index:a,targetIdx:t});const n=e.dataTransfer.getData("text/x-jop-folder-ids");if(n){const e=JSON.parse(n);if(1==e.length)return void webviewApi.postMessage({name:"favsAddFolder",id:e[0],targetIdx:t})}const r=e.dataTransfer.getData("text/x-jop-note-ids");if(r){const e=new Array;for(const t of JSON.parse(r))e.push(t);return void webviewApi.postMessage({name:"favsAddNote",id:e,targetIdx:t})}const s=e.dataTransfer.getData("text/x-plugin-note-tabs-id");if(s){const e=new Array(s);webviewApi.postMessage({name:"favsAddNote",id:e,targetIdx:t})}}document.addEventListener("change",(e=>{cancelDefault(e);const t=e.target;if(editStarted&&"title"===t.className){enableEdit(t,!1);const e=t.parentElement.parentElement.dataset.idx;e&&""!==t.value?webviewApi.postMessage({name:"favsRename",index:e,newTitle:t.value}):t.value=t.title}})),document.addEventListener("focusout",(e=>{cancelDefault(e);const t=e.target;editStarted&&"title"===t.className&&(enableEdit(t,!1),t.value=t.title)})),document.addEventListener("wheel",(e=>{const t=document.getElementById("favs-container");t&&(t.scrollLeft-=-e.deltaY)}));