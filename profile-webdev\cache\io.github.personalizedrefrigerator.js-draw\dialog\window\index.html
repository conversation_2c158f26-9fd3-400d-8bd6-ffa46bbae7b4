<!doctype html>
<html class="new-window-editor">
	<head>
		<meta charset="utf-8" />
		<meta name="Content-Security-Policy"
			  content="default-src self; object-src 'none'; style-src self 'unsafe-inline';"/>
		<script>
			// Silences a warning:
			var exports = {};
		</script>
		<script src="./window.js"></script>
		<style>
			:root,
			body {
				font-family: sans-serif;
				padding: 0;
				margin: 0;
			}

			:root::-webkit-scrollbar {
				width: 5px;
				height: 5px;
			}

			:root::-webkit-scrollbar-thumb {
				background-color: #666;
				border-radius: 5px;
			}

			.button-container {
				display: flex;
				flex-direction: row;
				justify-content: end;
			}

			.imageEditorContainer {
				width: 100vw;
				height: 100vh;

				/* Prevent scroll */
				position: fixed;
				top: 0;
				left: 0;
			}
		</style>
	</head>
	<body>
		<div id="log-container"></div>
	</body>
</html>
