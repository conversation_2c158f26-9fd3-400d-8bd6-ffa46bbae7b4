(()=>{"use strict";var e={775:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.default=joplin},200:(e,t)=>{var i,o,n,a,s,r,d,l,u,c;Object.defineProperty(t,"__esModule",{value:!0}),t.ContentScriptType=t.SettingStorage=t.AppType=t.SettingItemSubType=t.SettingItemType=t.ToolbarButtonLocation=t.isContextMenuItemLocation=t.MenuItemLocation=t.ModelType=t.ImportModuleOutputFormat=t.FileSystemItem=void 0,(c=t.FileSystemItem||(t.FileSystemItem={})).File="file",c.Directory="directory",(u=t.ImportModuleOutputFormat||(t.ImportModuleOutputFormat={})).Markdown="md",u.Html="html",(l=t.ModelType||(t.ModelType={}))[l.Note=1]="Note",l[l.Folder=2]="Folder",l[l.Setting=3]="Setting",l[l.Resource=4]="Resource",l[l.Tag=5]="Tag",l[l.NoteTag=6]="NoteTag",l[l.Search=7]="Search",l[l.Alarm=8]="Alarm",l[l.MasterKey=9]="MasterKey",l[l.ItemChange=10]="ItemChange",l[l.NoteResource=11]="NoteResource",l[l.ResourceLocalState=12]="ResourceLocalState",l[l.Revision=13]="Revision",l[l.Migration=14]="Migration",l[l.SmartFilter=15]="SmartFilter",l[l.Command=16]="Command",function(e){e.File="file",e.Edit="edit",e.View="view",e.Note="note",e.Tools="tools",e.Help="help",e.Context="context",e.NoteListContextMenu="noteListContextMenu",e.EditorContextMenu="editorContextMenu",e.FolderContextMenu="folderContextMenu",e.TagContextMenu="tagContextMenu"}(i=t.MenuItemLocation||(t.MenuItemLocation={})),t.isContextMenuItemLocation=function(e){return[i.Context,i.NoteListContextMenu,i.EditorContextMenu,i.FolderContextMenu,i.TagContextMenu].includes(e)},(d=t.ToolbarButtonLocation||(t.ToolbarButtonLocation={})).NoteToolbar="noteToolbar",d.EditorToolbar="editorToolbar",(r=t.SettingItemType||(t.SettingItemType={}))[r.Int=1]="Int",r[r.String=2]="String",r[r.Bool=3]="Bool",r[r.Array=4]="Array",r[r.Object=5]="Object",r[r.Button=6]="Button",(s=t.SettingItemSubType||(t.SettingItemSubType={})).FilePathAndArgs="file_path_and_args",s.FilePath="file_path",s.DirectoryPath="directory_path",(a=t.AppType||(t.AppType={})).Desktop="desktop",a.Mobile="mobile",a.Cli="cli",(n=t.SettingStorage||(t.SettingStorage={}))[n.Database=1]="Database",n[n.File=2]="File",(o=t.ContentScriptType||(t.ContentScriptType={})).MarkdownItPlugin="markdownItPlugin",o.CodeMirrorPlugin="codeMirrorPlugin"},684:function(e,t,i){var o=this&&this.__awaiter||function(e,t,i,o){return new(i||(i=Promise))((function(n,a){function s(e){try{d(o.next(e))}catch(e){a(e)}}function r(e){try{d(o.throw(e))}catch(e){a(e)}}function d(e){var t;e.done?n(e.value):(t=e.value,t instanceof i?t:new i((function(e){e(t)}))).then(s,r)}d((o=o.apply(e,t||[])).next())}))};Object.defineProperty(t,"__esModule",{value:!0}),t.Dialog=void 0;const n=i(775),a=i(444);t.Dialog=class{constructor(e){this._title=e}static showMessage(e){return o(this,void 0,void 0,(function*(){return yield n.default.views.dialogs.showMessageBox(e)}))}getFullPath(e){return o(this,void 0,void 0,(function*(){switch(e.type){case a.FavoriteType.Folder:case a.FavoriteType.Note:case a.FavoriteType.Todo:const t=yield n.default.data.get([a.Favorites.getDesc(e).dataType,e.value],{fields:["title","parent_id"]});if(t){let e=new Array,i=t.parent_id;for(;i;){const t=yield n.default.data.get(["folders",i],{fields:["title","parent_id"]});if(!t)break;i=t.parent_id,e.push(t.title)}return e.reverse().push(t.title),e.join("/")}case a.FavoriteType.Tag:const i=yield n.default.data.get([a.Favorites.getDesc(e).dataType,e.value],{fields:["title"]});if(i)return i.title;case a.FavoriteType.Search:return e.value}return""}))}prepareDialogHtml(e){return o(this,void 0,void 0,(function*(){const t=yield this.getFullPath(e),i=a.Favorites.isSearch(e)?"":"disabled";return`\n      <div>\n        <h3><i class="fas ${a.Favorites.getDesc(e).icon}"></i>${this._title} ${a.Favorites.getDesc(e).name} Favorite</h3>\n        <form name="inputForm">\n          <label for="title"><strong>Name</strong></label>\n          <input type="text" id="title" name="title" value="${e.title}" tabindex="0" autofocus required>\n          <label for="value"><strong>${a.Favorites.getDesc(e).label}</strong></label>\n          <textarea id="value" name="value" rows="3" ${i} tabindex="0" required>${t}</textarea>\n        </form>\n      </div>\n    `}))}register(e){return o(this,void 0,void 0,(function*(){this._dialog=yield n.default.views.dialogs.create("dialog"+this._title),yield n.default.views.dialogs.addScript(this._dialog,"./webview_dialog.css"),e&&(yield n.default.views.dialogs.setButtons(this._dialog,e))}))}open(e){return o(this,void 0,void 0,(function*(){const t=yield this.prepareDialogHtml(e);return yield n.default.views.dialogs.setHtml(this._dialog,t),yield n.default.views.dialogs.open(this._dialog)}))}}},444:function(e,t){var i,o=this&&this.__awaiter||function(e,t,i,o){return new(i||(i=Promise))((function(n,a){function s(e){try{d(o.next(e))}catch(e){a(e)}}function r(e){try{d(o.throw(e))}catch(e){a(e)}}function d(e){var t;e.done?n(e.value):(t=e.value,t instanceof i?t:new i((function(e){e(t)}))).then(s,r)}d((o=o.apply(e,t||[])).next())}))};Object.defineProperty(t,"__esModule",{value:!0}),t.Favorites=t.FavoriteType=void 0,function(e){e[e.Folder=0]="Folder",e[e.Note=1]="Note",e[e.Todo=2]="Todo",e[e.Tag=3]="Tag",e[e.Search=4]="Search"}(i=t.FavoriteType||(t.FavoriteType={}));const n=[{name:"Notebook",icon:"fa-book",dataType:"folders",label:"Full path"},{name:"Note",icon:"fa-file-alt",dataType:"notes",label:"Full path"},{name:"To-do",icon:"fa-check-square",dataType:"notes",label:"Full path"},{name:"Tag",icon:"fa-tag",dataType:"tags",label:"Tag"},{name:"Search",icon:"fa-search",dataType:"searches",label:"Search query"}];class a{constructor(e){this._settings=e}get favorites(){return this._settings.favorites}get length(){return this.favorites.length}store(){return o(this,void 0,void 0,(function*(){yield this._settings.storeFavorites()}))}indexOutOfBounds(e){return e<0||e>=this.length}encodeHtml(e,t){let i=e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#039;");return t?i.trim():i}decodeHtml(e,t){let i=e.replace(/&amp;/g,"&").replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&quot;/g,'"').replace(/&#039;/g,"'");return t?i.trim():i}static isNote(e){return!!e&&e.type===i.Note}static isTodo(e){return!!e&&e.type===i.Todo}static isSearch(e){return!!e&&e.type===i.Search}static getDesc(e){if(void 0!==e)return n[e.type]}get(e){if(!this.indexOutOfBounds(e))return this.favorites[e]}getDecodedValue(e){if(void 0!==e)return this.decodeHtml(e.value,!a.isSearch(e))}indexOf(e){if(e)for(let t=0;t<this.length;t++)if(this.favorites[t].value===e)return t;return-1}hasFavorite(e){return!(this.indexOf(e)<0)}create(e,t,o){return{value:this.encodeHtml(e,o!=i.Search),title:this.encodeHtml(t,!0),type:o}}add(e,t,i,n){return o(this,void 0,void 0,(function*(){if(void 0===e||void 0===t||void 0===i)return;const o=this.create(e,t,i);n&&n>0?this.favorites.splice(n,0,o):this.favorites.push(o),yield this.store()}))}changeValue(e,t){return o(this,void 0,void 0,(function*(){e<0||void 0===t||""===t||(this.favorites[e].value=this.encodeHtml(t,this.favorites[e].type!=i.Search),yield this.store())}))}changeTitle(e,t){return o(this,void 0,void 0,(function*(){e<0||void 0===t||""===t||(this.favorites[e].title=this.encodeHtml(t,!0),yield this.store())}))}changeType(e,t){return o(this,void 0,void 0,(function*(){e<0||void 0===t||(this.favorites[e].type=t,yield this.store())}))}moveWithIndex(e,t){return o(this,void 0,void 0,(function*(){if(this.indexOutOfBounds(e))return;if(t&&this.indexOutOfBounds(t))return;let i=this.length-1;t&&(i=t);const o=this.favorites[e];this.favorites.splice(e,1),this.favorites.splice(i,0,o),yield this.store()}))}delete(e){return o(this,void 0,void 0,(function*(){e>=0&&(this.favorites.splice(e,1),yield this.store())}))}}t.Favorites=a},607:function(e,t,i){var o=this&&this.__awaiter||function(e,t,i,o){return new(i||(i=Promise))((function(n,a){function s(e){try{d(o.next(e))}catch(e){a(e)}}function r(e){try{d(o.throw(e))}catch(e){a(e)}}function d(e){var t;e.done?n(e.value):(t=e.value,t instanceof i?t:new i((function(e){e(t)}))).then(s,r)}d((o=o.apply(e,t||[])).next())}))};Object.defineProperty(t,"__esModule",{value:!0});const n=i(775),a=i(200),s=i(444),r=i(310),d=i(673),l=i(684);n.default.plugins.register({onStart:function(){return o(this,void 0,void 0,(function*(){const e=n.default.clipboard,t=n.default.commands,i=n.default.data,u=n.default.settings,c=n.default.workspace,v=new r.Settings;yield v.register();const f=new s.Favorites(v),h=new d.Panel(f,v);yield h.register();const g=new l.Dialog("Add");yield g.register();const p=new l.Dialog("Edit");function y(e,t){return o(this,void 0,void 0,(function*(){try{yield i.get([s.Favorites.getDesc(e).dataType,e.value],{fields:["id"]})}catch(i){if(!(yield l.Dialog.showMessage(`Cannot open favorite. Seems that the target ${s.Favorites.getDesc(e).name.toLocaleLowerCase()} was deleted.\n\nDo you want to delete the favorite also?`)))return yield f.delete(t),yield h.updateWebview(),!0}return!1}))}function m(e,i,n,a,s){return o(this,void 0,void 0,(function*(){let o=e,r=i;const d=f.indexOf(e);if(d>=0)yield t.execute("favsEditFavorite",d);else{if(a){const e=yield g.open(f.create(o,r,n));if("ok"!=e.id||null==e.formData)return;r=e.formData.inputForm.title,e.formData.inputForm.value&&(o=e.formData.inputForm.value)}if(""===o||""===r)return;yield f.add(o,r,n,s),yield h.updateWebview()}}))}yield p.register([{id:"delete",title:"Delete"},{id:"ok",title:"OK"},{id:"cancel",title:"Cancel"}]),yield t.register({name:"favsOpenFavorite",execute:n=>o(this,void 0,void 0,(function*(){const a=f.get(n);if(a){switch(a.type){case s.FavoriteType.Folder:if(yield y(a,n))return;t.execute("openFolder",a.value);break;case s.FavoriteType.Note:case s.FavoriteType.Todo:if(yield y(a,n))return;yield function(e,t){return o(this,void 0,void 0,(function*(){let o;const n=yield i.get([s.Favorites.getDesc(e).dataType,e.value],{fields:["id","is_todo"]});s.Favorites.isNote(e)&&n.is_todo&&(o=s.FavoriteType.Todo),s.Favorites.isTodo(e)&&!n.is_todo&&(o=s.FavoriteType.Note),o&&(yield f.changeType(t,o),yield h.updateWebview())}))}(a,n),t.execute("openNote",a.value);break;case s.FavoriteType.Tag:if(yield y(a,n))return;t.execute("openTag",a.value);break;case s.FavoriteType.Search:yield e.writeText(f.getDecodedValue(a)),yield t.execute("focusSearch")}yield h.updateWebview()}}))}),yield t.register({name:"favsEditFavorite",execute:e=>o(this,void 0,void 0,(function*(){const t=f.get(e);if(!t)return;const i=yield p.open(t);if("ok"==i.id&&null!=i.formData)yield f.changeTitle(e,i.formData.inputForm.title),yield f.changeValue(e,i.formData.inputForm.value);else{if("delete"!=i.id)return;yield f.delete(e)}yield h.updateWebview()}))}),yield t.register({name:"favsAddFolder",label:"Add notebook to Favorites",iconName:"fas fa-book",enabledCondition:"oneFolderSelected",execute:(e,t)=>o(this,void 0,void 0,(function*(){if(e){const o=yield i.get(["folders",e],{fields:["id","title"]});if(!o)return;yield m(o.id,o.title,s.FavoriteType.Folder,v.editBeforeAdd,t)}else{const e=yield c.selectedFolder();if(!e)return;yield m(e.id,e.title,s.FavoriteType.Folder,v.editBeforeAdd,t)}}))}),yield t.register({name:"favsAddNote",label:"Add note to Favorites",iconName:"fas fa-sticky-note",enabledCondition:"someNotesSelected",execute:(e,t)=>o(this,void 0,void 0,(function*(){if(e)for(const o of e){if(e.length>1&&f.hasFavorite(o))continue;const n=yield i.get(["notes",o],{fields:["id","title","is_todo"]});if(!n)return;const a=v.editBeforeAdd&&1==e.length;yield m(n.id,n.title,n.is_todo?s.FavoriteType.Todo:s.FavoriteType.Note,a,t)}else{const e=yield c.selectedNote();if(!e)return;yield m(e.id,e.title,e.is_todo?s.FavoriteType.Todo:s.FavoriteType.Note,v.editBeforeAdd,t)}}))}),yield t.register({name:"favsAddTag",label:"Add tag to Favorites",iconName:"fas fa-tag",execute:e=>o(this,void 0,void 0,(function*(){if(e){const t=yield i.get(["tags",e],{fields:["id","title"]});if(!t)return;yield m(t.id,t.title,s.FavoriteType.Tag,v.editBeforeAdd)}}))}),yield t.register({name:"favsAddSearch",label:"Add new search to Favorites",iconName:"fas fa-search",execute:()=>o(this,void 0,void 0,(function*(){yield m("","New Search",s.FavoriteType.Search,!0)}))}),yield t.register({name:"favsClear",label:"Remove all Favorites",iconName:"fas fa-times",execute:()=>o(this,void 0,void 0,(function*(){(yield l.Dialog.showMessage("Do you really want to remove all Favorites?"))||(yield v.clearFavorites(),yield h.updateWebview())}))}),yield t.register({name:"favsToggleVisibility",label:"Toggle Favorites visibility",iconName:"fas fa-eye-slash",execute:()=>o(this,void 0,void 0,(function*(){yield h.toggleVisibility()}))}),yield n.default.views.menus.create("toolsFavorites","Favorites",[{commandName:"favsAddFolder",label:"Add active notebook"},{commandName:"favsAddNote",label:"Add selected note(s)"},{commandName:"favsAddSearch",label:"Add new search"},{commandName:"favsClear",label:"Remove all Favorites"},{commandName:"favsToggleVisibility",label:"Toggle panel visibility"}],a.MenuItemLocation.Tools),yield n.default.views.menuItems.create("foldersContextMenuAddFolder","favsAddFolder",a.MenuItemLocation.FolderContextMenu),yield n.default.views.menuItems.create("tagsContextMenuAddNote","favsAddTag",a.MenuItemLocation.TagContextMenu),yield n.default.views.menuItems.create("notesContextMenuAddNote","favsAddNote",a.MenuItemLocation.NoteListContextMenu),yield n.default.views.menuItems.create("editorContextMenuAddNote","favsAddNote",a.MenuItemLocation.EditorContextMenu),u.onChange((e=>o(this,void 0,void 0,(function*(){yield v.read(e),yield h.updateWebview()})))),yield h.updateWebview()}))}})},673:function(e,t,i){var o=this&&this.__awaiter||function(e,t,i,o){return new(i||(i=Promise))((function(n,a){function s(e){try{d(o.next(e))}catch(e){a(e)}}function r(e){try{d(o.throw(e))}catch(e){a(e)}}function d(e){var t;e.done?n(e.value):(t=e.value,t instanceof i?t:new i((function(e){e(t)}))).then(s,r)}d((o=o.apply(e,t||[])).next())}))};Object.defineProperty(t,"__esModule",{value:!0}),t.Panel=void 0;const n=i(775),a=i(444);t.Panel=class{constructor(e,t){this._favs=e,this._settings=t}register(){return o(this,void 0,void 0,(function*(){this._panel=yield n.default.views.panels.create("favorites.panel"),yield n.default.views.panels.addScript(this._panel,"./webview.css"),yield n.default.views.panels.addScript(this._panel,"./webview.js"),yield n.default.views.panels.onMessage(this._panel,(e=>o(this,void 0,void 0,(function*(){"favsAddFolder"===e.name&&(yield n.default.commands.execute("favsAddFolder",e.id,e.targetIdx)),"favsAddNote"===e.name&&(yield n.default.commands.execute("favsAddNote",e.id,e.targetIdx)),"favsEdit"===e.name&&(yield n.default.commands.execute("favsEditFavorite",e.index)),"favsOpen"===e.name&&(yield n.default.commands.execute("favsOpenFavorite",e.index)),"favsRename"===e.name&&(yield this._favs.changeTitle(e.index,e.newTitle),yield this.updateWebview()),"favsDrag"===e.name&&(yield this._favs.moveWithIndex(e.index,e.targetIdx),yield this.updateWebview()),"favsDelete"===e.name&&(yield this._favs.delete(e.index),yield this.updateWebview())})))),yield n.default.views.panels.setHtml(this._panel,`\n      <div id="container" style="background:${this._settings.background};font-family:${this._settings.fontFamily},sans-serif;font-size:${this._settings.fontSize};">\n        <div id="favs-container">\n          <p style="padding-left:8px;">Loading panel...</p>\n        </div>\n      </div>\n    `)}))}getPanelTitleHtml(){let e="";if(this._settings.showPanelTitle){const t=this._settings.foreground;e=`\n        <div id="panel-title" draggable="false" style="height:${this._settings.lineHeight}px;">\n          <span class="fas fa-star" style="color:${t};"></span>\n          <span class="title" style="color:${t};">FAVORITES</span>\n        </div>\n      `}return e}getFavoritesHtml(){const e=[];let t=0;for(const i of this._favs.favorites){const o=this._settings.foreground,n=this._settings.background,s=this._settings.hoverBackground,r=this._settings.dividerColor;let d="";this._settings.showTypeIcons&&(d=`<span class="fas ${a.Favorites.getDesc(i).icon}" title="${a.Favorites.getDesc(i).name}" style="color:${o};"></span>`);let l="";(a.Favorites.isNote(i)||a.Favorites.isTodo(i))&&(l=`data-id="${i.value}"`),e.push(`\n        <div id="favorite" data-idx="${t++}" ${l} data-bg="${n}" draggable="${this._settings.enableDragAndDrop}"\n          onclick="clickFav(event)" oncontextmenu="openDialog(event)" onmouseover="setBackground(event,'${s}')" onmouseout="resetBackground(this)"\n          ondragstart="dragStart(event)" ondragover="dragOver(event, '${s}')" ondragleave="dragLeave(event)" ondrop="drop(event)" ondragend="dragEnd(event)"\n          style="height:${this._settings.lineHeight}px;min-width:${this._settings.minFavWidth}px;max-width:${this._settings.maxFavWidth}px;background:${n};border-color:${r};">\n          <span class="favorite-inner" style="border-color:${r};">\n            ${d}\n            <input class="title" title="${i.title}" value="${i.title}" style="color:${o};" readonly></input>\n            <span class="controls" style="background:${s};">\n              <span class="rename fas fa-pen" title="Rename" style="color:${o};"></span>\n              <span class="delete fas fa-trash" title="Delete" style="color:${o};"></span>\n            </span>\n          </span>\n        </div>\n      `)}return e.join("\n")}updateWebview(){return o(this,void 0,void 0,(function*(){const e=this.getPanelTitleHtml(),t=this.getFavoritesHtml();yield n.default.views.panels.setHtml(this._panel,`\n      <div id="container" style="background:${this._settings.background};font-family:${this._settings.fontFamily},sans-serif;font-size:${this._settings.fontSize};">\n        ${e}\n        <div id="favs-container" draggable="${this._settings.enableDragAndDrop}"\n          ondragover="dragOver(event, '${this._settings.hoverBackground}');" ondragleave="dragLeave(event);" ondrop="drop(event);" ondragend="dragEnd(event);"\n          style="height:${this._settings.lineHeight}px;">\n          ${t}\n          <div style="height:${this._settings.lineHeight}px;min-width:10px;"></div>\n        </div>\n      </div>\n    `)}))}toggleVisibility(){return o(this,void 0,void 0,(function*(){const e=yield n.default.views.panels.visible(this._panel);yield n.default.views.panels.show(this._panel,!e)}))}}},310:function(e,t,i){var o=this&&this.__awaiter||function(e,t,i,o){return new(i||(i=Promise))((function(n,a){function s(e){try{d(o.next(e))}catch(e){a(e)}}function r(e){try{d(o.throw(e))}catch(e){a(e)}}function d(e){var t;e.done?n(e.value):(t=e.value,t instanceof i?t:new i((function(e){e(t)}))).then(s,r)}d((o=o.apply(e,t||[])).next())}))};Object.defineProperty(t,"__esModule",{value:!0}),t.Settings=void 0;const n=i(775),a=i(200);var s;!function(e){e.Default="default",e.FontFamily="var(--joplin-font-family)",e.FontSize="var(--joplin-font-size)",e.Background="var(--joplin-background-color3)",e.HoverBackground="var(--joplin-background-color-hover3)",e.Foreground="var(--joplin-color-faded)",e.DividerColor="var(--joplin-divider-color)"}(s||(s={})),t.Settings=class{constructor(){this._favs=new Array,this._enableDragAndDrop=!0,this._editBeforeAdd=!0,this._showPanelTitle=!0,this._showTypeIcons=!0,this._lineHeight=30,this._minFavoriteWidth=15,this._maxFavoriteWidth=100,this._fontFamily=s.Default,this._fontSize=s.Default,this._background=s.Default,this._hoverBackground=s.Default,this._foreground=s.Default,this._dividerColor=s.Default,this._defaultRegExp=new RegExp(s.Default,"i")}get favorites(){return this._favs}get enableDragAndDrop(){return this._enableDragAndDrop}get editBeforeAdd(){return this._editBeforeAdd}get showPanelTitle(){return this._showPanelTitle}get showTypeIcons(){return this._showTypeIcons}get lineHeight(){return this._lineHeight}get minFavWidth(){return this._minFavoriteWidth}get maxFavWidth(){return this._maxFavoriteWidth}get fontFamily(){return this._fontFamily}get fontSize(){return this._fontSize}get background(){return this._background}get hoverBackground(){return this._hoverBackground}get foreground(){return this._foreground}get dividerColor(){return this._dividerColor}register(){return o(this,void 0,void 0,(function*(){yield n.default.settings.registerSection("favorites.settings",{label:"Favorites",iconName:"fas fa-star"}),yield n.default.settings.registerSettings({favorites:{value:[],type:a.SettingItemType.Array,section:"favorites.settings",public:!1,label:"Favorites"},enableDragAndDrop:{value:this._enableDragAndDrop,type:a.SettingItemType.Bool,section:"favorites.settings",public:!0,label:"Enable drag & drop of favorites",description:"If enabled, the position of favorites can be change via drag & drop."},editBeforeAdd:{value:this._editBeforeAdd,type:a.SettingItemType.Bool,section:"favorites.settings",public:!0,label:"Edit favorite before add",description:"Opens a dialog to edit the favorite before adding it. If disabled, the name can still be changed later."},showPanelTitle:{value:this._showPanelTitle,type:a.SettingItemType.Bool,section:"favorites.settings",public:!0,label:"Show favorites panel title",description:"Display 'FAVORITES' title in front of the favorites."},showTypeIcons:{value:this._showTypeIcons,type:a.SettingItemType.Bool,section:"favorites.settings",public:!0,label:"Show type icons for favorites",description:"Display icons before favorite titles representing the types (notebook, note, tag, etc.)."},lineHeight:{value:this._lineHeight,type:a.SettingItemType.Int,section:"favorites.settings",public:!0,minimum:20,label:"Line height (px)",description:"Line height of the favorites panel."},minFavoriteWidth:{value:this._minFavoriteWidth,type:a.SettingItemType.Int,section:"favorites.settings",public:!0,label:"Minimum favorite width (px)",description:"Minimum width of one favorite in pixel."},maxFavoriteWidth:{value:this._maxFavoriteWidth,type:a.SettingItemType.Int,section:"favorites.settings",public:!0,label:"Maximum favorite width (px)",description:"Maximum width of one favorite in pixel."},fontFamily:{value:this._fontFamily,type:a.SettingItemType.String,section:"favorites.settings",public:!0,advanced:!0,label:"Font family",description:"Font family used in the panel. Font families other than 'default' must be installed on the system. If the font is incorrect or empty, it might default to a generic sans-serif font. (default: App default)"},fontSize:{value:this._fontSize,type:a.SettingItemType.String,section:"favorites.settings",public:!0,advanced:!0,label:"Font size",description:"Font size used in the panel. Values other than 'default' must be specified in valid CSS syntax, e.g. '13px'. (default: App default font size)"},mainBackground:{value:this._background,type:a.SettingItemType.String,section:"favorites.settings",public:!0,advanced:!0,label:"Background color",description:"Main background color of the panel. (default: Note list background color)"},hoverBackground:{value:this._hoverBackground,type:a.SettingItemType.String,section:"favorites.settings",public:!0,advanced:!0,label:"Hover Background color",description:"Background color used when hovering a favorite. (default: Note list hover color)"},mainForeground:{value:this._foreground,type:a.SettingItemType.String,section:"favorites.settings",public:!0,advanced:!0,label:"Foreground color",description:"Foreground color used for text and icons. (default: App faded color)"},dividerColor:{value:this._dividerColor,type:a.SettingItemType.String,section:"favorites.settings",public:!0,advanced:!0,label:"Divider color",description:"Color of the divider between the favorites. (default: App default border color)"}}),this._favs=yield n.default.settings.value("favorites"),yield this.read()}))}getOrDefault(e,t,i,a){return o(this,void 0,void 0,(function*(){if(!e||e.keys.includes(i)){const e=yield n.default.settings.value(i);return a&&e.match(this._defaultRegExp)?a:e}return t}))}read(e){return o(this,void 0,void 0,(function*(){this._enableDragAndDrop=yield this.getOrDefault(e,this._enableDragAndDrop,"enableDragAndDrop"),this._editBeforeAdd=yield this.getOrDefault(e,this._editBeforeAdd,"editBeforeAdd"),this._showPanelTitle=yield this.getOrDefault(e,this._showPanelTitle,"showPanelTitle"),this._showTypeIcons=yield this.getOrDefault(e,this._showTypeIcons,"showTypeIcons"),this._lineHeight=yield this.getOrDefault(e,this._lineHeight,"lineHeight"),this._minFavoriteWidth=yield this.getOrDefault(e,this._minFavoriteWidth,"minFavoriteWidth"),this._maxFavoriteWidth=yield this.getOrDefault(e,this._maxFavoriteWidth,"maxFavoriteWidth"),this._fontFamily=yield this.getOrDefault(e,this._fontFamily,"fontFamily",s.FontFamily),this._fontSize=yield this.getOrDefault(e,this._fontSize,"fontSize",s.FontSize),this._background=yield this.getOrDefault(e,this._background,"mainBackground",s.Background),this._hoverBackground=yield this.getOrDefault(e,this._hoverBackground,"hoverBackground",s.HoverBackground),this._foreground=yield this.getOrDefault(e,this._foreground,"mainForeground",s.Foreground),this._dividerColor=yield this.getOrDefault(e,this._dividerColor,"dividerColor",s.DividerColor)}))}storeFavorites(){return o(this,void 0,void 0,(function*(){yield n.default.settings.setValue("favorites",this._favs)}))}clearFavorites(){return o(this,void 0,void 0,(function*(){this._favs=[],yield this.storeFavorites()}))}}}},t={};!function i(o){var n=t[o];if(void 0!==n)return n.exports;var a=t[o]={exports:{}};return e[o].call(a.exports,a,a.exports,i),a.exports}(607)})();