(()=>{"use strict";var e={775:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.default=joplin},200:(e,t)=>{var o,n,i,r,a,l,u,d,c,s;Object.defineProperty(t,"__esModule",{value:!0}),t.ContentScriptType=t.SettingStorage=t.AppType=t.SettingItemSubType=t.SettingItemType=t.ToolbarButtonLocation=t.isContextMenuItemLocation=t.MenuItemLocation=t.ModelType=t.ImportModuleOutputFormat=t.FileSystemItem=void 0,(s=t.FileSystemItem||(t.FileSystemItem={})).File="file",s.Directory="directory",(c=t.ImportModuleOutputFormat||(t.ImportModuleOutputFormat={})).Markdown="md",c.Html="html",(d=t.ModelType||(t.ModelType={}))[d.Note=1]="Note",d[d.Folder=2]="Folder",d[d.Setting=3]="Setting",d[d.Resource=4]="Resource",d[d.Tag=5]="Tag",d[d.NoteTag=6]="NoteTag",d[d.Search=7]="Search",d[d.Alarm=8]="Alarm",d[d.MasterKey=9]="MasterKey",d[d.ItemChange=10]="ItemChange",d[d.NoteResource=11]="NoteResource",d[d.ResourceLocalState=12]="ResourceLocalState",d[d.Revision=13]="Revision",d[d.Migration=14]="Migration",d[d.SmartFilter=15]="SmartFilter",d[d.Command=16]="Command",function(e){e.File="file",e.Edit="edit",e.View="view",e.Note="note",e.Tools="tools",e.Help="help",e.Context="context",e.NoteListContextMenu="noteListContextMenu",e.EditorContextMenu="editorContextMenu",e.FolderContextMenu="folderContextMenu",e.TagContextMenu="tagContextMenu"}(o=t.MenuItemLocation||(t.MenuItemLocation={})),t.isContextMenuItemLocation=function(e){return[o.Context,o.NoteListContextMenu,o.EditorContextMenu,o.FolderContextMenu,o.TagContextMenu].includes(e)},(u=t.ToolbarButtonLocation||(t.ToolbarButtonLocation={})).NoteToolbar="noteToolbar",u.EditorToolbar="editorToolbar",(l=t.SettingItemType||(t.SettingItemType={}))[l.Int=1]="Int",l[l.String=2]="String",l[l.Bool=3]="Bool",l[l.Array=4]="Array",l[l.Object=5]="Object",l[l.Button=6]="Button",(a=t.SettingItemSubType||(t.SettingItemSubType={})).FilePathAndArgs="file_path_and_args",a.FilePath="file_path",a.DirectoryPath="directory_path",(r=t.AppType||(t.AppType={})).Desktop="desktop",r.Mobile="mobile",r.Cli="cli",(i=t.SettingStorage||(t.SettingStorage={}))[i.Database=1]="Database",i[i.File=2]="File",(n=t.ContentScriptType||(t.ContentScriptType={})).MarkdownItPlugin="markdownItPlugin",n.CodeMirrorPlugin="codeMirrorPlugin"},607:function(e,t,o){var n=this&&this.__awaiter||function(e,t,o,n){return new(o||(o=Promise))((function(i,r){function a(e){try{u(n.next(e))}catch(e){r(e)}}function l(e){try{u(n.throw(e))}catch(e){r(e)}}function u(e){var t;e.done?i(e.value):(t=e.value,t instanceof o?t:new o((function(e){e(t)}))).then(a,l)}u((n=n.apply(e,t||[])).next())}))};Object.defineProperty(t,"__esModule",{value:!0});const i=o(775),r=o(200);i.default.plugins.register({onStart:function(){return n(this,void 0,void 0,(function*(){const e=(yield i.default.plugins.installationDir())+"/buttons.css";yield i.default.window.loadChromeCssFile(e);for(let e=1;e<7;e++){const t=e.toString();yield i.default.commands.register({name:"applyHeading"+t,label:"Heading "+t,iconName:"btnh"+t,enabledCondition:"richTextEditorVisible",execute:()=>n(this,void 0,void 0,(function*(){yield(e=>n(this,void 0,void 0,(function*(){yield i.default.commands.execute("editor.execCommand",{name:"mceToggleFormat",args:[],ui:!1,value:"h"+e})})))(t)}))}),yield i.default.views.menuItems.create("menuH"+t,"applyHeading"+t,r.MenuItemLocation.Edit,{accelerator:"CmdOrCtrl+"+t}),e>=4&&(yield i.default.views.toolbarButtons.create("btnH"+t,"applyHeading"+t,r.ToolbarButtonLocation.EditorToolbar))}}))}})}},t={};!function o(n){var i=t[n];if(void 0!==i)return i.exports;var r=t[n]={exports:{}};return e[n].call(r.exports,r,r.exports,o),r.exports}(607)})();