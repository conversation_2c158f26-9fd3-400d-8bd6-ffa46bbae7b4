{"manifest_version": 1, "id": "joplin.plugin.benji.favorites", "app_min_version": "2.1.5", "version": "1.3.2", "name": "Favorites", "description": "Save any notebook, note, to-do, tag, or search as favorite in an extra panel view for quick access.", "author": "Benji300", "homepage_url": "https://github.com/benji300/joplin-favorites", "repository_url": "https://github.com/benji300/joplin-favorites", "keywords": ["favorite", "shortcut", "saved", "quick", "folder", "notebook", "note", "todo", "tag", "search", "panel", "view"], "_package_hash": "d464e94ba60f0a1128e065fcf0b7c693"}