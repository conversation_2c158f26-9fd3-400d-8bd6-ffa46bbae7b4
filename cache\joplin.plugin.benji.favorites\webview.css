/* GENERAL STYLES */
html,
body,
body > div,
div#joplin-plugin-content {
  height: 100%;
}
a {
  text-decoration: none;
}
span {
  overflow: hidden;
  padding: 3px;
  text-align: center;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.fas {
  overflow: initial;
  /* Regular does not support all required icons */
  /* font-weight: 400; */
}

/* HORIZONTAL LAYOUT */
#container {
  display: flex;
  height: 100%;
  width: 100%;
  align-items: center;
}


span.favorite-inner > span {
    display: none !important;
}

span.favorite-inner > input {
    padding-left: 5px !important;
}


#panel-title {
  align-items: center;
  display: flex;
  padding: 0 5px;
}

#panel-title .fa-star {
    color: #2D6BDC !important;
    position: relative;
    top: -1px;
}

#favs-container {
  display: flex;
  float: left;
  overflow-x: overlay;
  overflow-y: overlay;
  width: 100%;
}
#favs-container:empty {
  min-width: 100%;
}
#favorite {
  align-items: center;
  border-style: solid;
  border-width: 0;
  display: flex;
}
.favorite-inner {
  align-items: center;
  border-width: 0;
  border-right-width: 1px;
  border-style: solid;
  display: flex;
  padding: 0 3px;
  width: 100%;
}
#favorite:last-of-type > .favorite-inner {
  border-style: none;
}

input.title {
  background: none;
  border: none;
  display: flex;
  font-family: inherit;
  font-size: inherit;
  text-overflow: ellipsis;
  width: 100%;
}
input.title:hover {
  background: none;
  cursor: default;
}
input.title:not([readonly]):focus {
  border-color: var(--joplin-warning-background-color);
  border-radius: 3px;
  border-style: solid;
  border-width: 1px;
  margin-right: 8px;
  outline: none;
  padding: 3px 1px;
}
input.title[readonly]:focus {
  outline: none;
}

.controls {
  border-radius: 3px;
  display: none;
  opacity: 0;
  position: relative;
  right: 10px;
}
.controls:hover {
  opacity: 1;
}
.controls > .fas {
  cursor: pointer;
}

/* DRAG AND DROP */
[draggable="true"] {
  /* To prevent user selecting inside the drag source */
  user-select: none;
  -moz-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
}

::-webkit-scrollbar {
  height: 4px;
  width: 7px;
}
::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 5px;
}

/* VERCTICAL LAYOUT OVERWRITES */
@media screen and (max-width: 400px) {
  #container {
    display: block;
  }

  #panel-title {
    padding-left: 12px;
  }

  #favs-container {
    display: block;
    height: 100% !important;
    width: 100%;
  }
  #favs-container:empty {
    min-height: 100% !important;
  }
  #favorite {
    border-bottom-width: 1px;
    max-width: 100% !important;
  }
  .favorite-inner {
    border-right-width: 0;
    padding-left: 24px;
    text-align: left;
  }

  .controls {
    display: table;
  }
}
