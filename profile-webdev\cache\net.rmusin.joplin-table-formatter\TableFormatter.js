exports.default=function(t){var e={};function r(n){if(e[n])return e[n].exports;var o=e[n]={i:n,l:!1,exports:{}};return t[n].call(o.exports,o,o.exports,r),o.l=!0,o.exports}return r.m=t,r.c=e,r.d=function(t,e,n){r.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:n})},r.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},r.t=function(t,e){if(1&e&&(t=r(t)),8&e)return t;if(4&e&&"object"==typeof t&&t&&t.__esModule)return t;var n=Object.create(null);if(r.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var o in t)r.d(n,o,function(e){return t[e]}.bind(null,o));return n},r.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return r.d(e,"a",e),e},r.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},r.p="",r(r.s=10)}([function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.BasePadCalculator=void 0;var n=function(){function t(){}return t.prototype.baseGetRightPadding=function(t,e,r,n){return t.repeat(this.getRightPadCount(e.getLongestColumnLengths()[n],e.rows[r].cells[n].getLength()))},t.prototype.getRightPadCount=function(t,e){var r=t>0?t-e:1;return(0==e||t>0&&e>0)&&r++,r},t}();e.BasePadCalculator=n},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.Table=void 0;var n=function(){function t(t,e,r,n){if(void 0===n&&(n=""),this._separatorEOL=e,this._alignments=r,this.leftPad=n,this.hasLeftBorder=!1,this.hasRightBorder=!1,null!=t&&null!=t[0]&&t[0].cells.length!=r.length)throw new Error("The number of columns must match the number of alignments.");this._rows=t}return Object.defineProperty(t.prototype,"rows",{get:function(){return this._rows},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"alignments",{get:function(){return this._alignments},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"columnCount",{get:function(){return this.hasRows?this.rows[0].cells.length:0},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"rowCount",{get:function(){return this.hasRows?this.rows.length:0},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"separatorEOL",{get:function(){return this._separatorEOL},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"hasRows",{get:function(){return null!=this.rows&&this.rows.length>0},enumerable:!1,configurable:!0}),t.prototype.isEmpty=function(){return!this.hasRows},t.prototype.getLongestColumnLengths=function(){if(!this.hasRows)return[];for(var t=new Array(this.columnCount).fill(0),e=0;e<this.rows.length;e++)for(var r=0;r<this.rows[e].cells.length;r++)t[r]=Math.max(this.rows[e].cells[r].getLength(),t[r]);return t},t}();e.Table=n},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.Alignment=void 0,function(t){t[t.NotSet=0]="NotSet",t[t.Left=1]="Left",t[t.Center=2]="Center",t[t.Right=3]="Right"}(e.Alignment||(e.Alignment={}))},function(t,e,r){"use strict";var n,o=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.RightPadCalculator=void 0;var i=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return o(e,t),e.prototype.getLeftPadding=function(t,e,r,n){return t.repeat(this.getLeftPaddingCount(e,r,n))},e.prototype.getRightPadding=function(t,e,r,n){return t},e.prototype.getLeftPaddingCount=function(t,e,r){var n=t.getLongestColumnLengths()[r],o=n>0?n-t.rows[e].cells[r].getLength():1;return o+=this.extraPadCount(t)},e.prototype.extraPadCount=function(t){return 1},e}(r(0).BasePadCalculator);e.RightPadCalculator=i},function(t,e,r){"use strict";var n,o=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.CenterPadCalculator=void 0;var i=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return o(e,t),e.prototype.getLeftPadding=function(t,e,r,n){return t.repeat(Math.floor(this.totalPadCount(e,n,r)))},e.prototype.getRightPadding=function(t,e,r,n){return t.repeat(Math.ceil(this.totalPadCount(e,n,r)))},e.prototype.totalPadCount=function(t,e,r){var n=t.getLongestColumnLengths()[e],o=n>0?n-t.rows[r].cells[e].getLength():1;return(o+=this.extraPadCount(t))/2},e.prototype.extraPadCount=function(t){return 2},e}(r(0).BasePadCalculator);e.CenterPadCalculator=i},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.Range=void 0;var n=function(){function t(t,e){this._startLine=t,this._endLine=e}return Object.defineProperty(t.prototype,"startLine",{get:function(){return this._startLine},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"endLine",{get:function(){return this._endLine},enumerable:!1,configurable:!0}),t}();e.Range=n},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.Cell=void 0;var n=function(){function t(t){this._value=t}return t.prototype.getValue=function(){return this._value},t.prototype.getLength=function(){for(var t=0,e=0,r=this._value.length;e<r;e++)t+=this.getCharDisplayLength(this._value.charAt(e));return t},t.prototype.getCharDisplayLength=function(t){return/^(([\u{4E00}-\u{9FFF}])|([\u{3400}-\u{4DBF}])|([\u{20000}-\u{2A6DF}])|([\u{2A700}-\u{2B73F}])|([\u{2B740}-\u{2B81F}]))$/u.test(t)?2:1},t}();e.Cell=n},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.Row=void 0;var n=function(){function t(t,e){this._cells=t,this._eol=e}return Object.defineProperty(t.prototype,"cells",{get:function(){return this._cells},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"EOL",{get:function(){return this._eol},enumerable:!1,configurable:!0}),t}();e.Row=n},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.Transformer=void 0;var n=function(){function t(t){this._next=t}return t.prototype.process=function(t){if(null==t||t.isEmpty())return t;var e=this.transform(t);return null!=this._next&&(e=this._next.process(e)),e},t}();e.Transformer=n},function(t,e,r){"use strict";var n=this&&this.__createBinding||(Object.create?function(t,e,r,n){void 0===n&&(n=r),Object.defineProperty(t,n,{enumerable:!0,get:function(){return e[r]}})}:function(t,e,r,n){void 0===n&&(n=r),t[n]=e[r]}),o=this&&this.__exportStar||function(t,e){for(var r in t)"default"===r||Object.prototype.hasOwnProperty.call(e,r)||n(e,t,r)};Object.defineProperty(e,"__esModule",{value:!0}),o(r(44),e),o(r(45),e),o(r(46),e),o(r(47),e),o(r(48),e),o(r(49),e)},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});const n=r(11);t.exports={default:function(t){return{plugin:function(t){t.defineExtension("formatTable",(function(){const t=this,e=t.getCursor();let r=e.line;for(;r>=0&&"|"===t.getLine(r).trimStart().charAt(0);)r--;r++;let o=e.line;for(;t.getLine(o)&&"|"===t.getLine(o).trimStart().charAt(0);)o++;const i=t.getRange({line:r,ch:0},{line:o,ch:0}),u=n.CliPrettify.prettify(i);t.replaceRange(u,{line:r,ch:0},{line:o,ch:0})}))}}}}},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.CliPrettify=void 0;var n=r(12),o=r(15),i=r(16),u=r(17),a=r(18),l=r(19),c=r(20),s=r(21),f=r(22),p=r(24),d=r(25),h=r(26),y=r(28),g=r(30),_=r(31),v=r(9),b=r(50),m=r(51),w=function(){function t(){}return t.prettify=function(t){return this.createPrettyfier().formatTables(t)},t.check=function(t){if(this.prettify(t)!==t)throw new Error("The input file is not prettyfied!")},t.createPrettyfier=function(){var t=new f.ConsoleLogger;return new n.MultiTablePrettyfier(new o.TableFinder(new i.TableValidator(new u.SelectionInterpreter(!0))),new p.SingleTablePrettyfier(new a.TableFactory(new l.AlignmentFactory,new u.SelectionInterpreter(!1),new c.TrimmerTransformer(new s.BorderTransformer(null)),new m.FairTableIndentationDetector),new i.TableValidator(new u.SelectionInterpreter(!1)),new h.TableViewModelFactory(new y.RowViewModelFactory(new g.ContentPadCalculator(new _.PadCalculatorSelector," "),new v.AlignmentMarkerStrategy(":"))),new b.TableStringWriter,[t],new d.NoSizeLimitChecker),new d.NoSizeLimitChecker)},t}();e.CliPrettify=w},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.MultiTablePrettyfier=void 0;var n=r(13),o=function(){function t(t,e,r){this._tableFinder=t,this._singleTablePrettyfier=e,this._sizeLimitChecker=r}return t.prototype.formatTables=function(t){if(!this._sizeLimitChecker.isWithinAllowedSizeLimit(t))return t;for(var e=new n.Document(t),r=null,o=0;null!=(r=this._tableFinder.getNextRange(e,o));){var i=this._singleTablePrettyfier.prettifyTable(e,r);e.replaceTextInRange(r,i),o=r.endLine+1}return e.getText()},t}();e.MultiTablePrettyfier=o},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.Document=void 0;var n=r(14),o=r(5),i=function(){function t(t){this._lines=this.buildLines(t)}return Object.defineProperty(t.prototype,"lines",{get:function(){return this._lines},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"fullRange",{get:function(){return new o.Range(0,this._lines.length)},enumerable:!1,configurable:!0}),t.prototype.getLines=function(t){return null==t?this.lines:this.lines.slice(t.startLine,t.endLine+1)},t.prototype.getText=function(t){void 0===t&&(t=null);var e=this.getLines(t);return e.reduce((function(r,n,o){var i=null!=t&&o==e.length-1?"":n.EOL;return r+(n.value+i)}),"")},t.prototype.replaceTextInRange=function(t,e){var r=this.buildLines(e);if(t.endLine-t.startLine+1!==r.length)throw new Error("Unexpected range length of text to replace.");r[r.length-1].EOL=this.lines[t.endLine].EOL;for(var n=t.startLine;n<=t.endLine;n++)this.lines[n]=r[n-t.startLine]},t.prototype.buildLines=function(t){return(t.match(/[^\n]*\n|[^\n]+/g)||[""]).map((function(t){return new n.Line(t)}))},t}();e.Document=i},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.Line=void 0;var n=function(){function t(t){this._value=t.replace(/\r?\n|\r/g,""),this._eol=t.substr(this._value.length)}return Object.defineProperty(t.prototype,"value",{get:function(){return this._value},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"EOL",{get:function(){return this._eol},set:function(t){this._eol=t},enumerable:!1,configurable:!0}),t}();e.Line=n},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.TableFinder=void 0;var n=r(5),o=function(){function t(t){this._tableValidator=t,this._ignoreStart="\x3c!-- markdown-table-prettify-ignore-start --\x3e",this._ignoreEnd="\x3c!-- markdown-table-prettify-ignore-end --\x3e"}return t.prototype.getNextRange=function(t,e){for(var r=e,n=!1;r<t.lines.length;){if(t.lines[r].value.trim()==this._ignoreStart?n=!0:t.lines[r].value.trim()==this._ignoreEnd&&(n=!1),!n){var o=this._tableValidator.lineIsValidSeparator(t.lines[r].value)?this.getNextValidTableRange(t,r):{range:null,ignoreBlockStarted:n};if(n=o.ignoreBlockStarted,null!=o.range)return o.range}r++}return null},t.prototype.getNextValidTableRange=function(t,e){for(var r=e-1,o=e+1,i=null,u=!1;o<t.lines.length;){if(t.lines[o].value.trim()==this._ignoreStart){u=!0;break}var a=t.getText(new n.Range(r,o));if(!this._tableValidator.isValid(a))break;i=a,o++}return{range:null!=i?new n.Range(r,o-1):null,ignoreBlockStarted:u}},t}();e.TableFinder=o},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.TableValidator=void 0;var n=function(){function t(t){this._selectionInterpreter=t}return t.prototype.isValid=function(t){if(null==t||void 0===t)return!1;var e=this._selectionInterpreter.allRows(t);return e.length>2&&e[0].length>1&&e.every((function(t){return t.length==e[0].length}))&&this.hasValidSeparators(this._selectionInterpreter.separator(t))},t.prototype.lineIsValidSeparator=function(t){return this.hasValidSeparators(this._selectionInterpreter.splitLine(t))},t.prototype.hasValidSeparators=function(t){var e=this;return!(!t||t.length<1||!t[1])&&(!!t.every((function(t){return e.validSeparator(t)}))||this.allDashesWithBorder(t))},t.prototype.validSeparator=function(t){var e=t.trim();return":"==e[0]&&(e=e.slice(1)),":"==e[e.length-1]&&(e=e.slice(0,-1)),"-"===e.replace(/(?!^)-/g,"")},t.prototype.allDashesWithBorder=function(t){var e=this,r=t.length>=3&&""===t[0].trim(),n=t.length>=3&&""===t[t.length-1].trim(),o=r?1:0,i=n?t.length-2:t.length-1;return!(!t.filter((function(t,e){return e>=o&&e<=i})).every((function(t){return e.validSeparator(t)}))||!r&&!n)},t}();e.TableValidator=n},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.SelectionInterpreter=void 0;var n=function(){function t(t){this._strict=t}return t.prototype.allRows=function(t){var e=t.split(/\r\n|\r|\n/).map(this.splitLine,this);return this._strict?e:e.filter((function(t){return t.length>0&&!(1==t.length&&/^\s*$/.test(t[0]))}))},t.prototype.separator=function(t){return this.allRows(t).filter((function(t,e){return 1==e}))[0]},t.prototype.splitLine=function(t){if(null==t||0==t.length)return[];for(var e=[],r=-1,n=-1;(r=t.indexOf("|",r+1))>-1;)"\\"==t[r-1]||this.codeBlockOpenTill(t.substr(0,r))||(e.push(t.substring(n+1,r)),n=r);return e.push(t.substring(n+1)),e},t.prototype.codeBlockOpenTill=function(t){return(t.match(/`/g)||[]).length%2!=0},t}();e.SelectionInterpreter=n},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.TableFactory=void 0;var n=r(1),o=r(6),i=r(7),u=function(){function t(t,e,r,n){this._alignmentFactory=t,this._selectionInterpreter=e,this._transformer=r,this._tableIndentationDetector=n}return t.prototype.getModel=function(t,e){var r=this,u=t.getLines(e);if(null==u||0==u.length)throw new Error("Can't create table model from no lines.");var a=u.filter((function(t,e){return 1!=e})).map((function(t){return new i.Row(r._selectionInterpreter.splitLine(t.value).map((function(t){return new o.Cell(t)})),t.EOL)})),l=u[1],c=this._selectionInterpreter.splitLine(l.value),s=null!=c&&c.length>0?this._alignmentFactory.createAlignments(c):[],f=this._tableIndentationDetector.getLeftPad(u.map((function(t){return t.value})));return this._transformer.process(new n.Table(a,l.EOL,s,f))},t}();e.TableFactory=u},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.AlignmentFactory=void 0;var n=r(2),o=function(){function t(){}return t.prototype.createAlignments=function(t){var e=this;return t.map((function(t){return e.alignmentOf(t.trim())}))},t.prototype.alignmentOf=function(t){var e=":"==t[0],r=":"==t[t.length-1];return e&&r?n.Alignment.Center:r?n.Alignment.Right:e?n.Alignment.Left:n.Alignment.NotSet},t}();e.AlignmentFactory=o},function(t,e,r){"use strict";var n,o=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.TrimmerTransformer=void 0;var i=r(8),u=r(6),a=r(7),l=r(1),c=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return o(e,t),e.prototype.transform=function(t){return new l.Table(this.trimColumnValues(t.rows),t.separatorEOL,t.alignments,t.leftPad)},e.prototype.trimColumnValues=function(t){var e=[];if(null==t)return e;for(var r=0;r<t.length;r++)e.push(new a.Row(t[r].cells.map((function(t){return new u.Cell(t.getValue().trim())})),t[r].EOL));return e},e}(i.Transformer);e.TrimmerTransformer=c},function(t,e,r){"use strict";var n,o=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.BorderTransformer=void 0;var i=r(8),u=r(1),a=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return o(e,t),e.prototype.transform=function(t){if(null==t||t.isEmpty())return t;var e=this.isColumnEmpty(t.rows,0),r=this.isColumnEmpty(t.rows,t.columnCount-1),n=this.rowsWithoutEmptyFirstAndLastColumn(t.rows,e,r),o=this.alignmentsWithoutEmptyFirstAndLastColumn(t.alignments,e,r),i=e?t.leftPad:"",a=new u.Table(n,t.separatorEOL,o,i);return a.hasLeftBorder=e,a.hasRightBorder=this.hasRightBorder(e,r),a},e.prototype.isColumnEmpty=function(t,e){for(var r=0;r<t.length;r++){var n=t[r].cells[e];if(null!=n&&""!=n.getValue().trim())return!1}return!0},e.prototype.rowsWithoutEmptyFirstAndLastColumn=function(t,e,r){var n=t;return e&&this.removeColumn(n,0),r&&this.removeColumn(n,n[0].cells.length-1),n},e.prototype.removeColumn=function(t,e){for(var r=0;r<t.length;r++)t[r].cells.splice(e,1)},e.prototype.alignmentsWithoutEmptyFirstAndLastColumn=function(t,e,r){var n=t;return e&&n.shift(),r&&n.pop(),n},e.prototype.hasRightBorder=function(t,e){var r=e;return t&&!e&&(r=!0),!t&&e&&(r=!1),r},e}(i.Transformer);e.BorderTransformer=a},function(t,e,r){"use strict";var n,o=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.ConsoleLogger=void 0;var i=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return o(e,t),e.prototype.logInfo=function(e){t.prototype.logIfEnabled.call(this,console.log,e)},e.prototype.logError=function(e){t.prototype.logIfEnabled.call(this,console.error,e)},e}(r(23).BaseLogger);e.ConsoleLogger=i},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.BaseLogger=void 0;var n=function(){function t(){this._enabled=!0}return t.prototype.setEnabled=function(t){this._enabled=t},t.prototype.logIfEnabled=function(t,e){this._enabled&&t(e)},t}();e.BaseLogger=n},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.SingleTablePrettyfier=void 0;var n=function(){function t(t,e,r,n,o,i){this._tableFactory=t,this._tableValidator=e,this._viewModelFactory=r,this._writer=n,this._loggers=o,this._sizeLimitChecker=i}return t.prototype.prettifyTable=function(t,e){var r=null,n="",o=t.getText(e);try{if(!this._sizeLimitChecker.isWithinAllowedSizeLimit(o))return o;if(this._tableValidator.isValid(o)){var i=this._tableFactory.getModel(t,e),u=this._viewModelFactory.build(i);r=this._writer.writeTable(u)}else n="Can't parse table from invalid text.",r=o}catch(t){this._loggers.forEach((function(e){return e.logError(t)}))}return n&&this._loggers.forEach((function(t){return t.logInfo(n)})),r},t}();e.SingleTablePrettyfier=n},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.NoSizeLimitChecker=void 0;var n=function(){function t(){}return t.prototype.isWithinAllowedSizeLimit=function(t){return!0},t}();e.NoSizeLimitChecker=n},function(t,e,r){"use strict";var n=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(e,"__esModule",{value:!0}),e.TableViewModelFactory=void 0;var o=r(27),i=function(){function t(t){this._rowViewModelFactory=t}return t.prototype.build=function(t){var e=new o.TableViewModel;return e.leftPad=t.leftPad,e.hasLeftBorder=t.hasLeftBorder,e.hasRightBorder=t.hasRightBorder,e.header=this._rowViewModelFactory.buildRow(0,t),e.rows=this.buildRows(t),e.separator=this.buildSeparator(e,t),e},t.prototype.buildRows=function(t){for(var e=new Array(t.rowCount-1),r=1;r<t.rowCount;r++)e[r-1]=this._rowViewModelFactory.buildRow(r,t);return e},t.prototype.buildSeparator=function(t,e){var r,o,i=new Array;i.push(t.header);try{for(var u=n(t.rows),a=u.next();!a.done;a=u.next()){var l=a.value;i.push(l)}}catch(t){r={error:t}}finally{try{a&&!a.done&&(o=u.return)&&o.call(u)}finally{if(r)throw r.error}}return this._rowViewModelFactory.buildSeparator(i,e)},t}();e.TableViewModelFactory=i},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.TableViewModel=void 0;var n=function(){function t(){this.leftPad="",this.rows=[]}return Object.defineProperty(t.prototype,"columnCount",{get:function(){return this.header.columnCount},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"rowCount",{get:function(){return this.rows.length},enumerable:!1,configurable:!0}),t}();e.TableViewModel=n},function(t,e,r){"use strict";var n=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(e,"__esModule",{value:!0}),e.RowViewModelFactory=void 0;var o=r(29),i=function(){function t(t,e){this._contentPadCalculator=t,this._alignmentMarkerStrategy=e,this.separatorChar="-"}return t.prototype.buildRow=function(t,e){if(null==e)throw new Error("Paramter can't be null");for(var r=new Array(e.columnCount),n=0;n<e.columnCount;n++)r[n]=this._contentPadCalculator.getLeftPadding(e,t,n)+e.rows[t].cells[n].getValue()+this._contentPadCalculator.getRightPadding(e,t,n);return new o.RowViewModel(r,e.rows[t].EOL)},t.prototype.buildSeparator=function(t,e){var r,i,u=this,a=t[0].columnCount,l=Array(a).fill(0);try{for(var c=n(t),s=c.next();!s.done;s=c.next())for(var f=s.value,p=0;p<a;p++)l[p]=Math.max(l[p],f.getValueAt(p).length)}catch(t){r={error:t}}finally{try{s&&!s.done&&(i=c.return)&&i.call(c)}finally{if(r)throw r.error}}var d=l.map((function(t){return u.separatorChar.repeat(t)})).map((function(t,r){return u._alignmentMarkerStrategy.markerFor(e.alignments[r]).mark(t)}));return new o.RowViewModel(d,e.separatorEOL)},t}();e.RowViewModelFactory=i},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.RowViewModel=void 0;var n=function(){function t(t,e){this._values=t,this._eol=e}return Object.defineProperty(t.prototype,"columnCount",{get:function(){return this._values.length},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"EOL",{get:function(){return this._eol},enumerable:!1,configurable:!0}),t.prototype.getValueAt=function(t){var e=this._values.length-1;if(t<0||t>e)throw new Error("Argument out of range; should be between 0 and "+e+", but was "+t+".");return this._values[t]},t}();e.RowViewModel=n},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.ContentPadCalculator=void 0;var n=function(){function t(t,e){this._padCalculatorSelector=t,this._paddingChar=e}return t.prototype.getLeftPadding=function(t,e,r){return this._padCalculatorSelector.select(t,r).getLeftPadding(this._paddingChar,t,e,r)},t.prototype.getRightPadding=function(t,e,r){return this._padCalculatorSelector.select(t,r).getRightPadding(this._paddingChar,t,e,r)},t.prototype.getPadChar=function(){return this._paddingChar},t}();e.ContentPadCalculator=n},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.PadCalculatorSelector=void 0;var n=r(32),o=r(36),i=r(40),u=r(2),a=function(){function t(){}return t.prototype.select=function(t,e){switch(t.alignments[e]){case u.Alignment.Center:return this.centerAlignmentPadCalculator(t,e);case u.Alignment.Right:return this.rightAlignmentPadCalculator(t,e);default:return this.leftAlignmentPadCalculator(t,e)}},t.prototype.leftAlignmentPadCalculator=function(t,e){return 0==e?new n.FirstColumnPadCalculator:e==t.columnCount-1?new n.LastColumnPadCalculator:new n.MiddleColumnPadCalculator},t.prototype.centerAlignmentPadCalculator=function(t,e){return 0==e?new i.FirstColumnPadCalculator:e==t.columnCount-1?new i.LastColumnPadCalculator:new i.MiddleColumnPadCalculator},t.prototype.rightAlignmentPadCalculator=function(t,e){return 0==e?new o.FirstColumnPadCalculator:e==t.columnCount-1?new o.LastColumnPadCalculator:new o.MiddleColumnPadCalculator},t}();e.PadCalculatorSelector=a},function(t,e,r){"use strict";var n=this&&this.__createBinding||(Object.create?function(t,e,r,n){void 0===n&&(n=r),Object.defineProperty(t,n,{enumerable:!0,get:function(){return e[r]}})}:function(t,e,r,n){void 0===n&&(n=r),t[n]=e[r]}),o=this&&this.__exportStar||function(t,e){for(var r in t)"default"===r||Object.prototype.hasOwnProperty.call(e,r)||n(e,t,r)};Object.defineProperty(e,"__esModule",{value:!0}),o(r(33),e),o(r(34),e),o(r(35),e)},function(t,e,r){"use strict";var n,o=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.FirstColumnPadCalculator=void 0;var i=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return o(e,t),e.prototype.getLeftPadding=function(t,e,r,n){return e.hasLeftBorder?t:""},e.prototype.getRightPadding=function(e,r,n,o){return t.prototype.baseGetRightPadding.call(this,e,r,n,o)},e}(r(0).BasePadCalculator);e.FirstColumnPadCalculator=i},function(t,e,r){"use strict";var n,o=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.MiddleColumnPadCalculator=void 0;var i=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return o(e,t),e.prototype.getLeftPadding=function(t,e,r,n){return t},e.prototype.getRightPadding=function(e,r,n,o){return t.prototype.baseGetRightPadding.call(this,e,r,n,o)},e}(r(0).BasePadCalculator);e.MiddleColumnPadCalculator=i},function(t,e,r){"use strict";var n,o=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.LastColumnPadCalculator=void 0;var i=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return o(e,t),e.prototype.getLeftPadding=function(t,e,r,n){return""!=e.rows[r].cells[n].getValue()||e.hasRightBorder?t:""},e.prototype.getRightPadding=function(e,r,n,o){return r.hasRightBorder?t.prototype.baseGetRightPadding.call(this,e,r,n,o):""},e}(r(0).BasePadCalculator);e.LastColumnPadCalculator=i},function(t,e,r){"use strict";var n=this&&this.__createBinding||(Object.create?function(t,e,r,n){void 0===n&&(n=r),Object.defineProperty(t,n,{enumerable:!0,get:function(){return e[r]}})}:function(t,e,r,n){void 0===n&&(n=r),t[n]=e[r]}),o=this&&this.__exportStar||function(t,e){for(var r in t)"default"===r||Object.prototype.hasOwnProperty.call(e,r)||n(e,t,r)};Object.defineProperty(e,"__esModule",{value:!0}),o(r(37),e),o(r(38),e),o(r(39),e)},function(t,e,r){"use strict";var n,o=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.FirstColumnPadCalculator=void 0;var i=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return o(e,t),e.prototype.extraPadCount=function(t){return t.hasLeftBorder?1:0},e}(r(3).RightPadCalculator);e.FirstColumnPadCalculator=i},function(t,e,r){"use strict";var n,o=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.MiddleColumnPadCalculator=void 0;var i=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return o(e,t),e}(r(3).RightPadCalculator);e.MiddleColumnPadCalculator=i},function(t,e,r){"use strict";var n,o=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.LastColumnPadCalculator=void 0;var i=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return o(e,t),e.prototype.getRightPadding=function(e,r,n,o){return r.hasLeftBorder?t.prototype.getRightPadding.call(this,e,r,n,o):""},e}(r(3).RightPadCalculator);e.LastColumnPadCalculator=i},function(t,e,r){"use strict";var n=this&&this.__createBinding||(Object.create?function(t,e,r,n){void 0===n&&(n=r),Object.defineProperty(t,n,{enumerable:!0,get:function(){return e[r]}})}:function(t,e,r,n){void 0===n&&(n=r),t[n]=e[r]}),o=this&&this.__exportStar||function(t,e){for(var r in t)"default"===r||Object.prototype.hasOwnProperty.call(e,r)||n(e,t,r)};Object.defineProperty(e,"__esModule",{value:!0}),o(r(41),e),o(r(42),e),o(r(43),e)},function(t,e,r){"use strict";var n,o=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.FirstColumnPadCalculator=void 0;var i=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return o(e,t),e.prototype.extraPadCount=function(t){return t.hasLeftBorder?2:1},e}(r(4).CenterPadCalculator);e.FirstColumnPadCalculator=i},function(t,e,r){"use strict";var n,o=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.MiddleColumnPadCalculator=void 0;var i=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return o(e,t),e}(r(4).CenterPadCalculator);e.MiddleColumnPadCalculator=i},function(t,e,r){"use strict";var n,o=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.LastColumnPadCalculator=void 0;var i=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return o(e,t),e.prototype.getRightPadding=function(e,r,n,o){return r.hasRightBorder?t.prototype.getRightPadding.call(this,e,r,n,o):""},e}(r(4).CenterPadCalculator);e.LastColumnPadCalculator=i},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0})},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.AlignmentMarkerStrategy=void 0;var n=r(2),o=r(9),i=function(){function t(t){this._markerChar=t}return t.prototype.markerFor=function(t){switch(t){case n.Alignment.Left:return new o.LeftAlignmentMarker(this._markerChar);case n.Alignment.Right:return new o.RightAlignmentMarker(this._markerChar);case n.Alignment.Center:return new o.CenterAlignmentMarker(this._markerChar);default:return new o.NotSetAlignmentMarker}},t}();e.AlignmentMarkerStrategy=i},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.CenterAlignmentMarker=void 0;var n=function(){function t(t){this._markerChar=t}return t.prototype.mark=function(t){return null==t||t.length<2?t:this._markerChar+t.substring(1,t.length-1)+this._markerChar},t}();e.CenterAlignmentMarker=n},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.LeftAlignmentMarker=void 0;var n=function(){function t(t){this._markerChar=t}return t.prototype.mark=function(t){return null==t||t.length<2?t:this._markerChar+t.substr(1)},t}();e.LeftAlignmentMarker=n},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.NotSetAlignmentMarker=void 0;var n=function(){function t(){}return t.prototype.mark=function(t){return t},t}();e.NotSetAlignmentMarker=n},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.RightAlignmentMarker=void 0;var n=function(){function t(t){this._markerChar=t}return t.prototype.mark=function(t){return null==t||t.length<2?t:t.substring(0,t.length-1)+this._markerChar},t}();e.RightAlignmentMarker=n},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.TableStringWriter=void 0;var n=function(){function t(){}return t.prototype.writeTable=function(t){if(null==t)throw new Error("Table can't be null.");if(null==t.header)throw new Error("Table must have a header.");if(null==t.separator)throw new Error("Table must have a separator.");if(null==t.rows||0==t.rowCount)throw new Error("Table must have rows.");var e="";return e+=this.writeRowViewModel(t.header,t,!0),e+=this.writeRowViewModel(t.separator,t,!0),e+=this.writeRows(t)},t.prototype.writeRows=function(t){for(var e="",r=0;r<t.rowCount;r++)e+=this.writeRowViewModel(t.rows[r],t,r!=t.rowCount-1);return e},t.prototype.writeRowViewModel=function(t,e,r){var n="";n+=e.leftPad,n+=this.getLeftBorderIfNeeded(e);for(var o=0;o<e.columnCount;o++)n+=t.getValueAt(o),n+=this.getSeparatorIfNeeded(e,o);return n+=this.getRightBorderIfNeeded(e),r&&(n+=t.EOL),n},t.prototype.getSeparatorIfNeeded=function(t,e){return e!=t.columnCount-1?"|":""},t.prototype.getLeftBorderIfNeeded=function(t){return t.hasLeftBorder?"|":""},t.prototype.getRightBorderIfNeeded=function(t){return t.hasRightBorder?"|":""},t}();e.TableStringWriter=n},function(t,e,r){"use strict";var n,o=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),i=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},u=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,o,i=r.call(t),u=[];try{for(;(void 0===e||e-- >0)&&!(n=i.next()).done;)u.push(n.value)}catch(t){o={error:t}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return u},a=this&&this.__spreadArray||function(t,e){for(var r=0,n=e.length,o=t.length;r<n;r++,o++)t[o]=e[r];return t};Object.defineProperty(e,"__esModule",{value:!0}),e.FairTableIndentationDetector=e.TableIndentationDetector=void 0;var l=function(){function t(){}return t.prototype.getLeftPad=function(t){var e=t.map((function(t){return t.match(/^\s*/)[0]}));return this.hasIndentation(e)?this.getIndentationChars(e):""},t}();e.TableIndentationDetector=l;var c=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return o(e,t),e.prototype.hasIndentation=function(t){var e=t.length;return t.filter((function(t){return t.length>0})).length>=e/2},e.prototype.getIndentationChars=function(t){var e,r,n=t.filter((function(t){return t.length>0})),o=new Map;try{for(var l=i(n),c=l.next();!c.done;c=l.next()){var s=c.value,f=1;o.has(s)&&(f+=o.get(s)),o.set(s,++f)}}catch(t){e={error:t}}finally{try{c&&!c.done&&(r=l.return)&&r.call(l)}finally{if(e)throw e.error}}var p=a([],u(o.entries())).reduce((function(t,e){return e[1]>t[1]?e:t}));return p[1]>1?p[0]:o[0]},e}(l);e.FairTableIndentationDetector=c}]).default;