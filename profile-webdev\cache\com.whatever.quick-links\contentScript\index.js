(()=>{"use strict";var t={400:function(t,e){var n=this&&this.__awaiter||function(t,e,n,o){return new(n||(n=Promise))((function(i,s){function l(t){try{c(o.next(t))}catch(t){s(t)}}function r(t){try{c(o.throw(t))}catch(t){s(t)}}function c(t){var e;t.done?i(t.value):(e=t.value,e instanceof n?e:new n((function(t){t(e)}))).then(l,r)}c((o=o.apply(t,e||[])).next())}))};Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t,e){function o(e,o){let i="New Note";o&&(i="New Task");return{text:e,hint:(i,s,l)=>n(this,void 0,void 0,(function*(){const n=l.from||s.from;n.ch-=2;const r=yield t.postMessage({command:"createNote",title:e,todo:o});i.replaceRange(`[${e}](:/${r.newNote.id})`,n,i.getCursor(),"complete")})),render:(t,n,o)=>{const s=t.ownerDocument.createElement("div");s.setAttribute("style","width: 100%; display:table;"),t.appendChild(s),s.innerHTML=`\n\t\t\t\t\t<div style="display:table-cell; padding-right: 5px">${e}</div>\n\t\t\t\t\t<div style="display:table-cell; text-align: right;"><small><em>${i}</em></small></div>\n\t\t\t\t\t`}}}const i=e=>n(this,void 0,void 0,(function*(){const i=yield t.postMessage({command:"getNotes",prefix:e});let s=[];const l=i.notes;for(let t=0;t<l.length;t++){const e=l[t],o={text:e.title,hint:(t,o,s)=>n(this,void 0,void 0,(function*(){const n=s.from||o.from;if(n.ch-=2,t.replaceRange(`[${e.title}](:/${e.id})`,n,t.getCursor(),"complete"),i.selectText){const o=Object.assign({},n),i=Object.assign({},n);o.ch+=1,i.ch+=1+e.title.length,t.setSelection(o,i)}}))};if(i.showFolders){const t=e.folder?e.folder:"unknown";o.render=(n,o,i)=>{const s=n.ownerDocument.createElement("div");s.setAttribute("style","width: 100%; display:table;"),n.appendChild(s),s.innerHTML=`\n\t\t\t\t\t<div style="display:table-cell; padding-right: 5px">${e.title}</div>\n\t\t\t\t\t<div style="display:table-cell; text-align: right;"><small><em>In ${t}</em></small></div>\n\t\t\t\t\t`}}else o.displayText=e.title;s.push(o)}return i.allowNewNotes&&e&&(s.push(o(e,!1)),s.push(o(e,!0))),s}));e.defineOption("quickLinks",!1,(function(t,o,s){o&&t.on("inputRead",(function(o,s){return n(this,void 0,void 0,(function*(){if(!o.state.completionActive&&t.getTokenAt(t.getCursor()).string.startsWith("@@")){const n={line:s.from.line,ch:s.from.ch+1},o=function(t,e){const o=t.getCursor();let l=t.getRange(n,o)||"";i(l).then((t=>{e({list:t,from:{line:s.from.line,ch:s.from.ch+1},to:{line:s.to.line,ch:s.to.ch+1}})}))};setTimeout((function(){e.showHint(t,o,{completeSingle:!1,closeOnUnfocus:!0,async:!0,closeCharacters:/[()\[\]{};:>,]/})}),10)}}))}))}))}},36:function(t,e,n){var o=this&&this.__awaiter||function(t,e,n,o){return new(n||(n=Promise))((function(i,s){function l(t){try{c(o.next(t))}catch(t){s(t)}}function r(t){try{c(o.throw(t))}catch(t){s(t)}}function c(t){var e;t.done?i(t.value):(e=t.value,e instanceof n?e:new n((function(t){t(e)}))).then(l,r)}c((o=o.apply(t,e||[])).next())}))};Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t,e){const{autocompletion:i,insertCompletionText:s}=n(808),{EditorSelection:l}=n(864),r=e=>o(this,void 0,void 0,(function*(){var n;const i=e.matchBefore(/[@][@][^()\[\]{};:>,\n]*/);if(!i||i.from===i.to&&!e.explicit)return null;const r=i.text.substring(2),c=yield t.postMessage({command:"getNotes",prefix:r}),a=(t,e)=>(n,o,i,r)=>{const a=`[${t}](:/${e})`;if(n.dispatch(s(n.state,a,i,r)),c.selectText){const e=i+1,o=e+t.length;n.dispatch({selection:l.range(e,o)})}},d=c.notes,u=[];for(const t of d)u.push({apply:a(t.title,t.id),label:t.title,detail:c.showFolders?`In ${null!==(n=t.folder)&&void 0!==n?n:"unknown"}`:void 0});const f=e=>{const n=r,i=e?"New Task":"New Note";u.push({label:i,detail:`"${n}"`,apply:(i,s,l,r)=>o(this,void 0,void 0,(function*(){const o=yield t.postMessage({command:"createNote",title:n,todo:e});a(n,o.newNote.id)(i,s,l,r)}))})};return c.allowNewNotes&&r.length>0&&(f(!0),f(!1)),{from:i.from,options:u,filter:!1}}));let c;c=e.joplinExtensions?e.joplinExtensions.completionSource(r):i({override:[r]}),e.addExtension([c,i({tooltipClass:()=>"quick-links-completions"})])}},424:(t,e,n)=>{Object.defineProperty(e,"__esModule",{value:!0});const o=n(400),i=n(36);t.exports={default:function(t){return{plugin:e=>e.cm6?(0,i.default)(t,e):(0,o.default)(t,e),codeMirrorResources:["addon/hint/show-hint"],codeMirrorOptions:{quickLinks:!0},assets:function(){return[{name:"./show-hint.css"}]}}}}},808:t=>{t.exports=require("@codemirror/autocomplete")},864:t=>{t.exports=require("@codemirror/state")}},e={},n=function n(o){var i=e[o];if(void 0!==i)return i.exports;var s=e[o]={exports:{}};return t[o].call(s.exports,s,s.exports,n),s.exports}(424);exports.default=n.default})();