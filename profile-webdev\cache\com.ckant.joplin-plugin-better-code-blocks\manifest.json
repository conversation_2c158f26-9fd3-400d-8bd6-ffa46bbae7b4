{"manifest_version": 1, "id": "com.ckant.joplin-plugin-better-code-blocks", "app_min_version": "2.11", "version": "2.0.1", "name": "Better Code Blocks", "description": "Enhances code blocks with inline rendering, autocompletion, and more!", "author": "<PERSON>", "homepage_url": "https://github.com/ckant/joplin-plugin-better-code-blocks", "repository_url": "https://github.com/ckant/joplin-plugin-better-code-blocks", "keywords": ["autocomplete", "code", "coding", "developer", "programming", "render", "rendering"], "categories": ["developer tools", "editor", "productivity"], "screenshots": [], "icons": {"16": "media/logo16.png", "32": "media/logo32.png", "48": "media/logo48.png", "128": "media/logo128.png"}, "promo_tile": {"src": "media/promo.png"}, "_package_hash": "47c7b39b080ee48d04ba309e61b8141d"}