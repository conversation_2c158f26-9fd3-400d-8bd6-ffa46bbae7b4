:root .imageEditorContainer {
	height: 100vh;
	width: 100vw;
}

body,
:root {
	padding: 0;
	margin: 0;
	font-size: 11pt;
}

body > :not(.imageEditorContainer):not(.clr-picker) button,
body > button {
	color: var(--joplin-color4);
	background-color: var(--joplin-background-color4);
	border: 1px solid rgba(100, 100, 100, 0.5);
	border-radius: 3px;
	padding: 8px;
	cursor: pointer;
}

button:focus-visible {
	border: 2px solid rgba(230, 200, 100, 0.6);
}

.save-or-exit-dialog {
	width: 100vw;
	height: 100vh;
	font-size: 2em;

	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
}

:root.new-window-editor .save-or-exit-dialog {
	height: calc(100vh - 4em);
}

.save-or-exit-dialog.has-unsaved-changes button {
	font-size: 1.1rem;
}

.save-or-exit-dialog:not(.has-unsaved-changes) .save-changes-button {
	display: none;
}

.save-or-exit-dialog .save-changes-button.save-changes-button {
	font-size: 1rem;
}

.save-or-exit-dialog input[type='radio'] {
	min-width: 20px;
}

.save-or-exit-dialog .button-container {
	position: absolute;
	bottom: 0;
	right: 0;

	display: flex;
	flex-direction: column;
	align-items: end;
}

.save-or-exit-dialog .button-container button {
	margin-top: 12px;
}

:root:not(.new-window-editor) .imageEditorContainer.match-joplin-theme {
	--background-color-1: var(--joplin-background-color);
	--foreground-color-1: var(--joplin-color);
	--background-color-2: var(--joplin-background-color3);
	--foreground-color-2: var(--joplin-color);
	--background-color-3: var(--joplin-raised-background-color);
	--foreground-color-3: var(--joplin-raised-color);

	--selection-background-color: var(--joplin-background-color-hover3);
	--selection-foreground-color: var(--joplin-color3);
	--primary-action-foreground-color: var(--joplin-color4);
}

:root .imageEditorContainer.js-draw-default-light {
	/* Copied from the js-draw README:*/
	/* Used for unselected buttons and dialog text. */
	--background-color-1: white;
	--foreground-color-1: black;

	/* Used for some menu/toolbar backgrounds. */
	--background-color-2: #f5f5f5;
	--foreground-color-2: #2c303a;

	/* Used for other menu/toolbar backgrounds. */
	--background-color-3: #e5e5e5;
	--foreground-color-3: #1c202a;

	/* Used for selected buttons. */
	--selection-background-color: #cbdaf1;
	--selection-foreground-color: #2c303a;

	/* Used for dialog backgrounds */
	--background-color-transparent: rgba(105, 100, 100, 0.5);

	/* Used for shadows */
	--shadow-color: rgba(0, 0, 0, 0.5);

	/* Color used for some button/input foregrounds */
	--primary-action-foreground-color: #15b;
}

:root .imageEditorContainer.js-draw-default-dark {
	/* Also from the js-draw README */
	--background-color-1: #151515;
	--foreground-color-1: white;

	--background-color-2: #222;
	--foreground-color-2: #efefef;

	--background-color-3: #272627;
	--foreground-color-3: #eee;

	--selection-background-color: #607;
	--selection-foreground-color: white;
	--shadow-color: rgba(250, 250, 250, 0.5);
	--background-color-transparent: rgba(50, 50, 50, 0.5);

	--primary-action-foreground-color: #7ae;
}

/* Make scrollbars similar to Joplin's */
*::-webkit-scrollbar {
	width: 5px;
	height: 5px;
}

*::-webkit-scrollbar-thumb {
	background-color: #666;
	border-radius: 5px;
}
