(()=>{"use strict";var t={143:(t,e)=>{var o,n,i,a,l,r,u,c,d,s,p;Object.defineProperty(e,"__esModule",{value:!0}),e.ContentScriptType=e.SettingStorage=e.AppType=e.SettingItemSubType=e.SettingItemType=e.ToastType=e.<PERSON>onLocation=e.isContextMenuItemLocation=e.MenuItemLocation=e.ModelType=e.ImportModuleOutputFormat=e.FileSystemItem=void 0,(p=e.FileSystemItem||(e.FileSystemItem={})).File="file",p.Directory="directory",(s=e.ImportModuleOutputFormat||(e.ImportModuleOutputFormat={})).Markdown="md",s.Html="html",(d=e.ModelType||(e.ModelType={}))[d.Note=1]="Note",d[d.Folder=2]="Folder",d[d.Setting=3]="Setting",d[d.Resource=4]="Resource",d[d.Tag=5]="Tag",d[d.NoteTag=6]="NoteTag",d[d.Search=7]="Search",d[d.Alarm=8]="Alarm",d[d.MasterKey=9]="MasterKey",d[d.ItemChange=10]="ItemChange",d[d.NoteResource=11]="NoteResource",d[d.ResourceLocalState=12]="ResourceLocalState",d[d.Revision=13]="Revision",d[d.Migration=14]="Migration",d[d.SmartFilter=15]="SmartFilter",d[d.Command=16]="Command",function(t){t.File="file",t.Edit="edit",t.View="view",t.Note="note",t.Tools="tools",t.Help="help",t.Context="context",t.NoteListContextMenu="noteListContextMenu",t.EditorContextMenu="editorContextMenu",t.FolderContextMenu="folderContextMenu",t.TagContextMenu="tagContextMenu"}(o=e.MenuItemLocation||(e.MenuItemLocation={})),e.isContextMenuItemLocation=function(t){return[o.Context,o.NoteListContextMenu,o.EditorContextMenu,o.FolderContextMenu,o.TagContextMenu].includes(t)},(c=e.ToolbarButtonLocation||(e.ToolbarButtonLocation={})).NoteToolbar="noteToolbar",c.EditorToolbar="editorToolbar",(u=e.ToastType||(e.ToastType={})).Info="info",u.Success="success",u.Error="error",(r=e.SettingItemType||(e.SettingItemType={}))[r.Int=1]="Int",r[r.String=2]="String",r[r.Bool=3]="Bool",r[r.Array=4]="Array",r[r.Object=5]="Object",r[r.Button=6]="Button",(l=e.SettingItemSubType||(e.SettingItemSubType={})).FilePathAndArgs="file_path_and_args",l.FilePath="file_path",l.DirectoryPath="directory_path",(a=e.AppType||(e.AppType={})).Desktop="desktop",a.Mobile="mobile",a.Cli="cli",(i=e.SettingStorage||(e.SettingStorage={}))[i.Database=1]="Database",i[i.File=2]="File",(n=e.ContentScriptType||(e.ContentScriptType={})).MarkdownItPlugin="markdownItPlugin",n.CodeMirrorPlugin="codeMirrorPlugin"},156:function(t,e,o){var n=this&&this.__awaiter||function(t,e,o,n){return new(o||(o=Promise))((function(i,a){function l(t){try{u(n.next(t))}catch(t){a(t)}}function r(t){try{u(n.throw(t))}catch(t){a(t)}}function u(t){var e;t.done?i(t.value):(e=t.value,e instanceof o?e:new o((function(t){t(e)}))).then(l,r)}u((n=n.apply(t,e||[])).next())}))};Object.defineProperty(e,"__esModule",{value:!0});const i=o(998),a=o(143);i.default.plugins.register({onStart:function(){return n(this,void 0,void 0,(function*(){const t=i.default.views.dialogs,e=yield t.create("button_dialog");yield t.setHtml(e,'\n\t<h1>Futuremotion UI Elements</h1>\n\t<form id="fmui_button_form" name="fm_button_form">\n\t\t<label for="button-color">Select the button color:</label>\n        <select name="button_color" id="button-color" class="select">\n            <option value="white">White</option>\n            <option value="darkgray">Dark Gray</option>\n            <option value="lightblue">Light Blue</option>\n            <option value="blue">Blue</option>\n            <option value="blue-outline">Blue Outline</option>\n        </select>\n        <label for="button-size">Select the button size:</label>\n        <select name="button_size" id="button-size" class="select">\n            <option value="default">Default</option>\n            <option value="large">Large</option>\n            <option value="xlarge">Extra Large</option>\n        </select>\n        <label for "button-font">Select the button font:</label>\n        <select name="button_font" id="button-font" class="select">\n            <option value="font-helvnow">Helvetica Now Text</option>\n            <option value="font-roboto">Roboto</option>\n            <option value="font-inter">Inter</option>\n        </select>\n        <label for "button-arrow">Add an arrow?</label>\n        <select name="button_arrow" id="button-arrow" class="select">\n            <option value="">None</option>\n            <option value="arrowbefore">Before</option>\n            <option value="arrowafter">After</option>\n        </select>\n\t</form>'),yield i.default.views.dialogs.addScript(e,"button_modal.js"),yield i.default.views.dialogs.addScript(e,"button_modal.css"),yield t.setButtons(e,[{id:"add",title:"Add"},{id:"cancel",title:"Cancel"}]),yield i.default.commands.register({name:"CreateButton",label:"Creates a button.",enabledCondition:"markdownEditorPaneVisible && !richTextEditorVisible",iconName:"fas fa-code",execute:()=>n(this,void 0,void 0,(function*(){let o;o=!1;var n,a=yield i.default.commands.execute("selectedText");(null===(n=a)||""===n.trim())&&(o=!0,a="Button Text");const l=yield t.open(e);if("cancel"==l.id)return void(yield i.default.commands.execute("editor.focus"));yield i.default.commands.execute("editor.focus");var r=l.formData.fm_button_form;let u;u=`<span class="fm-enhanced-link ${r.button_color} ${r.button_size} ${r.button_font} ${r.button_arrow}">${a}</span>`,!1===o?yield i.default.commands.execute("replaceSelection",u):yield i.default.commands.execute("insertText",u),yield i.default.commands.execute("editor.focus")}))}),yield i.default.views.toolbarButtons.create("CreateHTMLButton","CreateButton",a.ToolbarButtonLocation.EditorToolbar)}))}})},998:(t,e)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.default=joplin}},e={};!function o(n){var i=e[n];if(void 0!==i)return i.exports;var a=e[n]={exports:{}};return t[n].call(a.exports,a,a.exports,o),a.exports}(156)})();