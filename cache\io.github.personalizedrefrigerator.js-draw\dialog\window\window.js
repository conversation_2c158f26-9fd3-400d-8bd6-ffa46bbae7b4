(()=>{"use strict";(()=>{const e=e=>{const t=document.createElement("p");t.appendChild(document.createTextNode(e)),t.onclick=()=>t.remove();const n=document.querySelector("#log-container");null==n||n.appendChild(t)};let t=[];window.onmessage=n=>{var o;if(!n.origin.startsWith("file:"))return void e(`Ignored message with origin ${n.origin}`);const s=n.data;if("addScript"===s.kind){const e=n.data.src;if(e.endsWith(".js")){const t=document.createElement("script");t.src=e,document.head.appendChild(t)}else{const t=document.createElement("link");t.href=e,t.rel="stylesheet",document.head.appendChild(t)}}else if("setHtml"===s.kind)document.body.innerHTML=s.html;else if("setButtons"===s.kind){const e=s.buttons,t=[];for(const n of e){const e=document.createElement("button");e.innerText=null!==(o=n.title)&&void 0!==o?o:n.id,e.onclick=()=>{window.parent.postMessage({kind:"dialogResult",result:{id:n.id}},"*"),window.close()},t.push(e)}let n=document.querySelector(".button-container");n&&n.remove(),n=document.createElement("div"),n.classList.add("button-container"),n.replaceChildren(...t),document.body.appendChild(n)}else t.forEach((e=>e(n)))},window.addEventListener("error",(t=>{e(t.toString()+t.error.stack)})),window.webviewApi={postMessage:e=>{const n=Math.random();return window.parent.postMessage({message:e,id:n},"*"),new Promise((e=>{const o=s=>{s.data.responseId===n&&(t=t.filter((e=>e!==o)),e(s.data.response))};t.push(o)}))},onMessage:e=>{t.push((t=>{t.data.message&&e(t.data)}))}}})()})();