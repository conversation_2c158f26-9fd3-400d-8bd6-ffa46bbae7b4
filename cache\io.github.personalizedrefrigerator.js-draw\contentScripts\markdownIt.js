(()=>{"use strict";var e={167:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.default=e=>{const t=e.querySelector("img");if(!t)return void console.warn("Freehand drawing plugin: No image found in container.");if(e.classList.contains("js-draw--editable"))return;e.classList.add("js-draw--editable");const a=e.getAttribute("data-js-draw-edit-label"),i=e.getAttribute("data-js-draw-content-script-id"),n=()=>{const e=t.src;webviewApi.postMessage(i,e).catch((t=>{console.error("Error posting message!",t,"\nMessage: ",e)}))},s=document.body.classList.contains("mce-content-body")||"tinymce"===document.body.id,r="undefined"!=typeof webviewApi;s?t.style.cursor="pointer":r&&(()=>{const t=document.createElement("button");t.textContent=`${a} 🖊️`,t.classList.add("jsdraw--editButton"),e.appendChild(t),t.onclick=()=>{n()}})(),t.ondblclick=n}},31:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.getLocales=t.setLocale=void 0;const a={insertDrawing:"Insert Drawing",insertDrawingInNewWindow:"Insert drawing in new window",restoreFromAutosave:"Restore from autosaved drawing",deleteAutosave:"Delete all autosaved drawings",noSuchAutosaveExists:"No autosave exists",discardChanges:"Discard changes",defaultImageTitle:"Freehand Drawing",edit:"Edit",close:"Close",saveAndClose:"Save and close",overwriteExisting:"Overwrite existing",saveAsNewDrawing:"Save as a new drawing",clickBelowToContinue:"Done! Click below to continue.",discardUnsavedChanges:"Discard unsaved changes?",resumeEditing:"Resume editing",saveAndResumeEditing:"Save and resume editing",saveChanges:"Save changes",exitInstructions:"All changes saved! Click below to exit.",settingsPaneDescription:"Settings for the Freehand Drawing image editor.",setting__disableFullScreen:"Dialog mode",setting__disableFullScreenDescription:"Enabling this setting causes the editor to only partially fill the Joplin window.",setting__autosaveIntervalSettingLabel:"Autosave interval (minutes)",setting__autosaveIntervalSettingDescription:'Adjusts how often a backup copy of the current drawing is created. The most recent autosave can be restored by searching for ":restore autosave" in the command palette (ctrl+shift+p or cmd+shift+p on MacOS) and clicking "Restore from autosaved drawing". If this setting is set to zero, autosaves are created every two minutes.',setting__themeLabel:"Theme",setting__toolbarTypeLabel:"Toolbar type",setting__toolbarTypeDescription:"This setting switches between possible toolbar user interfaces for the image editor.",setting__keyboardShortcuts:"Keyboard shortcuts",toolbarTypeDefault:"Default",toolbarTypeSidebar:"Sidebar",toolbarTypeDropdown:"Dropdown",styleMatchJoplin:"Match Joplin",styleJsDrawLight:"Light",styleJsDrawDark:"Dark",images:"Images",pdfs:"PDFs",allFiles:"All Files",loadLargePdf:e=>`A selected file is a large PDF (${e} pages). Loading it may take some time and increase the size of the current drawing. Continue?`,notAnEditableImage:(e,t)=>`Resource ${e} is not an editable image. Unable to edit resource of type ${t}.`},i={de:{insertDrawing:"Zeichnung einfügen",restoreFromAutosave:"Automatische Sicherung wiederherstellen",deleteAutosave:"Alle automatischen Sicherungen löschen",noSuchAutosaveExists:"Keine automatischen Sicherungen vorhanden",discardChanges:"Änderungen verwerfen",defaultImageTitle:"Freihand-Zeichnen",edit:"Bearbeiten",close:"Schließen",overwriteExisting:"Existierende Zeichnung überschreiben",saveAsNewDrawing:"Als neue Zeichnung speichern",clickBelowToContinue:"Fertig! Klicke auf „Ok“ um fortzufahen.",discardUnsavedChanges:"Ungespeicherte Änderungen verwerfen?",resumeEditing:"Bearbeiten fortfahren",notAnEditableImage:(e,t)=>`Die Ressource ${e} ist kein bearbeitbares Bild. Ressource vom Typ ${t} kann nicht bearbeitet werden.`},en:a,es:{insertDrawing:"Añada dibujo",restoreFromAutosave:"Resturar al autoguardado",deleteAutosave:"Borrar el autoguardado",noSuchAutosaveExists:"No autoguardado existe",discardChanges:"Descartar cambios",defaultImageTitle:"Dibujo",edit:"Editar",close:"Cerrar",saveAndClose:"Guardar y cerrar",overwriteExisting:"Sobrescribir existente",saveAsNewDrawing:"Guardar como dibujo nuevo",clickBelowToContinue:"Guardado. Ponga «ok» para continuar.",discardUnsavedChanges:"¿Descartar cambios no guardados?",resumeEditing:"Continuar editando",saveAndResumeEditing:"Guardar y continuar editando"},ro:{insertDrawing:"Inserează un desen",insertDrawingInNewWindow:"Inserează un desen într-o fereastră nouă",restoreFromAutosave:"Restaurează dintr-un desen salvat automat",deleteAutosave:"Șterge toate desenele salvate automat",noSuchAutosaveExists:"Nicio salvare automată nu există",discardChanges:"Anulează modificările",defaultImageTitle:"Desen liber",edit:"Editează",close:"Închide",saveAndClose:"Salvează și închide",overwriteExisting:"Suprascrie existent",saveAsNewDrawing:"Salvează ca desen nou",clickBelowToContinue:"Gata! Fă clic mai jos pentru a continua.",discardUnsavedChanges:"Anulezi modificările nesalvate?",resumeEditing:"Continuă editarea",saveAndResumeEditing:"Salvează și continuă editarea",saveChanges:"Salvează modificările",exitInstructions:"Toate modificările au fost salvate! Fă clic mai jos pentru a ieși.",settingsPaneDescription:"Setări pentru editorul de imagine liber.",setting__disableFullScreen:"Mod dialog",setting__disableFullScreenDescription:"Activarea acestei opțiuni face ca editorul să acopere doar parțial fereastra Joplin.",setting__autosaveIntervalSettingLabel:"Interval salvare automată (minute)",setting__autosaveIntervalSettingDescription:'Ajustează cât de des se face o copie de siguranță a desenului curent. Cea mai recentă versiune salvată automat poate fi restaurată căutând după ":restore autosave" în paleta de comenzi (Ctrl+Shift+P sau Cmd+Shift+P pe MacOS) și făcând clic pe „Restaurează dintr-un desen salvat automat”. Dacă acestă setare este 0, salvările automate sunt create la fiecare 2 minute.',setting__themeLabel:"Temă",setting__toolbarTypeLabel:"Tip bară de instrumente",setting__toolbarTypeDescription:"Această setare comută între posibilele interfețe pentru editorul de imagine.",setting__keyboardShortcuts:"Scurtături de la tastatură",toolbarTypeDefault:"Implicit",toolbarTypeSidebar:"Bară laterală",toolbarTypeDropdown:"Casete derulante",styleMatchJoplin:"La fel ca Joplin",styleJsDrawLight:"Luminoasă",styleJsDrawDark:"Întunecată",images:"Imagini",pdfs:"PDF-uri",allFiles:"Toate fișierele",loadLargePdf:e=>`Un fișier PDF selectat este un fișier mare (${e} de pagini). Încărcarea lui ar putea dura ceva timp și să crească dimensiunea desenului curent. Continui?`,notAnEditableImage:(e,t)=>`Resursa ${e} nu este o imagine editabilă. Nu se poate edita resursa de tipul ${t}.`}};let n,s=[],r=!1;t.setLocale=e=>{"string"==typeof e&&(e=[e]),(e=>{const t=[...e];for(let a of e){a=a.replace("_","-");const e=a.indexOf("-");-1!==e&&t.push(a.substring(0,e))}for(const e of t)if(e in i){n=i[e];break}s=t})(e),r=!0},(0,t.setLocale)(navigator.languages),t.getLocales=()=>[...s],t.default=new Proxy(a,{get(e,t){var i;r||console.warn("Accessing language data without a localization set. The default Electron locale will be used.");const s=t;return null!==(i=null==n?void 0:n[s])&&void 0!==i?i:a[s]}})}},t={};function a(i){var n=t[i];if(void 0!==n)return n.exports;var s=t[i]={exports:{}};return e[i](s,s.exports,a),s.exports}var i={};(()=>{var e=i;const t=a(167),n=a(31);e.default=({contentScriptId:e})=>({plugin:(a,i)=>{(0,n.setLocale)(i.settingValue("locale"));const s=a.renderer.rules.image;a.renderer.rules.image=(i,r,o,l,d)=>{var u;const c=null!==(u=null==s?void 0:s(i,r,o,l,d))&&void 0!==u?u:"";if(!/src\s*=\s*['"](file:[/][/]|jop[-a-zA-Z]+:[/][/])?[^'"]*[.]svg([?]t=\d+)?['"]/i.exec(null!=c?c:""))return c;const g=a.utils.escapeHtml(t.default.toString()),v=e=>JSON.stringify(a.utils.escapeHtml(e)),m=c.replace("<img ",`<img onload="(${g})(this.parentElement)" `);return["<span","\tclass='jsdraw--svgWrapper'",`\tdata-js-draw-edit-label=${v(n.default.edit)}`,`\tdata-js-draw-content-script-id=${v(e)}`,"\tcontentEditable='false'>",m,"</span>"].join("")}},assets:()=>[{name:"markdownIt.css"},{name:"markdownIt-content.js"}]})})(),exports.default=i.default})();