// Generated from csvAnalyze.g4 by ANTLR 4.8
// jshint ignore: start
var antlr4 = require('antlr4/index');

// This class defines a complete listener for a parse tree produced by csvAnalyzeParser.
function csvAnalyzeListener() {
	antlr4.tree.ParseTreeListener.call(this);
	return this;
}

csvAnalyzeListener.prototype = Object.create(antlr4.tree.ParseTreeListener.prototype);
csvAnalyzeListener.prototype.constructor = csvAnalyzeListener;

// Enter a parse tree produced by csvAnalyzeParser#csvFile.
csvAnalyzeListener.prototype.enterCsvFile = function(ctx) {
};

// Exit a parse tree produced by csvAnalyzeParser#csvFile.
csvAnalyzeListener.prototype.exitCsvFile = function(ctx) {
};


// Enter a parse tree produced by csvAnalyzeParser#row.
csvAnalyzeListener.prototype.enterRow = function(ctx) {
};

// Exit a parse tree produced by csvAnalyzeParser#row.
csvAnalyzeListener.prototype.exitRow = function(ctx) {
};


// Enter a parse tree produced by csvAnalyzeParser#field.
csvAnalyzeListener.prototype.enterField = function(ctx) {
};

// Exit a parse tree produced by csvAnalyzeParser#field.
csvAnalyzeListener.prototype.exitField = function(ctx) {
};


// Enter a parse tree produced by csvAnalyzeParser#text.
csvAnalyzeListener.prototype.enterText = function(ctx) {
};

// Exit a parse tree produced by csvAnalyzeParser#text.
csvAnalyzeListener.prototype.exitText = function(ctx) {
};


// Enter a parse tree produced by csvAnalyzeParser#string.
csvAnalyzeListener.prototype.enterString = function(ctx) {
};

// Exit a parse tree produced by csvAnalyzeParser#string.
csvAnalyzeListener.prototype.exitString = function(ctx) {
};


// Enter a parse tree produced by csvAnalyzeParser#delimiter.
csvAnalyzeListener.prototype.enterDelimiter = function(ctx) {
};

// Exit a parse tree produced by csvAnalyzeParser#delimiter.
csvAnalyzeListener.prototype.exitDelimiter = function(ctx) {
};


// Enter a parse tree produced by csvAnalyzeParser#tab.
csvAnalyzeListener.prototype.enterTab = function(ctx) {
};

// Exit a parse tree produced by csvAnalyzeParser#tab.
csvAnalyzeListener.prototype.exitTab = function(ctx) {
};


// Enter a parse tree produced by csvAnalyzeParser#semi.
csvAnalyzeListener.prototype.enterSemi = function(ctx) {
};

// Exit a parse tree produced by csvAnalyzeParser#semi.
csvAnalyzeListener.prototype.exitSemi = function(ctx) {
};


// Enter a parse tree produced by csvAnalyzeParser#comma.
csvAnalyzeListener.prototype.enterComma = function(ctx) {
};

// Exit a parse tree produced by csvAnalyzeParser#comma.
csvAnalyzeListener.prototype.exitComma = function(ctx) {
};



exports.csvAnalyzeListener = csvAnalyzeListener;