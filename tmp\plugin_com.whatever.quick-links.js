(()=>{"use strict";var t={632:(t,e)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.default=joplin},808:(t,e)=>{var i,o,n,l,r,d;Object.defineProperty(e,"__esModule",{value:!0}),e.ContentScriptType=e.SettingItemType=e.Toolbar<PERSON>onLocation=e.isContextMenuItemLocation=e.MenuItemLocation=e.ImportModuleOutputFormat=e.FileSystemItem=void 0,(d=e.FileSystemItem||(e.FileSystemItem={})).File="file",d.Directory="directory",(r=e.ImportModuleOutputFormat||(e.ImportModuleOutputFormat={})).Markdown="md",r.Html="html",function(t){t.File="file",t.Edit="edit",t.View="view",t.Note="note",t.Tools="tools",t.Help="help",t.Context="context",t.NoteListContextMenu="noteListContextMenu",t.EditorContextMenu="editorContextMenu",t.FolderContextMenu="folderContextMenu",t.TagContextMenu="tagContextMenu"}(i=e.MenuItemLocation||(e.MenuItemLocation={})),e.isContextMenuItemLocation=function(t){return[i.Context,i.NoteListContextMenu,i.EditorContextMenu,i.FolderContextMenu,i.TagContextMenu].includes(t)},(l=e.ToolbarButtonLocation||(e.ToolbarButtonLocation={})).NoteToolbar="noteToolbar",l.EditorToolbar="editorToolbar",(n=e.SettingItemType||(e.SettingItemType={}))[n.Int=1]="Int",n[n.String=2]="String",n[n.Bool=3]="Bool",n[n.Array=4]="Array",n[n.Object=5]="Object",n[n.Button=6]="Button",(o=e.ContentScriptType||(e.ContentScriptType={})).MarkdownItPlugin="markdownItPlugin",o.CodeMirrorPlugin="codeMirrorPlugin"},740:function(t,e,i){var o=this&&this.__awaiter||function(t,e,i,o){return new(i||(i=Promise))((function(n,l){function r(t){try{u(o.next(t))}catch(t){l(t)}}function d(t){try{u(o.throw(t))}catch(t){l(t)}}function u(t){var e;t.done?n(t.value):(e=t.value,e instanceof i?e:new i((function(t){t(e)}))).then(r,d)}u((o=o.apply(t,e||[])).next())}))};Object.defineProperty(e,"__esModule",{value:!0});const n=i(632),l=i(808),r=6e4,d="showFolders",u="allowNewNotes",s="selectText";let a=!1,c=!1,f=!1,p={};function y(){return o(this,void 0,void 0,(function*(){a=yield n.default.settings.value(d),a&&(yield v())}))}function m(){return o(this,void 0,void 0,(function*(){c=yield n.default.settings.value(u)}))}function g(){return o(this,void 0,void 0,(function*(){f=yield n.default.settings.value(s)}))}function v(){return o(this,void 0,void 0,(function*(){p=yield function(){return o(this,void 0,void 0,(function*(){let t={};const e={fields:["id","title"],page:1};let i=yield n.default.data.get(["folders"],e);for(i.items.forEach((e=>t[e.id]=e.title));i.has_more;)e.page+=1,i=yield n.default.data.get(["folders"],e),i.items.forEach((e=>t[e.id]=e.title));return t}))}(),setTimeout((()=>{a&&v()}),r)}))}n.default.plugins.register({onStart:function(){return o(this,void 0,void 0,(function*(){yield function(){return o(this,void 0,void 0,(function*(){const t="QuickLinks";yield n.default.settings.registerSection(t,{description:"Quick Links Plugin Settings",label:"Quick Links",iconName:"fas fa-link"}),yield n.default.settings.registerSettings({[d]:{public:!0,section:t,type:l.SettingItemType.Bool,value:a,label:"Show Notebooks"},[u]:{public:!0,section:t,type:l.SettingItemType.Bool,value:c,label:"Allow new notes"},[s]:{public:!0,section:t,type:l.SettingItemType.Bool,value:f,label:"Select link text after inserting"}}),yield y(),yield m(),yield m(),yield g(),yield n.default.settings.onChange((t=>{t.keys.indexOf(d)>=0&&y(),t.keys.indexOf(u)>=0&&m(),t.keys.indexOf(s)>=0&&g()}))}))}(),yield n.default.contentScripts.register(l.ContentScriptType.CodeMirrorPlugin,"quickLinks","./contentScript/index.js"),yield n.default.contentScripts.onMessage("quickLinks",(t=>o(this,void 0,void 0,(function*(){const e=(yield n.default.workspace.selectedNoteIds())[0];if("getNotes"===t.command){const i=t.prefix;let l=yield function(t){return o(this,void 0,void 0,(function*(){return""===t?(yield n.default.data.get(["notes"],{fields:["id","title","parent_id"],order_by:"updated_time",order_dir:"DESC",limit:21})).items:(yield n.default.data.get(["search"],{fields:["id","title","parent_id"],limit:21,query:`title:${t.trimRight()}*`})).items}))}(i);return{notes:l.filter((t=>t.id!==e)).map((t=>({id:t.id,title:t.title,folder:p[t.parent_id]}))),showFolders:a,allowNewNotes:c,selectText:f}}if("createNote"===t.command){const e=yield n.default.workspace.selectedNote(),i=yield n.default.data.get(["folders",e.parent_id]);return{newNote:yield n.default.data.post(["notes"],null,{is_todo:t.todo,title:t.title,parent_id:i.id})}}}))))}))}})}},e={};!function i(o){var n=e[o];if(void 0!==n)return n.exports;var l=e[o]={exports:{}};return t[o].call(l.exports,l,l.exports,i),l.exports}(740)})();