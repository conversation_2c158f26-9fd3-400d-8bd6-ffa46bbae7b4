{"msg": {"backup": {"completed": "Backup wurde erstellt"}, "error": {"PluginUpgrade": "Upgrade Fehler %s: %s", "folderCreation": "Fehler beim Ordner erstellen: %s", "ConfigureBackupPath": "<PERSON>te einen Backup Pfad in `<PERSON><PERSON><PERSON> Tools > Options > Backup` konfigurieren", "PasswordMissMatch": "Passwörter stimmen nicht überein!", "BackupPathDontExist": "Der Backup Pfad '%s' existiert nicht!", "BackupAlreadyRunning": "Es läuft bereits ein Backup!", "Backup": "Backup Fehler für %s: %s", "fileCopy": "Fehler beim kopieren von <PERSON>/Ordner in %s: %s", "deleteFile": "Fehler beim löschen von Datei/Ordner in %s: %s", "BackupSetNotSupportedChars": "Der Name des Backup-Sets enthält nicht zulässige Zeichen ( %s )!", "passwordDoubleQuotes": "Das Passwort enthält \" (Doppelte Anführungszeichen), diese sind wegen eines Bugs nicht erlaubt. Der Passwortschutz für die Backups wurde deaktivert!"}}, "settings": {"path": {"label": "Sicherungs Pfad", "description": "Speicherort für die Backups. Dieser Pfad ist exklusiv für die Joplin Backups wenn die Einstellungen für 'Erstellen eines Unterordners' deaktiviert wird, es dürfen sich dann keine anderen Daten darin befinden!"}, "exportPath": {"label": "Temporärer Export Pfad", "description": "Temporärer Pfad für den Datenexport aus Joplin, bevor die Daten in den %s verschoben werden"}, "backupRetention": {"label": "Behalte x Si<PERSON>ungen", "description": "Wie viele Sicherungen aufbewahrt werden sollen. Wenn mehr als eine Version konfiguriert ist, werden die Ordner im Sicherungspfad entsprechend der Einstellung 'Sicherungsset Namen' erstellt"}, "backupInterval": {"label": "Sicherungsinterval in Stunden", "description": "0 = Automatisches Sicherung ist deaktivert"}, "onlyOnChange": {"label": "Nur bei änderung", "description": "Erstellt eine Sicherung im angegebenen Sicherungsintervall nur dann, wenn es eine Änderung in den Notizen, Tags, Dateien oder Notizbücher gab"}, "usePassword": {"label": "Passwort geschütztes Sicherung", "description": "Die Sicherung wird mittels verschlüsseltem Archive geschützt"}, "password": {"label": "Passwort", "description": "Wenn ein Passwort eingegeben wurde, sind die Sicherungen mit einem Passwort geschützt"}, "passwordRepeat": {"label": "Passwort wiederholen", "description": "Wiederholen Sie das Passwort, um dieses zu bestätigen"}, "fileLogLevel": {"label": "Protokollierungsebene", "description": "Protokollierungsebene für die Backup Logdatei"}, "createSubfolder": {"label": "<PERSON>rstellen eines Unterordners", "description": "Erstellt einen Unterordner im konfigurierten %s. <PERSON><PERSON> de<PERSON>ti<PERSON>, wenn sich keine weiteren Daten im %s befinden!"}, "createSubfolderPerProfile": {"label": "Unterordner für Joplin profile", "description": "Erstellt einen Unterordner innerhalb des Sicherungsverzeichnisses für das aktuelle Profil. Dadurch können mehrere Profile derselben Joplin Installation dasselbe Sicherungsverzeichnis verwenden, ohne dass Sicherungen anderer Profile überschrieben werden. Alle Profile, die dasselbe Sicherungsverzeichnis verwenden, müssen diese Einstellung aktiviert haben"}, "zipArchive": {"label": "<PERSON><PERSON><PERSON> ein Archive", "description": "Backup Daten in einem Archiv speichern, wenn ein Passwortschutz für die Sicherung eingestellt ist wird immer ein Archiv erstellt"}, "compressionLevel": {"label": "ZIP Komprimierungsgrad", "description": "Komprimierungsgrad für das Archiv"}, "backupSetName": {"label": "Sicherungsset Namen", "description": "Name des Sicherungssatzes, wenn mehrere Sicherungen aufbewahrt werden sollen. Moment Token (https://momentjs.com/docs/#/displaying/format/) können mittels {TOKEN} verwendet werden"}, "backupPlugins": {"label": "<PERSON>lug<PERSON> sichern", "description": "Plugin jpl Dateien mit sichern (Es werden keine Plugin Einstellungen gesichert!)"}, "exportFormat": {"label": "Export Format", "description": "Joplin Datenexportformat während der Sicherung"}, "singleJex": {"label": "Eine JEX Datei", "description": "Erstellt nur eine JEX Datei (Empf<PERSON><PERSON>, um den Verlust interner Notizverknüpfungen oder der Ordnerstruktur bei einer Wiederherstellung zu vermeiden!)"}, "execFinishCmd": {"label": "Befehl nach der Sicherung", "description": "Befehl/Program nach der Sicherung ausführen"}}, "backupReadme": "# Jo<PERSON>lin Sicherung\n\nDieser Ordner enthält eine oder mehrere Sicherungen aus der Joplin Note Anwendung.\n\nSiehe [Backup documentation](https://joplinapp.org/plugins/plugin/io.github.jackgruber.backup/#restore) für Informationen wie eine Sicherung wieder hergestellt werden kann.", "command": {"createBackup": "<PERSON>up erstellen"}}