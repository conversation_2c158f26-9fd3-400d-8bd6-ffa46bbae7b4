{"manifest_version": 1, "id": "io.github.jackgruber.backup", "app_min_version": "2.1.3", "version": "1.4.3", "name": "Backup", "description": "Plugin to create manual and automatic backups.", "author": "<PERSON><PERSON><PERSON><PERSON>", "homepage_url": "https://github.com/JackGruber/joplin-plugin-backup/blob/master/README.md", "repository_url": "https://github.com/JackGruber/joplin-plugin-backup", "keywords": ["backup", "jex", "export", "zip", "7zip", "encrypted", "archive"], "categories": ["productivity", "files"], "screenshots": [{"src": "img/main.png", "label": "Screenshot: Showing the basic settings"}, {"src": "img/showcase1.png", "label": "Screenshot: Showing the advanced settings"}], "icons": {"256": "img/icon_256.png"}, "_package_hash": "efb7b014c11abe70246c7e75df60773a"}