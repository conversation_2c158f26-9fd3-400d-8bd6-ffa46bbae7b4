2025-07-19 04:16:44: App: Profile directory: C:/Users/<USER>/.config/joplin-desktop/profile-webdev
2025-07-19 04:16:44: App: Root profile directory: C:/Users/<USER>/.config/joplin-desktop
2025-07-19 04:16:44: Database was open successfully
2025-07-19 04:16:44: Checking for database schema update...
2025-07-19 04:16:44: Current database version {"version":47,"table_fields_version":47}
2025-07-19 04:16:44: Upgrading database from version 47
2025-07-19 04:16:44: New version: 47. Previously recorded version: 47
2025-07-19 04:16:44: mergeGlobalAndLocalSettings: Skipping non-built-in key: plugin-io.github.personalizedrefrigerator.snippets-and-autocomplete.snippets-note-id
2025-07-19 04:16:44: KeychainService: checking if keychain supported
2025-07-19 04:16:44: KeychainService: check was already done - skipping. Supported: 1
2025-07-19 04:16:44: e2ee/utils: Master password is not set - trying to get it from the active master key...
2025-07-19 04:16:44: handleSyncStartupOperation: Processing operation: 0
2025-07-19 04:16:44: App: Client ID: 00e4cf47cf354b8dbe658fdba017e89d
2025-07-19 04:16:44: models/Setting: Applying default migrations...
2025-07-19 04:16:44: e2ee/utils: Trying to load 0 master keys...
2025-07-19 04:16:44: e2ee/utils: Loaded master keys: 0
2025-07-19 04:16:44: ResourceFetcher: Auto-add resources: Mode: always
2025-07-19 04:16:44: permanentlyDeleteOldData: Processing items older than 7776000000ms...
2025-07-19 04:16:44: ResourceFetcher: Auto-added resources: 0
2025-07-19 04:16:44: permanentlyDeleteOldData: Items to permanently delete: {"folderIds":[],"noteIds":[]}
2025-07-19 04:16:44: checkDisabledSyncItemsNotification: No errors: Hiding notification
2025-07-19 04:16:44: App: "syncInfoCache" was changed - setting up encryption related code
2025-07-19 04:16:44: e2ee/utils: Trying to load 0 master keys...
2025-07-19 04:16:44: e2ee/utils: Loaded master keys: 0
2025-07-19 04:16:44: App: "syncInfoCache" was changed - setting up encryption related code
2025-07-19 04:16:44: e2ee/utils: Trying to load 0 master keys...
2025-07-19 04:16:44: e2ee/utils: Loaded master keys: 0
2025-07-19 04:16:44: App: "syncInfoCache" was changed - setting up encryption related code
2025-07-19 04:16:44: e2ee/utils: Trying to load 0 master keys...
2025-07-19 04:16:44: e2ee/utils: Loaded master keys: 0
2025-07-19 04:16:44: app.start: doing regular boot
2025-07-19 04:16:44: App: Setting up auto-updater service...
2025-07-19 04:16:44: App: Setting up auto-updater service...
2025-07-19 04:16:44: App: "syncInfoCache" was changed - setting up encryption related code
2025-07-19 04:16:44: e2ee/utils: Trying to load 0 master keys...
2025-07-19 04:16:44: e2ee/utils: Loaded master keys: 0
2025-07-19 04:16:44: App: "syncInfoCache" was changed - setting up encryption related code
2025-07-19 04:16:44: e2ee/utils: Trying to load 0 master keys...
2025-07-19 04:16:44: e2ee/utils: Loaded master keys: 0
2025-07-19 04:16:44: App: "syncInfoCache" was changed - setting up encryption related code
2025-07-19 04:16:44: e2ee/utils: Trying to load 0 master keys...
2025-07-19 04:16:44: e2ee/utils: Loaded master keys: 0
2025-07-19 04:16:44: RevisionService: runInBackground: Starting background service with revision collection interval 600000
2025-07-19 04:16:44: PluginService: Loading plugin from C:/Users/<USER>/.config/joplin-desktop/profile-webdev/cache/com.Plugin.ToGoogleSearch
2025-07-19 04:16:44: PluginService: Loading plugin from C:/Users/<USER>/.config/joplin-desktop/profile-webdev/cache/com.ckant.joplin-plugin-better-code-blocks
2025-07-19 04:16:44: PluginService: Loading plugin from C:/Users/<USER>/.config/joplin-desktop/profile-webdev/cache/com.coderrsid.pasteSpecial
2025-07-19 04:16:44: PluginService: Loading plugin from C:/Users/<USER>/.config/joplin-desktop/profile-webdev/cache/com.futuremotion.custom-styles
2025-07-19 04:16:44: PluginService: Loading plugin from C:/Users/<USER>/.config/joplin-desktop/profile-webdev/cache/com.rafjaf.headings4to6
2025-07-19 04:16:44: PluginService: Loading plugin from C:/Users/<USER>/.config/joplin-desktop/profile-webdev/cache/com.whatever.quick-links
2025-07-19 04:16:44: PluginService: Loading plugin from C:/Users/<USER>/.config/joplin-desktop/profile-webdev/cache/de.habelt.CsvImport
2025-07-19 04:16:44: joplin.plugins: Starting plugin: com.Plugin.ToGoogleSearch
2025-07-19 04:16:44: joplin.plugins: Finished running onStart handler: com.Plugin.ToGoogleSearch (Took 2ms)
2025-07-19 04:16:44: PluginService: Loading plugin from C:/Users/<USER>/.config/joplin-desktop/profile-webdev/cache/joplin.plugin.benji.favorites
2025-07-19 04:16:44: joplin.plugins: Starting plugin: com.ckant.joplin-plugin-better-code-blocks
2025-07-19 04:16:44: joplin.plugins: Finished running onStart handler: com.ckant.joplin-plugin-better-code-blocks (Took 19ms)
2025-07-19 04:16:44: joplin.plugins: Starting plugin: com.coderrsid.pasteSpecial
2025-07-19 04:16:44: joplin.plugins: [error] Uncaught exception in plugin "com.coderrsid.pasteSpecial": Error: Accelerator "Cmd+Shift+V" is not valid.
Error: Accelerator "Cmd+Shift+V" is not valid.
    at KeymapService.validateAccelerator (C:\Users\<USER>\AppData\Local\Programs\Joplin\resources\app.asar\node_modules\@joplin\lib\services\KeymapService.js:339:19)
    at KeymapService.registerCommandAccelerator (C:\Users\<USER>\AppData\Local\Programs\Joplin\resources\app.asar\node_modules\@joplin\lib\services\KeymapService.js:209:18)
    at JoplinViewsMenus.registerCommandAccelerators (C:\Users\<USER>\AppData\Local\Programs\Joplin\resources\app.asar\node_modules\@joplin\lib\services\plugins\api\JoplinViewsMenus.js:24:52)
    at JoplinViewsMenus.create (C:\Users\<USER>\AppData\Local\Programs\Joplin\resources\app.asar\node_modules\@joplin\lib\services\plugins\api\JoplinViewsMenus.js:49:14)
    at executeSandboxCall (C:\Users\<USER>\AppData\Local\Programs\Joplin\resources\app.asar\node_modules\@joplin\lib\services\plugins\utils\executeSandboxCall.js:45:15)
    at IpcRenderer.<anonymous> (C:\Users\<USER>\AppData\Local\Programs\Joplin\resources\app.asar\services\plugins\PluginRunner.js:155:69)
    at IpcRenderer.emit (node:events:530:35)
    at Object.onMessage (node:electron/js2c/renderer_init:2:10603)
2025-07-19 04:16:44: joplin.plugins: Finished running onStart handler: com.coderrsid.pasteSpecial (Took 3ms)
2025-07-19 04:16:44: PluginService: Loading plugin from C:/Users/<USER>/.config/joplin-desktop/profile-webdev/cache/joplin.plugin.note.tabs
2025-07-19 04:16:44: joplin.plugins: Starting plugin: com.futuremotion.custom-styles
2025-07-19 04:16:44: joplin.plugins: Finished running onStart handler: com.futuremotion.custom-styles (Took 6ms)
2025-07-19 04:16:44: PluginService: Loading plugin from C:/Users/<USER>/.config/joplin-desktop/profile-webdev/cache/joplin.plugin.quick.html.tags
2025-07-19 04:16:44: joplin.plugins: Starting plugin: com.rafjaf.headings4to6
2025-07-19 04:16:44: PluginService: Loading plugin from C:/Users/<USER>/.config/joplin-desktop/profile-webdev/cache/net.rmusin.joplin-table-formatter
2025-07-19 04:16:44: joplin.plugins: Finished running onStart handler: com.rafjaf.headings4to6 (Took 13ms)
2025-07-19 04:16:44: joplin.plugins: Starting plugin: com.whatever.quick-links
2025-07-19 04:16:44: joplin.plugins: Finished running onStart handler: com.whatever.quick-links (Took 10ms)
2025-07-19 04:16:44: PluginService: Loading plugin from C:/Users/<USER>/.config/joplin-desktop/profile-webdev/cache/io.github.jackgruber.backup
2025-07-19 04:16:44: joplin.plugins: Starting plugin: de.habelt.CsvImport
2025-07-19 04:16:44: joplin.plugins: Finished running onStart handler: de.habelt.CsvImport (Took 2ms)
2025-07-19 04:16:44: joplin.plugins: Starting plugin: joplin.plugin.benji.favorites
2025-07-19 04:16:45: joplin.plugins: Finished running onStart handler: joplin.plugin.benji.favorites (Took 47ms)
2025-07-19 04:16:45: joplin.plugins: Starting plugin: joplin.plugin.note.tabs
2025-07-19 04:16:45: joplin.plugins: Starting plugin: joplin.plugin.quick.html.tags
2025-07-19 04:16:45: joplin.plugins: Finished running onStart handler: joplin.plugin.quick.html.tags (Took 13ms)
2025-07-19 04:16:45: joplin.plugins: Finished running onStart handler: joplin.plugin.note.tabs (Took 56ms)
2025-07-19 04:16:45: joplin.plugins: Starting plugin: net.rmusin.joplin-table-formatter
2025-07-19 04:16:45: joplin.plugins: Finished running onStart handler: net.rmusin.joplin-table-formatter (Took 3ms)
2025-07-19 04:16:45: joplin.plugins: Starting plugin: io.github.jackgruber.backup
2025-07-19 04:16:45: KeychainServiceDriver.electron: [warn] Decryption of a setting failed. Corrupted data or new keychain password? Error: Error: Could not call remote method 'decryptString'. Check that the method signature is correct. Underlying error: Error: Error while decrypting the ciphertext provided to safeStorage.decryptString.Underlying stack: Error: Error while decrypting the ciphertext provided to safeStorage.decryptString.
    at Object.decryptString (C:\Users\<USER>\AppData\Local\Programs\Joplin\resources\app.asar\bridge.js:38:47)
    at C:\Users\<USER>\AppData\Local\Programs\Joplin\resources\app.asar\node_modules\@electron\remote\dist\src\main\server.js:480:71
    at IpcMainImpl.<anonymous> (C:\Users\<USER>\AppData\Local\Programs\Joplin\resources\app.asar\node_modules\@electron\remote\dist\src\main\server.js:323:27)
    at IpcMainImpl.emit (node:events:518:28)
    at IpcMainImpl.emit (node:domain:489:12)
    at WebContents.<anonymous> (node:electron/js2c/browser_init:2:89937)
    at WebContents.emit (node:events:518:28)
    at WebContents.emit (node:domain:489:12)

Error: Could not call remote method 'decryptString'. Check that the method signature is correct. Underlying error: Error: Error while decrypting the ciphertext provided to safeStorage.decryptString.Underlying stack: Error: Error while decrypting the ciphertext provided to safeStorage.decryptString.
    at Object.decryptString (C:\Users\<USER>\AppData\Local\Programs\Joplin\resources\app.asar\bridge.js:38:47)
    at C:\Users\<USER>\AppData\Local\Programs\Joplin\resources\app.asar\node_modules\@electron\remote\dist\src\main\server.js:480:71
    at IpcMainImpl.<anonymous> (C:\Users\<USER>\AppData\Local\Programs\Joplin\resources\app.asar\node_modules\@electron\remote\dist\src\main\server.js:323:27)
    at IpcMainImpl.emit (node:events:518:28)
    at IpcMainImpl.emit (node:domain:489:12)
    at WebContents.<anonymous> (node:electron/js2c/browser_init:2:89937)
    at WebContents.emit (node:events:518:28)
    at WebContents.emit (node:domain:489:12)

    at C:\Users\<USER>\AppData\Local\Programs\Joplin\resources\app.asar\node_modules\@electron\remote\dist\src\main\server.js:483:25
    at IpcMainImpl.<anonymous> (C:\Users\<USER>\AppData\Local\Programs\Joplin\resources\app.asar\node_modules\@electron\remote\dist\src\main\server.js:323:27)
    at IpcMainImpl.emit (node:events:518:28)
    at IpcMainImpl.emit (node:domain:489:12)
    at WebContents.<anonymous> (node:electron/js2c/browser_init:2:89937)
    at WebContents.emit (node:events:518:28)
    at WebContents.emit (node:domain:489:12)
2025-07-19 04:16:45: KeychainServiceDriver.electron: [warn] Decryption of a setting failed. Corrupted data or new keychain password? Error: Error: Could not call remote method 'decryptString'. Check that the method signature is correct. Underlying error: Error: Error while decrypting the ciphertext provided to safeStorage.decryptString.Underlying stack: Error: Error while decrypting the ciphertext provided to safeStorage.decryptString.
    at Object.decryptString (C:\Users\<USER>\AppData\Local\Programs\Joplin\resources\app.asar\bridge.js:38:47)
    at C:\Users\<USER>\AppData\Local\Programs\Joplin\resources\app.asar\node_modules\@electron\remote\dist\src\main\server.js:480:71
    at IpcMainImpl.<anonymous> (C:\Users\<USER>\AppData\Local\Programs\Joplin\resources\app.asar\node_modules\@electron\remote\dist\src\main\server.js:323:27)
    at IpcMainImpl.emit (node:events:518:28)
    at IpcMainImpl.emit (node:domain:489:12)
    at WebContents.<anonymous> (node:electron/js2c/browser_init:2:89937)
    at WebContents.emit (node:events:518:28)
    at WebContents.emit (node:domain:489:12)

Error: Could not call remote method 'decryptString'. Check that the method signature is correct. Underlying error: Error: Error while decrypting the ciphertext provided to safeStorage.decryptString.Underlying stack: Error: Error while decrypting the ciphertext provided to safeStorage.decryptString.
    at Object.decryptString (C:\Users\<USER>\AppData\Local\Programs\Joplin\resources\app.asar\bridge.js:38:47)
    at C:\Users\<USER>\AppData\Local\Programs\Joplin\resources\app.asar\node_modules\@electron\remote\dist\src\main\server.js:480:71
    at IpcMainImpl.<anonymous> (C:\Users\<USER>\AppData\Local\Programs\Joplin\resources\app.asar\node_modules\@electron\remote\dist\src\main\server.js:323:27)
    at IpcMainImpl.emit (node:events:518:28)
    at IpcMainImpl.emit (node:domain:489:12)
    at WebContents.<anonymous> (node:electron/js2c/browser_init:2:89937)
    at WebContents.emit (node:events:518:28)
    at WebContents.emit (node:domain:489:12)

    at C:\Users\<USER>\AppData\Local\Programs\Joplin\resources\app.asar\node_modules\@electron\remote\dist\src\main\server.js:483:25
    at IpcMainImpl.<anonymous> (C:\Users\<USER>\AppData\Local\Programs\Joplin\resources\app.asar\node_modules\@electron\remote\dist\src\main\server.js:323:27)
    at IpcMainImpl.emit (node:events:518:28)
    at IpcMainImpl.emit (node:domain:489:12)
    at WebContents.<anonymous> (node:electron/js2c/browser_init:2:89937)
    at WebContents.emit (node:events:518:28)
    at WebContents.emit (node:domain:489:12)
2025-07-19 04:16:45: joplin.plugins: Finished running onStart handler: io.github.jackgruber.backup (Took 47ms)
2025-07-19 04:16:45: PluginService: Loading plugin from C:/Users/<USER>/.config/joplin-desktop/profile-webdev/cache/io.github.personalizedrefrigerator.js-draw
2025-07-19 04:16:45: SpellCheckerServiceDriverNative: Set effective languages to ""
2025-07-19 04:16:45: DecryptionWorker: cannot start because no master key is currently loaded.
2025-07-19 04:16:45: Preparing scheduled sync
2025-07-19 04:16:45: Sync cancelled - no sync target is selected.
2025-07-19 04:16:45: Updating all notifications...
2025-07-19 04:16:45: Garbage collecting alarms...
2025-07-19 04:16:45: joplin.plugins: Starting plugin: io.github.personalizedrefrigerator.js-draw
2025-07-19 04:16:45: useFormNote: Sync has finished and note has never been changed - reloading it
2025-07-19 04:16:46: joplin.plugins: Finished running onStart handler: io.github.personalizedrefrigerator.js-draw (Took 290ms)
2025-07-19 04:16:46: EditorPluginHandler: emitUpdate: []
2025-07-19 04:16:46: EditorPluginHandler: emitActivationCheck: responses: {"activatedEditors":[]}
2025-07-19 04:16:46: KeychainServiceDriver.electron: [warn] Decryption of a setting failed. Corrupted data or new keychain password? Error: Error: Could not call remote method 'decryptString'. Check that the method signature is correct. Underlying error: Error: Error while decrypting the ciphertext provided to safeStorage.decryptString.Underlying stack: Error: Error while decrypting the ciphertext provided to safeStorage.decryptString.
    at Object.decryptString (C:\Users\<USER>\AppData\Local\Programs\Joplin\resources\app.asar\bridge.js:38:47)
    at C:\Users\<USER>\AppData\Local\Programs\Joplin\resources\app.asar\node_modules\@electron\remote\dist\src\main\server.js:480:71
    at IpcMainImpl.<anonymous> (C:\Users\<USER>\AppData\Local\Programs\Joplin\resources\app.asar\node_modules\@electron\remote\dist\src\main\server.js:323:27)
    at IpcMainImpl.emit (node:events:518:28)
    at IpcMainImpl.emit (node:domain:489:12)
    at WebContents.<anonymous> (node:electron/js2c/browser_init:2:89937)
    at WebContents.emit (node:events:518:28)
    at WebContents.emit (node:domain:489:12)

Error: Could not call remote method 'decryptString'. Check that the method signature is correct. Underlying error: Error: Error while decrypting the ciphertext provided to safeStorage.decryptString.Underlying stack: Error: Error while decrypting the ciphertext provided to safeStorage.decryptString.
    at Object.decryptString (C:\Users\<USER>\AppData\Local\Programs\Joplin\resources\app.asar\bridge.js:38:47)
    at C:\Users\<USER>\AppData\Local\Programs\Joplin\resources\app.asar\node_modules\@electron\remote\dist\src\main\server.js:480:71
    at IpcMainImpl.<anonymous> (C:\Users\<USER>\AppData\Local\Programs\Joplin\resources\app.asar\node_modules\@electron\remote\dist\src\main\server.js:323:27)
    at IpcMainImpl.emit (node:events:518:28)
    at IpcMainImpl.emit (node:domain:489:12)
    at WebContents.<anonymous> (node:electron/js2c/browser_init:2:89937)
    at WebContents.emit (node:events:518:28)
    at WebContents.emit (node:domain:489:12)

    at C:\Users\<USER>\AppData\Local\Programs\Joplin\resources\app.asar\node_modules\@electron\remote\dist\src\main\server.js:483:25
    at IpcMainImpl.<anonymous> (C:\Users\<USER>\AppData\Local\Programs\Joplin\resources\app.asar\node_modules\@electron\remote\dist\src\main\server.js:323:27)
    at IpcMainImpl.emit (node:events:518:28)
    at IpcMainImpl.emit (node:domain:489:12)
    at WebContents.<anonymous> (node:electron/js2c/browser_init:2:89937)
    at WebContents.emit (node:events:518:28)
    at WebContents.emit (node:domain:489:12)
2025-07-19 04:16:46: KeychainServiceDriver.electron: [warn] Decryption of a setting failed. Corrupted data or new keychain password? Error: Error: Could not call remote method 'decryptString'. Check that the method signature is correct. Underlying error: Error: Error while decrypting the ciphertext provided to safeStorage.decryptString.Underlying stack: Error: Error while decrypting the ciphertext provided to safeStorage.decryptString.
    at Object.decryptString (C:\Users\<USER>\AppData\Local\Programs\Joplin\resources\app.asar\bridge.js:38:47)
    at C:\Users\<USER>\AppData\Local\Programs\Joplin\resources\app.asar\node_modules\@electron\remote\dist\src\main\server.js:480:71
    at IpcMainImpl.<anonymous> (C:\Users\<USER>\AppData\Local\Programs\Joplin\resources\app.asar\node_modules\@electron\remote\dist\src\main\server.js:323:27)
    at IpcMainImpl.emit (node:events:518:28)
    at IpcMainImpl.emit (node:domain:489:12)
    at WebContents.<anonymous> (node:electron/js2c/browser_init:2:89937)
    at WebContents.emit (node:events:518:28)
    at WebContents.emit (node:domain:489:12)

Error: Could not call remote method 'decryptString'. Check that the method signature is correct. Underlying error: Error: Error while decrypting the ciphertext provided to safeStorage.decryptString.Underlying stack: Error: Error while decrypting the ciphertext provided to safeStorage.decryptString.
    at Object.decryptString (C:\Users\<USER>\AppData\Local\Programs\Joplin\resources\app.asar\bridge.js:38:47)
    at C:\Users\<USER>\AppData\Local\Programs\Joplin\resources\app.asar\node_modules\@electron\remote\dist\src\main\server.js:480:71
    at IpcMainImpl.<anonymous> (C:\Users\<USER>\AppData\Local\Programs\Joplin\resources\app.asar\node_modules\@electron\remote\dist\src\main\server.js:323:27)
    at IpcMainImpl.emit (node:events:518:28)
    at IpcMainImpl.emit (node:domain:489:12)
    at WebContents.<anonymous> (node:electron/js2c/browser_init:2:89937)
    at WebContents.emit (node:events:518:28)
    at WebContents.emit (node:domain:489:12)

    at C:\Users\<USER>\AppData\Local\Programs\Joplin\resources\app.asar\node_modules\@electron\remote\dist\src\main\server.js:483:25
    at IpcMainImpl.<anonymous> (C:\Users\<USER>\AppData\Local\Programs\Joplin\resources\app.asar\node_modules\@electron\remote\dist\src\main\server.js:323:27)
    at IpcMainImpl.emit (node:events:518:28)
    at IpcMainImpl.emit (node:domain:489:12)
    at WebContents.<anonymous> (node:electron/js2c/browser_init:2:89937)
    at WebContents.emit (node:events:518:28)
    at WebContents.emit (node:domain:489:12)
2025-07-19 04:16:46: DecryptionWorker: cannot start because no master key is currently loaded.
2025-07-19 04:16:48: RevisionService: maintenance: Starting...
2025-07-19 04:16:48: RevisionService: maintenance: Service is enabled
2025-07-19 04:16:48: RevisionService: collectRevisions: Created revisions for 0 notes
2025-07-19 04:16:48: RevisionService: maintenance: Done in 102ms
2025-07-19 04:16:49: EditorPluginHandler: emitUpdate: []
2025-07-19 04:16:49: EditorPluginHandler: emitActivationCheck: responses: {"activatedEditors":[]}
2025-07-19 04:16:50: AutoUpdaterService: Checking for update
2025-07-19 04:16:50: AutoUpdaterService: Checking for update...
2025-07-19 04:16:50: AutoUpdaterService: Update for version 3.4.1 is not available (latest version: 3.4.1, downgrade is disallowed).
2025-07-19 04:16:50: AutoUpdaterService: Update not available.
2025-07-19 04:16:53: EditorPluginHandler: emitUpdate: []
2025-07-19 04:16:53: EditorPluginHandler: emitActivationCheck: responses: {"activatedEditors":[]}
2025-07-19 04:16:54: SearchEngine: Updating FTS table...
2025-07-19 04:16:54: Updating items_normalized from {"updated_time":0,"id":""}
2025-07-19 04:16:54: SearchEngine: Updated FTS table in 1ms. Inserted: 0. Deleted: 0
2025-07-19 04:16:57: DeleteAction: emptyTrash/folders: folder title: "Frameworks and Deployments"; Item IDs: ["64a0c6feb3f349239fd44d42fff668b6"]
2025-07-19 04:16:57: DeleteAction: emptyTrash/folders: folder title: "── WEB DEVELOPMENT ── "; Item IDs: ["ab3acc46c2b94dc18d8ad6a990e0e271"]
2025-07-19 04:17:07: SearchEngine: Updating FTS table...
2025-07-19 04:17:07: Updating items_normalized from {"updated_time":0,"id":""}
2025-07-19 04:17:07: SearchEngine: Updated FTS table in 0ms. Inserted: 0. Deleted: 0
2025-07-19 04:17:12: Preparing scheduled sync
2025-07-19 04:17:12: Sync cancelled - no sync target is selected.
2025-07-19 04:17:14: ResourceService::indexNoteResources: Start
2025-07-19 04:17:14: ResourceService::indexNoteResources: Completed
2025-07-19 04:17:14: ResourceService::deleteOrphanResources: []
2025-07-19 04:17:24: SearchEngine: Updating FTS table...
2025-07-19 04:17:24: Updating items_normalized from {"updated_time":0,"id":""}
2025-07-19 04:17:24: SearchEngine: Updated FTS table in 1ms. Inserted: 0. Deleted: 0
2025-07-19 04:26:16: SearchEngine: Updating FTS table...
2025-07-19 04:26:16: Updating items_normalized from {"updated_time":0,"id":""}
2025-07-19 04:26:16: SearchEngine: Updated FTS table in 22ms. Inserted: 1. Deleted: 0
2025-07-19 04:26:30: Preparing scheduled sync
2025-07-19 04:26:30: Sync cancelled - no sync target is selected.
2025-07-19 04:26:44: RevisionService: maintenance: Starting...
2025-07-19 04:26:44: RevisionService: maintenance: Service is enabled
2025-07-19 04:26:44: RevisionService: collectRevisions: Created revisions for 1 notes
2025-07-19 04:26:44: RevisionService: maintenance: Done in 131ms
2025-07-19 04:30:11: SearchEngine: Updating FTS table...
2025-07-19 04:30:11: Updating items_normalized from {"updated_time":0,"id":""}
2025-07-19 04:30:11: SearchEngine: Updated FTS table in 20ms. Inserted: 1. Deleted: 0
2025-07-19 04:30:29: SearchEngine: Updating FTS table...
2025-07-19 04:30:29: Updating items_normalized from {"updated_time":0,"id":""}
2025-07-19 04:30:29: SearchEngine: Updated FTS table in 20ms. Inserted: 1. Deleted: 0
2025-07-19 04:30:34: Preparing scheduled sync
2025-07-19 04:30:34: Sync cancelled - no sync target is selected.
2025-07-19 04:35:17: focusHandler: [warn] Tried action "focus" on an undefined element: Dialog::titleInputRef
2025-07-19 04:35:29: SearchEngine: Updating FTS table...
2025-07-19 04:35:29: Updating items_normalized from {"updated_time":0,"id":""}
2025-07-19 04:35:29: SearchEngine: Updated FTS table in 1ms. Inserted: 0. Deleted: 0
2025-07-19 04:35:31: EditorPluginHandler: emitUpdate: []
2025-07-19 04:35:31: EditorPluginHandler: emitActivationCheck: responses: {"activatedEditors":[]}
2025-07-19 04:35:32: useFormNote: Sync has finished and note has never been changed - reloading it
2025-07-19 04:35:41: SearchEngine: Updating FTS table...
2025-07-19 04:35:42: Updating items_normalized from {"updated_time":0,"id":""}
2025-07-19 04:35:42: SearchEngine: Updated FTS table in 21ms. Inserted: 1. Deleted: 0
2025-07-19 04:35:52: Preparing scheduled sync
2025-07-19 04:35:52: Sync cancelled - no sync target is selected.
2025-07-19 04:36:44: RevisionService: maintenance: Starting...
2025-07-19 04:36:44: RevisionService: maintenance: Service is enabled
2025-07-19 04:36:44: RevisionService: collectRevisions: Created revisions for 2 notes
2025-07-19 04:36:44: RevisionService: maintenance: Done in 131ms
2025-07-19 04:46:44: Running background sync on timer...
2025-07-19 04:46:44: Preparing scheduled sync
2025-07-19 04:46:44: Sync cancelled - no sync target is selected.
2025-07-19 04:46:44: RevisionService: maintenance: Starting...
2025-07-19 04:46:44: RevisionService: maintenance: Service is enabled
2025-07-19 04:46:44: RevisionService: collectRevisions: Created revisions for 0 notes
2025-07-19 04:46:44: RevisionService: maintenance: Done in 110ms
2025-07-19 04:56:44: RevisionService: maintenance: Starting...
2025-07-19 04:56:44: RevisionService: maintenance: Service is enabled
2025-07-19 04:56:44: RevisionService: collectRevisions: Created revisions for 0 notes
2025-07-19 04:56:44: RevisionService: maintenance: Done in 108ms
2025-07-19 05:06:44: RevisionService: maintenance: Starting...
2025-07-19 05:06:44: RevisionService: maintenance: Service is enabled
2025-07-19 05:06:44: RevisionService: collectRevisions: Created revisions for 0 notes
2025-07-19 05:06:44: RevisionService: maintenance: Done in 111ms
2025-07-19 05:16:44: Garbage collecting alarms...
2025-07-19 05:16:44: Running background sync on timer...
2025-07-19 05:16:44: Preparing scheduled sync
2025-07-19 05:16:44: Sync cancelled - no sync target is selected.
2025-07-19 05:16:44: RevisionService: maintenance: Starting...
2025-07-19 05:16:44: RevisionService: maintenance: Service is enabled
2025-07-19 05:16:44: RevisionService: collectRevisions: Created revisions for 0 notes
2025-07-19 05:16:44: RevisionService: maintenance: Done in 109ms
2025-07-19 05:26:44: RevisionService: maintenance: Starting...
2025-07-19 05:26:44: RevisionService: maintenance: Service is enabled
2025-07-19 05:26:44: RevisionService: collectRevisions: Created revisions for 0 notes
2025-07-19 05:26:44: RevisionService: maintenance: Done in 113ms
2025-07-19 05:28:17: EditorPluginHandler: emitUpdate: []
2025-07-19 05:28:17: EditorPluginHandler: emitActivationCheck: responses: {"activatedEditors":[]}
2025-07-19 05:28:32: SearchEngine: Updating FTS table...
2025-07-19 05:28:32: Updating items_normalized from {"updated_time":0,"id":""}
2025-07-19 05:28:32: SearchEngine: Updated FTS table in 19ms. Inserted: 1. Deleted: 0
2025-07-19 05:28:46: Preparing scheduled sync
2025-07-19 05:28:46: Sync cancelled - no sync target is selected.
2025-07-19 05:29:01: SearchEngine: Updating FTS table...
2025-07-19 05:29:01: Updating items_normalized from {"updated_time":0,"id":""}
2025-07-19 05:29:01: SearchEngine: Updated FTS table in 21ms. Inserted: 1. Deleted: 0
2025-07-19 05:29:12: SearchEngine: Updating FTS table...
2025-07-19 05:29:12: Updating items_normalized from {"updated_time":0,"id":""}
2025-07-19 05:29:12: SearchEngine: Updated FTS table in 20ms. Inserted: 1. Deleted: 0
2025-07-19 05:29:19: Preparing scheduled sync
2025-07-19 05:29:19: Sync cancelled - no sync target is selected.
