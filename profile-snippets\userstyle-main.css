@import url("../userstyle-fontface.css");
@import url("../userstyle-syntax.css");
@import url("../userstyle-print.css");
@import url("../userstyle-futuremotion.css");

:root {

   --default-body-max-width-print: 1100px;
   --default-body-max-width: 1340px;
   /* MAIN ///////////////////////////////////////////////////////////////////////*/
   /*/////////////////////////////////////////////////////////////////////////////*/

   --default-text-color: #202122;
   --default-font-size: 15.6px;
   --default-font-size-table: 15.5px;
   --default-font-size-table-inline-code: 13.5px;
   --default-font-size-table-codeblock: 14.2px;
   --default-p-line-height: 1.56rem;
   --default-line-height: 1.5rem;
   --default-line-height-caption: 1.3125rem;
   --default-p-margin-bottom: 1rem;
   --default-p-margin: 8px 0 14px 0;
   --default-body-margin-left: 0;
   --default-body-margin-right: 0;
   --default-font-family: var(--font-stack-roboto);
   --link-color-primary-blue-dark: #214ec1;
   --link-color-primary-blue: #2D6DE2;
   --link-color-primary-visited: #842de2;

   /* TABLE STYLES ///////////////////////////////////////////////////////////////*/
   /*/////////////////////////////////////////////////////////////////////////////*/

   --table-color-border-default: #CCCCCC;
   --table-color-border-default-head: #b9b9b9;
   --table-color-text-header: #2a2a2a;
   --table-color-text-default: #2a2a2a;
   --table-color-bg-row-header: #f9f9f9;
   --table-color-row-non-header: #FFFFFF;
   --table-font-size-default: 15.8px;
   --table-margin-top: 20px;
   --table-margin-bot: 20px;
   --table-row-height: 20px;


   /* HR STYLES //////////////////////////////////////////////////////////////////////////////////*/
   /*/////////////////////////////////////////////////////////////////////////////////////////////*/

   --horizontal-line-color: #c9ced5;
   --horizontal-line-border-bottom: 1px solid var(--horizontal-line-color);
   --horizontal-line-margin: 18px 0 18px 0;
   --horizontal-line-table-border-width: 2px;
   --horizontal-line-table-margin: 7.6px 0rem 9.2px 0rem;

   /* CODE STYLES ////////////////////////////////////////////////////////////////*/
   /*/////////////////////////////////////////////////////////////////////////////*/

   --inlinecode-color-default: #4e5256;
   --inlinecode-color-strong: #4d5256;
   --inlinecode-color-hover: #333538;
   --inlinecode-color-border: #d6d6d6;
   --inlinecode-color-border-hover: #d9d9d9;
   --inlinecode-color-background: #FBFBFB;
   --inlinecode-color-background-hover: #fefdfd;
   --inlinecode-padding: 4px 6px 4px 6px;
   --inlinecode-margin: 2px 2px;
   --inlinecode-border: 1px solid var(--inlinecode-color-border);
   --inlinecode-border-hover: 1px solid var(--inlinecode-color-border-hover);
   --inlinecode-border-2px: 2px solid var(--inlinecode-color-border);
   --inlinecode-border-2px-hover: 2px solid var(--inlinecode-color-border-hover);
   --inlinecode-border-radius-default: 4.6px;
   --inlinecode-border-radius-large: 5px;
   --inlinecode-letter-spacing: .05px;
   --inlinecode-font-size: 13px;
   --inlinecode-font-weight-strong: 500;
   --inlinecode-font-family: var(--font-stack-inputmono);
   --inlinecode-table-border-radius: 3.4px;
   --inlinecode-table-padding: 0.2em 0.4em 0.2em 0.4em;
   --inlinecode-table-font-size: 13.16px;
   --inlinecode-table-margin-rightleft: 2.4px;
   --inlinecode-table-letter-spacing: .1px;
   --codeblock-border-color: #d4d5d7;
   --codeblock-background-color: #fafafa6e;
   --codeblock-padding: .7025rem;
   --codeblock-border-radius: .3rem;
   --codeblock-line-height: 1.32rem;

   /* MARK STYLES ////////////////////////////////////////////////////////////////////////////////*/
   /*/////////////////////////////////////////////////////////////////////////////////////////////*/

   --mark-table-background-color: #f1f3f5;
   --mark-table-color: #5b646f;
   --mark-table-padding: 1.76px 4.8px 3.84px 4.8px;
   --mark-table-border-radius: 4.6px;
   --mark-table-font-size: 13px;
   --mark-table-line-height: 24.48px;
   --mark-table-border: none;
   --mark-table-margin-top: 10px;
   --mark-default-background-color: #ecedef;
   --mark-default-color: #495059;
   --mark-default-padding: 0px 6px;
   --mark-default-font-size: 15px;
   --mark-default-border-radius: 3px;
   --mark-default-margin: 0 2px;
   --mark-default-letter-spacing: .3px;

   /* HEADER STYLES //////////////////////////////////////////////////////////////*/
   /*/////////////////////////////////////////////////////////////////////////////*/

   --header-padding: 0 0 4px 0;
   --header-color: #1a1b1e;
   --header-code-color: #333538;
   --header-code-color-bg: #F9F9F9;
   --header-code-color-border: #d1d3d6;
   --header-border-bottom: 1px solid #d0d2d8;
   --header-first-child-margin-top: 8px;

   --h1-font-weight: 600;
   --h2-font-weight: 500;
   --h3-font-weight: 500;
   --h4-font-weight: 500;
   --h5-font-weight: 500;
   --h6-font-weight: 600;

   --h1-font-size: 2.14rem;
   --h2-font-size: 1.60rem;
   --h3-font-size: 1.43rem;
   --h4-font-size: 1.26rem;
   --h5-font-size: 1.18rem;
   --h6-font-size: 1.00rem;

   --h1-letter-spacing: -.3px;
   --h1-margin-default: 20px 0px 12px 0px;
   --h1-padding-default: 0px 0px 4px 0px;
   --h6-line-height: 33.04px;
   --h6-link-icon-url: url(../svg/external-link-fm-thick-4-normal.svg);

   /* HEADER INLINE CODE STYLES //////////////////////////////////////////////////////////////////*/
   /*/////////////////////////////////////////////////////////////////////////////////////////////*/

   --header-code-border-color: #d1d2d5;
   --header-code-color: #656970;
   --header-code-font-family: var(--font-stack-inputmono);
   --header-code-font-weight: 400;
   --header-code-border-radius: 5px;
   --header-code-margin: 2px;
   --header-code-h1-padding: 4px 6px 5px 6px;
   --header-code-h1-margin: 2px;
   --header-code-h1-font-size: 18px;
   --header-code-h1-color: #6a6f73;
   --header-code-h1-letter-spacing: -.1px;
   --header-code-h1-border: 3px solid var(--header-code-border-color);
   --header-code-h1-top-adjustment: -2px;
   --header-code-h2-padding: 3px 6px 3px 5px;
   --header-code-h2-margin: 2px;
   --header-code-h2-font-size: 14px;
   --header-code-h2-color: #4b4c4e;
   --header-code-h2-font-weight: 400;
   --header-code-h2-letter-spacing: .2px;
   --header-code-h2-border: 2px solid var(--header-code-border-color);
   --header-code-h2-top-adjustment: -1px;
   --header-code-h3-padding: 4px 5px 3px 5px;
   --header-code-h3-margin: 2px;
   --header-code-h3-font-size: 13px;
   --header-code-h3-color: #4b4c4e;
   --header-code-h3-font-weight: 400;
   --header-code-h3-letter-spacing: .6px;
   --header-code-h3-border: 2px solid var(--header-code-border-color);
   --header-code-h3-top-adjustment: -1px;
   --header-code-h4-padding: 4px 7px 4px 7px;
   --header-code-h4-margin: 2px;
   --header-code-h4-color: #4b4c4e;
   --header-code-h4-border-color: #d2d2d2;
   --header-code-h4-font-size: 12px;
   --header-code-h4-font-weight: 400;
   --header-code-h4-letter-spacing: .6px;
   --header-code-h4-border: 2px solid var(--header-code-border-color);
   --header-code-h4-top-adjustment: -1px;
   --header-code-h5-padding: 3.4px 6px 3.4px 6px;
   --header-code-h5-margin: 2px;
   --header-code-h5-color: #4b4c4e;
   --header-code-h5-border-color: #d2d2d2;
   --header-code-h5-font-size: 12px;
   --header-code-h5-font-weight: 400;
   --header-code-h5-letter-spacing: .3px;
   --header-code-h5-border: 2px solid var(--header-code-border-color);
   --header-code-h5-top-adjustment: -1px;

   /* LIST STYLES ////////////////////////////////////////////////////////////////*/
   /*/////////////////////////////////////////////////////////////////////////////*/

   --list-ol-padding-left: .1rem;
   --list-ul-marker-color: #71767e;

   /* MARK STYLES ////////////////////////////////////////////////////////////////*/
   /*/////////////////////////////////////////////////////////////////////////////*/

   --mark-table-color: #3d3428;
   --mark-table-color-bg: #f8f9c1;
   --mark-table-padding: .4375rem .375rem .3125rem .375rem;
   --mark-table-border-radius: 0.1875rem;

   /* IMAGE STYLES ///////////////////////////////////////////////////////////////*/
   /*/////////////////////////////////////////////////////////////////////////////*/

   --image-margin-bottom: 14.4px;
   --image-margin-top: 14.4px;

   /* FONT STACKS ////////////////////////////////////////////////////////////////*/
   /*/////////////////////////////////////////////////////////////////////////////*/

   --font-stack-neuehaas-display: "Neue Haas Grotesk Display Pro", "Haas Grot Disp", "Helvetica Now Text", "SF Pro Text", sans-serif;
   --font-stack-neuehaas-text: "Haas Grot Text", "Helvetica Now Display", "SF Pro Display", sans-serif;
   --font-stack-helvnow: "Helvetica Now Text", Helvetica, "Helvetica LT Pro", "SF Pro Text", Arial, sans-serif;
   --font-stack-helvnowdisplay: "Helvetica Now Display", "SF Pro Display", "Haas Grot Disp", "Helvetica Neue LT Pro", "Helvetica LT Pro", sans-serif;
   --font-stack-helvnowdisplay-med: "Helvetica Now Display Medium", "SF Pro Display Medium", "SF Pro Display", "Helvetica Neue LT Pro", "Helvetica LT Pro", sans-serif;
   --font-stack-helvnowmed: "Helvetica Now Text Medium", "Helvetica Now Text", Helvetica, "Helvetica LT Pro", "SF Pro Text", Arial, sans-serif;
   --font-stack-inter: Inter, Roboto, "Helvetica Now Text", Helvetica, Arial, sans-serif;
   --font-stack-roboto: Roboto, "Roboto Flex", "Helvetica LT Pro", Helvetica, "SF Pro Text", Arial, sans-serif;
   --font-stack-helv: "Helvetica", "Helvetica LT Pro", "SF Pro Text", "Arial", sans-serif;
   --font-stack-sfpro: "SF Pro Display", "Helvetica Now Display", "Helvetica LT Pro", "Arial", sans-serif;
   --font-stack-sfprotext: "SF Pro Text", "Helvetica Now Text", "Helvetica LT Pro", "Arial", sans-serif;
   --font-stack-robotomono: "Roboto Mono", "SF Mono", "Input Mono", Input, monospace;
   --font-stack-inputmono: "Input Mono", Input, "Roboto Mono", "SF Mono", monospace;
   --font-stack-inputmonomedium: "Input Mono Medium", "Input Mono", Input, "Roboto Mono", "SF Mono", monospace;
   --font-stack-sfmono: "SF Mono", "Input Mono", Input, "Roboto Mono", "SF Mono", monospace;
}

/* DOCUMENT WIDTH MASTER CONTROL /////////////////////////////////////////////////////////////////*/
/*////////////////////////////////////////////////////////////////////////////////////////////////*/
/*////////////////////////////////////////////////////////////////////////////////////////////////*/

/* @font-face {
   font-family: "Roboto Mono VF";
   src: url("https://mdn.github.io/shared-assets/fonts/variable-fonts/AmstelvarAlpha-VF.woff2") format("woff2-variations");
   font-weight: 300 900;
   font-stretch: 35% 100%;
   font-style: normal;
   font-display: swap;
 } */


/* SCREEN WIDTH //////////////////////////////////////////////////////////////////////////////////*/

.mce-content-body,
.mce-content-body #rendered-md {
   width: var(--default-body-max-width) !important;
   max-width: var(--default-body-max-width) !important;
   margin-left: 5px !important;
   padding-left: 5px !important;

}

/* PRINT WIDTH ///////////////////////////////////////////////////////////////////////////////////*/

html {
   div.exported-note {
      width: var(--default-body-max-width-print) !important;
      max-width: var(--default-body-max-width-print) !important;
   }
}

/* MAIN PRINT AND SCREEN STYLES //////////////////////////////////////////////////////////////////*/
/*////////////////////////////////////////////////////////////////////////////////////////////////*/
/*////////////////////////////////////////////////////////////////////////////////////////////////*/

div#rendered-md {
   box-sizing: border-box !important;
   margin: 0 !important;
   padding: 0 !important;
   max-width: 100% !important;
}

html div.exported-note .jop-tinymce table td,
html div.exported-note .jop-tinymce table th,
.jop-tinymce table td,
.jop-tinymce table th,
table td, th {
   border: 1px solid var(--table-color-border-default) !important;
   padding-right: 12px !important;
   padding-left: 12px !important;
   color: #32373F;
   font-size: 15;
   font-size: var(--default-font-size-table) !important;
   font-family: var(--font-stack-helvnow) !important;
   text-align: left;
}

html .mce-content-body #rendered-md,
html .mce-content-body,
html div.exported-note #rendered-md,
html div.exported-note,
html body,
html #rendered-md,
#rendered-md {

   /* DEFAULT BASIC STYLES //////////////////////////////////////////////////////////////////////*/

   margin: 0 !important;
   font-family: var(--font-stack-roboto) !important;
   word-wrap: break-word !important;
   box-sizing: border-box !important;
   color: var(--default-text-color) !important;
   font-weight: 400 !important;
   font-size: var(--default-font-size) !important;
   line-height: var(--default-line-height) !important;

   ul li, ol li {
      code {
         display: inline !important;
         border: var(--inlinecode-border) !important;
         background-color: var(--inlinecode-color-background) !important;
         padding: 2px 4px !important;
         color: var(--inlinecode-color-default) !important;
         font-size: var(--inlinecode-font-size) !important;
         line-height: normal !important;
         font-family: var(--font-stack-inputmono) !important;
         letter-spacing: var(--inlinecode-letter-spacing) !important;
         border-radius: 3px !important;
         position: relative;
         top: -1px;
      }
   }

   p {
      margin: var(--default-p-margin) !important;
      font-weight: 400 !important;
      line-height: var(--default-p-line-height) !important;
      unicode-bidi: plaintext;
      letter-spacing: .15px;

      strong {
         font-weight: 500 !important;
      }
      code:not(.notebox-container *) {
         display: inline;
         border: var(--inlinecode-border);
         border-radius: 3.5px;
         background-color: var(--inlinecode-color-background);
         padding: 2px 4px !important;
         color: var(--inlinecode-color-default);
         font-size: var(--inlinecode-font-size);
         line-height: normal;
         font-family: var(--font-stack-inputmono);
         letter-spacing: var(--inlinecode-letter-spacing);
         position: relative;
         top: -1px;
      }
   }

   a {
      .inline-code:not(.notebox-container *) {
         color: var(--link-color-primary-blue) !important;
         font-family: var(--inlinecode-font-family) !important;
      }
   }

   strong {
      font-weight: 600 !important;
      font-weight: 500 !important;
      letter-spacing: -.2px !important;
      letter-spacing: .1px !important;

      code.inline-code {
         color: var(--inlinecode-color-default) !important;
         font-weight: var(--inlinecode-font-weight-strong) !important;
         font-size: var(--inlinecode-font-size) !important;
         font-family: var(--inlinecode-font-family) !important;
      }
   }

   blockquote {
      opacity: .8 !important;
      margin: unset !important;
      margin-left: 8px !important;
      border-left: 3px solid #CCCCCC !important;
      padding-left: 1rem !important;
      color: #626161 !important;
   }

   hr {
      margin: var(--horizontal-line-margin) !important;
      border-bottom: var(--horizontal-line-border-bottom) !important;
      /* width: var(--default-body-max-width) !important; */
   }

   a {
      background-color: transparent;
      color: var(--link-color-primary-blue);
      text-decoration: none !important;

      &:hover {
         cursor: pointer !important;
         color: var(--link-color-primary-blue-dark);
      }

      &:visited {
         cursor: pointer !important;
         color: var(--link-color-primary-visited);
      }
   }

   a[href^="https://youtu"] {
      img {
         width: var(--default-body-max-width) !important;
      }
   }

   img {
      margin-top: var(--image-margin-top) !important;

      margin-bottom: var(--image-margin-bottom) !important;
      max-width: 100% !important;

      &:has(+h1) {
         margin-bottom: 0 !important;
      }

      &:has(+h2) {
         margin-bottom: 0 !important;
      }

      &:has(+h3) {
         margin-bottom: 0 !important;
      }

      &:has(+h4) {
         margin-bottom: 0 !important;
      }

      &:has(+h5) {
         margin-bottom: 0 !important;
      }
   }

   mark,
   body mark {
      display: inline-flex;
      position: relative;
      vertical-align: middle !important;
      border-radius: var(--mark-default-border-radius) !important;
      background-color: #f2e9ff !important;
      padding: var(--mark-default-padding);
      color: #5852b1 !important;
      font-size: var(--mark-default-font-size) !important;
      letter-spacing: var(--mark-default-letter-spacing) !important;
      height: 21px !important;
      align-items: center;
      top: -1px !important;
   }

   button.copy-code-blocks-button {
      right: 14px !important;
      top: 6px !important;
      display: inline-flex !important;
      justify-content: flex-end;

      svg {
         stroke: #c2c7cf !important
      }

      &.copied-code-blocks {
         svg {
            stroke: #d2d7df !important;
         }
      }

   }

   div.joplin-editable {
      width: var(--default-body-max-width) !important;
      top: unset !important;
      position: relative !important;
      margin-bottom: 18px;
      margin-top: 14px;
   }


   /* LIST STYLES ///////////////////////////////////////////////////////////////////////////////*/

   ul {

      &:not(.joplin-checklist) {

         position: relative;

         li::before {
            display: inline-flex;
            position: relative;
            scale: 100%;
            filter: invert(29%) sepia(10%) saturate(704%) hue-rotate(173deg) brightness(74%) contrast(78%);
            margin-right: 12px;
            margin-left: -21px !important;
            background-image: url(../svg/bullet_new_3.svg);
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' x='4px' y='4px' width='8px' height='8px' viewBox='0 0 8 8' xml:space='preserve'%3E%3Ccircle cx='4' cy='4' r='4' /%3E%3C/svg%3E");
            background-size: 6px 6px;
            background-repeat: no-repeat;
            width: 7px;
            height: 0.55rem;
            content: "";
         }

         li {
            margin-bottom: 0px !important;
            margin-left: -5px !important;
            line-height: 27px !important;
            list-style-type: none !important;
         }
      }

      &.joplin-checklist {
         margin-right: 0px !important;
         margin-left: 19px !important;
         padding-left: 0 !important;
         list-style: none !important;

         li {
            display: list-item;
            position: relative !important;
            opacity: 1 !important;
            margin: 0px 0px 0px 8px !important;
            margin-left: 6px !important;
            color: #000000 !important;
            list-style: none !important;
            line-height: 1.6rem !important;

            &::before {
               position: absolute;
               top: 1.6px;
               vertical-align: middle !important;
               cursor: pointer;
               margin-left: -2em;
               background-size: 16px 16px !important;
               padding: 0 !important;
               padding-left: 7px !important;
               width: 1em;
               height: 1em;
               pointer-events: all;
               color: #373737 !important;
               font-size: 16px !important;
               font-family: "Font Awesome 5 Free";
            }

            &:not(.checked)::before {
               content: "\f0c8" !important;
            }

            &.checked::before {
               content: "\f14a" !important;
            }


         }
      }

      li {
         p {
            code {
               display: inline-flex;
               padding: 6px 4px 6px 4px !important;
               height: 11px !important;
               font-size: 12px !important;
               letter-spacing: .1px !important;
            }
         }
      }
   }

   ol {
      margin-left: 17px !important;

      padding: 0;

      li {
         margin-top: 0px !important;

         margin-bottom: 0px !important;
         padding-left: var(--list-ol-padding-left) !important;
         list-style: decimal !important;

         &::marker {
            color: var(--list-ul-marker-color);
            font-weight: 400;
            font-size: 15px;
         }

         div.joplin-editable pre.hljs code,
         pre.hljs code {
            display: block !important;
            position: relative;
            top: -1px !important;
            margin-top: 0px !important;
            margin-bottom: 0px !important;

            /* New Additions */
            border: 0px !important;
            border-radius: var(--codeblock-border-radius) !important;
            background-color: var(--inlinecode-color-background) !important;
            padding: var(--codeblock-padding) !important;
            padding-top: 2px !important;
            height: unset !important;
            font-weight: 400 !important;
            font-size: 13px !important;
            font-size: .86rem !important;
            line-height: var(--codeblock-line-height) !important;
            font-family: var(--font-stack-inputmono) !important;
            letter-spacing: -.2px !important;
         }
      }
   }

   ul, ol {
      margin-top: 0 !important;
      margin-bottom: 16px !important;

      li {
         line-height: 28px !important;
      }
   }

   ul li ul,
   ol li ol {
      margin-bottom: 0px !important;
   }

   li p {
      margin-top: 0.2em;
      margin-bottom: 0 !important;
   }

   /* CODE STYLES ///////////////////////////////////////////////////////////////////////////////*/

   code.inline-code {
      display         : inline;
      position        : relative;
      vertical-align  : middle;
      margin          : var(--inlinecode-margin);
      border          : var(--inlinecode-border);
      border-radius   : var(--inlinecode-border-radius-default);
      background-color: var(--inlinecode-color-background);
      padding         : var(--inlinecode-padding);
      color           : var(--inlinecode-color-default);
      font-size       : var(--inlinecode-font-size);
      font-family     : var(--font-stack-inputmono);

   }


   div.joplin-editable pre.hljs, pre.hljs, .hljs {
      display: block !important;
      margin-top: .65rem !important;
      margin-bottom: 12px !important;
      border: none !important;
      border-width: 0px !important;
      border-style: none;
      border-color: var(--codeblock-border-color) !important;
      border-radius: var(--codeblock-border-radius) !important;
      overflow-x: auto !important;
      text-wrap: wrap !important;
      width: var(--default-body-max-width) !important;
      background-color: var(--codeblock-background-color) !important;

      code {
         display: block !important;
         margin-top: 0px !important;
         margin-right: 0 !important;
         margin-bottom: 0px !important;
         margin-left: 0 !important;
         border: 1px solid var(--codeblock-border-color) !important;
         border-radius: var(--codeblock-border-radius) !important;
         padding: var(--codeblock-padding) !important;
         height: unset !important;
         color: #3f3d3d;
         font-weight: 300 !important;
         font-size: 13.4px !important;
         line-height: 1.35rem !important;
         font-family: var(--font-stack-sfmono) !important;
         letter-spacing: -.2px !important;
         background-color: var(--codeblock-background-color) !important;
      }
   }

   /* TABLE STYLES //////////////////////////////////////////////////////////////////////////////*/

   div.joplin-table-wrapper {
      display: inline-flex;
      box-sizing: border-box !important;
      margin: 0 !important;
      padding: 0 !important;
      width: var(--default-body-max-width) !important;
      margin-bottom: 8px !important;

      /* Push consecutive tables away from eachother ///////////////////////////////////////////*/
      +div.joplin-table-wrapper {
         margin-top: 20px !important;
      }
   }

   table td,
   table th,
   td,
   th {
      padding: unset !important;
   }

   table, table.mce-item-table {
      border: 1px solid var(--table-color-border-default) !important;
      background-color: var(--table-color-row-non-header) !important;
      width: var(--default-body-max-width) !important;
      width: 100% !important;
      font-size: var(--table-font-size-default) !important;
      margin-top: 24px !important;
      table-layout: fixed !important;


      /* TABLE CUSTOM COLOR ASSIGNMENT ///////////////////////////////////////////////////////////*/
      /*//////////////////////////////////////////////////////////////////////////////////////////*/

      /* DEFAULT STYLES */
      span[data-mce-style~="color:"] {
         border: 1px solid #e4e9ee;
         display: inline-flex !important;
         padding-left: 6px !important;
         letter-spacing: .2px !important;
         padding-right: 6px !important;
         border-radius: 5px !important;
         height: 24px !important;
         align-items: center !important;
         font-size: 14px !important;
         font-weight: 400 !important;
         font-family: var(--font-stack-roboto) !important;
         margin-top: 1px !important;
         border-collapse: collapse !important;
      }

      /* LIGHT GREEN */
      span[data-mce-style="color: rgb(191, 237, 210);"] {
         color: #4aaca2 !important;
         background-color: #f4f9f9 !important;
         border: none !important;
         &:hover {
            color: #358b82 !important;
            background-color: #edf9f9 !important;
         }
      }
      /* DARK GREEN */
      span[data-mce-style="color: rgb(45, 194, 107);"] {
         color: #049463 !important;
         background-color: #f0fbf7 !important;
         border: none !important;
         &:hover {
            color: #016358 !important;
            background-color: #eafbf5 !important;
         }
      }
      /* VERY DARK GREEN */
      span[data-mce-style="color: rgb(22, 145, 121);"] {
         color: #037f6e !important;
         background-color: #ecfcf6 !important;
         border: none !important;
         &:hover {
            color: #014d49 !important;
            background-color: #e4f9f1 !important;
         }
      }

      /* LIGHT YELLOW */
      span[data-mce-style="color: rgb(251, 238, 184);"] {
         color: #cfa703 !important;
         background-color: #fffae9 !important;
         border: none !important;
         &:hover {
            color: #bb8003 !important;
            background-color: #fffae9 !important;
         }
      }
      /* DARK YELLOW */
      span[data-mce-style="color: rgb(241, 196, 15);"] {
         color: #c88903 !important;
         background-color: #fef6e1 !important;
         border: none !important;
         &:hover {
            color: #b25805 !important;
            background-color: #fbf3dd !important;
         }
      }
      /* VERY DARK YELLOW */
      span[data-mce-style="color: rgb(230, 126, 35);"] {
         color: #d15f03 !important;
         background-color: #fff6e6 !important;
         border: none !important;
         &:hover {
            color: #b84c00 !important;
            background-color: #fff7e9 !important;
         }
      }

      /* LIGHT RED */
      span[data-mce-style="color: rgb(248, 202, 198);"] {
         color: #db5960 !important;
         background-color: #fef3f5 !important;
         border: none !important;
         &:hover {
            color: #cb262e !important;
            background-color: #fff6f8 !important;
         }
      }
      /* DARK RED */
      span[data-mce-style="color: rgb(224, 62, 45);"] {
         color: #dd0c48 !important;
         background-color: #fff3f5 !important;
         border: none !important;
         &:hover {
            color: #ac0233 !important;
            background-color: #fef2f4 !important;
         }
      }
      /* VERY DARK RED */
      span[data-mce-style="color: rgb(186, 55, 42);"] {
         color: #bb0523 !important;
         background-color: #fff1f3 !important;
         border: none !important;
         &:hover {
            color: #830101 !important;
            background-color: #fcf2f4 !important;
         }
      }

      /* LIGHT PURPLE */
      span[data-mce-style="color: rgb(236, 202, 250);"] {
         color: #9f4ef5 !important;
         background-color:#f6efff !important;
         border: none !important;
         &:hover {
            color: #6f19cb !important;
            background-color: #f2e9ff !important;
         }
      }
      /* DARK PURPLE */
      span[data-mce-style="color: rgb(185, 106, 217);"] {
         color: #7b1ddf !important;
         background-color: #f6efff !important;
         border: none !important;
         &:hover {
            color: #5607a9 !important;
            background-color: #f2e9ff !important;
         }
      }
      /* VERY DARK PURPLE */
      span[data-mce-style="color: rgb(132, 63, 161);"] {
         color: #7812e5 !important;
         background-color: #f6efff !important;
         border: none !important;
         &:hover {
            color: #3d007d !important;
            background-color: #f4ecff !important;
         }
      }

      /* LIGHT BLUE */
      span[data-mce-style="color: rgb(194, 224, 244);"] {
         color: #4f6fe3 !important;
         background-color: #f0f3ff !important;
         border: none !important;
         &:hover {
            color: #1034b5 !important;
            background-color: #f0f3ff !important;
         }
      }
      /* DARK BLUE */
      span[data-mce-style="color: rgb(53, 152, 219);"] {
         color: #1842dc !important;
         background-color: #edf1ff !important;
         border: none !important;
         &:hover {
            color: #0529af !important;
            background-color: #e9eeff !important;
         }
      }
      /* VERY DARK BLUE */
      span[data-mce-style="color: rgb(35, 111, 161);"] {
         color: #0f27c2 !important;
         background-color: #edefff !important;
         border: none !important;
         &:hover {
            color: #08198a !important;
            background-color: #e5e7fd !important;
         }
      }

      /* GRAY 100-500 RESET TO INLINE */
      span[data-mce-style="color: rgb(236, 240, 241);"],
      span[data-mce-style="color: rgb(206, 212, 217);"],
      span[data-mce-style="color: rgb(149, 165, 166);"],
      span[data-mce-style="color: rgb(126, 140, 141);"],
      span[data-mce-style="color: rgb(52, 73, 94);"] {
         border: none !important;
         display: inline !important;
         padding: 0 !important;
         letter-spacing: unset !important;
         border-radius: unset !important;
         height: unset !important;
         align-items: unset !important;
         font-size: inherit !important;
         font-weight: inherit !important;
         margin: 0 !important;
         border-collapse: unset !important;
      }

      /* GRAY 100-500 */
      span[data-mce-style="color: rgb(236, 240, 241);"] { color: #2f36457a !important; &:hover { color: #767e8e   !important; } }
      span[data-mce-style="color: rgb(206, 212, 217);"] { color: #2f3645a1 !important; &:hover { color: #2f3645bf !important; } }
      span[data-mce-style="color: rgb(149, 165, 166);"] { color: #2f3645b8 !important; &:hover { color: #2f3645d9 !important; } }
      span[data-mce-style="color: rgb(126, 140, 141);"] { color: #2f3645d6 !important; &:hover { color: #2f3645ed !important; } }
      span[data-mce-style="color: rgb(52, 73, 94);"]    { color: #2f3645eb !important; &:hover { color: #1a1e28   !important; } }

      /* BLACK */
      span[data-mce-style="color: rgb(0, 0, 0);"] {
         color: #424851 !important;
         background-color: none !important;
         border: 1px solid #e1e4ea !important;
         &:hover {
            color: #191a1d !important;
            background-color: #f5f6f9 !important;
            border: 1px solid #c9ccd1 !important;
         }
      }

      /* WHITE */
      span[data-mce-style="color: rgb(255, 255, 255);"] {
         color: #ffffff !important;
         background-color: #7d858f !important;
         border: none !important;
      }


      p, div, td, tr, tr td,
      th, code, div code, tr code,
      tr div code, td code, td div code,
      th code, th div code, tr p,
      tr div, th p, th div, td p,
      td div, tbody, thead {
         font-size: var(--table-font-size-default) !important;
      }

      p+hr {
         border-width: 1px !important;
         border-color: #dcd8d8 !important;
      }


      tr, th, tr td, tr th {
         border: 1px solid var(--table-color-border-default) !important;
      }

      tr {
         &:nth-child(even) {
            background-color: var(--table-color-row-non-header) !important;
         }
      }

      a {
         background-color: transparent;

         color: var(--link-color-primary-blue);
         text-decoration: none !important;

         &:hover {
            cursor: pointer !important;
            color: var(--link-color-primary-blue-dark);
         }

         &:visited {
            cursor: pointer !important;
            color: var(--link-color-primary-visited);
         }
      }

      p {
         ol li {
            margin-bottom: 1rem !important;
         }
      }

      strong {
         font-weight: 500 !important;
      }

      hr {
         margin: var(--horizontal-line-table-margin) !important;
         border-width: var(--horizontal-line-table-border-width) !important;
         border-color: var(--horizontal-line-color) !important;
         padding: 0px !important;
      }

      /* TABLE CODE STYLES /////////////////////////////////////////////////////////////////////*/

      code, .inline-code {
         strong {
            color: var(--inlinecode-color-strong) !important;
            font-weight: var(--inlinecode-font-weight-strong) !important;
            font-size: var(--inlinecode-font-size) !important;
         }
      }

      thead mark, thead td mark, thead tr mark, thead tr td mark,
      tbody mark, tbody td mark, tbody tr mark, tbody tr td mark {
         display: inline-flex !important;
            box-sizing: border-box !important;
            margin-left: 1px;
            margin-right: 1px;
            border: none !important;
            border-radius: 0.2875rem !important;
            border-collapse: collapse !important;
            background-origin: padding-box !important;
            background-color: #e1ddfc !important;
            color: #270987 !important;
            font-size: 13.6px !important;
            line-height: 24.5px !important;
            font-family: var(--font-stack-roboto) !important;
            overflow-wrap: break-word !important;
            height: 24px !important;
            font-weight: 400;
            letter-spacing: .5px !important;
      }

      div.joplin-editable {
         margin-top: 12px !important;
         margin-bottom: 8px !important;
         width: unset !important;
      }

      +div.joplin-editable {
         margin-top: 26px !important;
      }

      .hljs, .hljs code {
         display: block !important;
         border-radius: var(--codeblock-border-radius) !important;
         padding: 0 !important;
         font-family: var(--font-stack-inputmono) !important;
      }

      tbody, thead {

         td, th {
            border: 1px solid var(--table-color-border-default) !important;
            padding: 8px 12px 8px 12px !important;
            color: #32373F !important;
            font-size: var(--default-font-size-table) !important;
            font-family: var(--font-stack-helvnow) !important;
            text-align: left !important;

         }

         tr:hover {
            background-color: #ffffff;
         }

         tr td code,
         tr th code,
         code {
            display: inline !important;
            position: relative !important;
            vertical-align: middle !important;
            box-sizing: border-box !important;
            margin-right: 0 !important;
            margin-left: 0 !important;
            border: var(--inlinecode-border) !important;
            border-radius: 3.5px !important;
            border-style: solid !important;
            border-color: #c1c3cc !important;
            background-color: #f8f8f8 !important;
            padding: 2px 5px !important;
            color: #4e5154 !important;
            font-size: 13px !important;
            line-height: 1.9rem !important;
            font-family: var(--font-stack-inputmono) !important;
            top: -1px !important;

            strong {
               font-weight: 400 !important;
               font-family: var(--font-stack-inputmono);
            }
         }

         /* TABLE LIST STYLES /////////////////////////////////////////////////////////////////*/

         ol li {
            margin-bottom: 4px !important;
            line-height: 1.3rem !important;
         }

         ul {
            &:not(.joplin-checklist) li {
               margin-bottom: 1px !important;
               margin-left: -3px !important;
               color: #404040 !important;
               line-height: 1.6rem !important;

               &::before {
                  opacity: .7 !important;
                  margin-right: 8px !important;
               }
            }
         }
      }

      thead {
         tr {
            th {

               border: 1px solid var(--table-color-border-default-head) !important;
               background: #F9F9F9 !important;
               background-color: #f9f9f9 !important;
               padding: 10px 14px 10px 14px !important;
               height: var(--table-row-height) !important;
               color: #0f0f0f !important;
               font-weight: 500 !important;
               font-size: 16px !important;
               font-family: var(--font-stack-roboto) !important;
               letter-spacing: .1px;

               code {
                  top: -.5px !important;
                  margin-left: 3px !important;
                  border: 1px solid #cecece !important;
                  font-weight: 400 !important;
                  font-size: 13px !important;

                  strong {
                     font-weight: 400 !important;
                     font-family: var(--font-stack-inputmono);
                  }
               }
            }
         }
      }

      tbody {

         tr {

            height: 39px;

            &::nth-child(even) {
               background-color: var(--table-color-row-non-header);

               &:hover {
                  background-color: var(--table-color-row-non-header);
               }
            }

            td {
               vertical-align: middle !important;
               border: 1px solid var(--table-color-border-default) !important;
               background-color: #ffffff;
               padding: 8px 14px 8px 14px !important;
               height: var(--table-row-height) !important;
               color: #393939 !important;
               font-weight: 400;
               font-size: var(--default-font-size-table) !important;
               line-height: 1.6rem !important;
               font-family: var(--font-stack-roboto) !important;

               p {
                  margin: 6px 0px 16px 0px !important;
                  padding: 0 !important;
                  color: var(--default-text-color);
                  font-size: var(--default-font-size-table) !important;
                  line-height: 1.36rem !important;
                  font-family: var(--font-stack-roboto) !important;
                  text-align: left !important;
                  width: unset !important;
               }


               strong {
                  font-weight: 400 !important;
                  font-family: var(--font-stack-helvnowmed);

                  code {
                     font-weight: 400 !important;
                     font-family: "Input Mono Medium" !important;
                  }
               }

               hr {
                  margin-top: 16px !important;
                  margin-bottom: 16px !important;
               }

               pre.hljs {
                  border: 1px solid #CCCCCC !important;
                  background-color: #FFFFFF !important;
                  white-space: break-spaces !important;
                  width: unset !important;

                  code {
                     display: inline-block !important;
                     white-space: break-spaces;
                     word-wrap: break-word;
                     /* Additions */
                     margin-top: 0px !important;
                     margin-bottom: 0px !important;
                     border: 0 !important;
                     background-color: var(--codeblock-background-color) !important;
                     padding: 12px !important;
                     width: 100% !important;
                     height: unset !important;
                     font-weight: 400 !important;
                     font-size: var(--default-font-size-table-codeblock) !important;
                     line-height: 1.26rem !important;
                     font-family: var(--font-stack-sfmono) !important;
                     letter-spacing: -.1px !important;
                  }
               }

               ol li, ul li {
                  strong code {
                     font-weight: 400 !important;
                     font-family: "Input Mono Medium" !important;
                  }

                  code, code.inline-code {
                     border: var(--inlinecode-border) !important;
                     border-radius: var(--inlinecode-border-radius-default) !important;
                     background-color: var(--inlinecode-color-background) !important;
                     font-weight: 400;
                     font-family: var(--font-stack-inputmono) !important;
                  }
               }

               ol {
                  margin: 0px 0px 0px 18px !important;
                  margin-left: 1.1em !important;
               }

               code+code,
               code+strong code,
               ol li code+code,
               ol li code+strong code,
               ul li code+code,
               ul li code+strong code {
                  margin-left: 3px !important;
               }
            }
         }

         pre.hljs,
         div pre.hljs {
            margin-top: 6px !important;
            margin-bottom: 6px !important;
            background-color: transparent !important;

            code {
               display: block !important;
               padding: 12px !important;
               max-width: 1030px !important;
               /* margin-top: 7px !important;
                    margin-bottom: 7px !important;
                    margin-right: 12px !important; */
            }
         }

         strong {
            font-weight: 500 !important;
            letter-spacing: .2px !important;
         }
      }
   }

   /* TABLE CORRECTIONS /////////////////////////////////////////////////////////////////////////*/

   h6+table h2+table,
   h1+table {
      margin-top: 32px !important;
   }

   ul+table {
      margin-top: 28px !important;
   }

   /* HEADER STYLES /////////////////////////////////////////////////////////////////////////////*/

   h1, h2, h3, h4, h5 {

      border-bottom: var(--header-border-bottom) !important;
      padding: var(--header-padding) !important;
      color: var(--header-color) !important;
      font-family: var(--font-stack-helvnow) !important;
      width: var(--default-body-max-width) !important;

      strong {
         font-weight: 500 !important;
      }

      &:first-child {
         margin-top: var(--header-first-child-margin-top) !important
      }

   }

   h1 {
      margin: 36px 0px 8px 0px !important;
      padding: var(--h1-padding-default) !important;
      color: var(--header-color) !important;
      font-weight: var(--h1-font-weight) !important;
      font-size: var(--h1-font-size) !important;
      font-family: var(--font-stack-sfpro) !important;
      letter-spacing: var(--h1-letter-spacing) !important;

      code {
         display: inline !important;
         position: relative !important;
         top: var(--header-code-h1-top-adjustment) !important;
         box-sizing: border-box !important;
         margin: var(--header-code-margin) !important;
         border: var(--header-code-h1-border) !important;
         border-radius: var(--header-code-border-radius) !important;
         padding: var(--header-code-h1-padding) !important;
         color: var(--header-code-h1-color) !important;
         font-weight: var(--header-code-font-weight) !important;
         font-size: var(--header-code-h1-font-size) !important;
         font-family: var(--font-stack-sfmono) !important;
         letter-spacing: var(--header-code-h1-letter-spacing) !important;
      }
   }

   h2 {
      margin: 26px 0px 12px 0px !important;
      padding-bottom: 4px !important;
      color: var(--header-color) !important;
      font-weight: var(--h2-font-weight) !important;
      font-size: var(--h2-font-size) !important;
      font-family: var(--font-stack-helvnowdisplay) !important;
      font-family: var(--font-stack-sfpro) !important;
      letter-spacing: .2px !important;
      box-sizing: border-box;
      border-bottom: 1px solid #d0d2d8 !important;

      code {
         display: inline !important;
         position: relative !important;
         top: var(--header-code-h2-top-adjustment) !important;
         box-sizing: border-box !important;
         margin: var(--header-code-margin) !important;
         border: var(--header-code-h2-border) !important;
         border-radius: var(--header-code-border-radius) !important;
         padding: var(--header-code-h2-padding) !important;
         color: var(--header-code-h2-color) !important;
         font-weight: var(--header-code-h2-font-weight);
         font-size: var(--header-code-h2-font-size) !important;
         font-family: var(--font-stack-sfmono) !important;
         letter-spacing: var(--header-code-h2-letter-spacing) !important;
      }
   }

   h3 {
      margin: 26px 0px 10px 0px !important;
      padding-bottom: 0px !important;
      color: var(--header-color) !important;
      font-weight: 500 !important;
      font-size: var(--h3-font-size) !important;
      font-family: var(--font-stack-sfpro) !important;
      letter-spacing: -.04px !important;
      border: none !important;

      code {
         display: inline !important;
         position: relative !important;
         box-sizing: border-box !important;
         margin: var(--header-code-margin) !important;
         border: var(--header-code-h3-border) !important;
         border-radius: var(--header-code-border-radius) !important;
         padding: var(--header-code-h3-padding) !important;
         color: var(--header-code-h3-color) !important;
         font-weight: var(--header-code-h3-font-weight);
         font-size: var(--header-code-h3-font-size) !important;
         font-family: var(--font-stack-inputmono) !important;
         letter-spacing: var(--header-code-h3-letter-spacing) !important;
      }

   }

   h4 {
      margin: 26px 0px 10px 0px !important;
      border: none !important;
      padding: 0 !important;
      color: var(--header-color) !important;
      font-weight: 500 !important;
      font-size: var(--h4-font-size) !important;
      font-family: var(--font-stack-sfpro) !important;
      letter-spacing: -.11px;
      border-bottom: none !important;

      code {
         display: inline !important;
         position: relative !important;
         top: var(--header-code-h4-top-adjustment) !important;
         box-sizing: border-box !important;
         margin: var(--header-code-margin) !important;
         border: var(--header-code-h4-border) !important;
         border-color: var(--header-code-h4-border-color) !important;
         border-radius: var(--header-code-border-radius) !important;
         padding: var(--header-code-h4-padding) !important;
         color: var(--header-code-h4-color) !important;
         font-weight: var(--header-code-h4-font-weight) !important;
         font-size: var(--header-code-h4-font-size) !important;
         font-family: var(--font-stack-inputmono) !important;
         letter-spacing: var(--header-code-h4-letter-spacing) !important;
      }
   }

   h5 {
      display: block;
      margin: 20px 0px 8px 0px !important;
      border-bottom: none !important;
      border-radius: 5px;
      padding: 0px !important;
      padding-bottom: 0px !important;
      color: var(--header-color) !important;
      font-weight: 500 !important;
      font-size: var(--h5-font-size) !important;
      font-family: var(--font-stack-sfpro) !important;
      letter-spacing: .15px !important;

      code {
         display: inline !important;
         position: relative !important;
         top: var(--header-code-h5-top-adjustment) !important;
         box-sizing: border-box !important;
         margin: var(--header-code-margin) !important;
         border: var(--header-code-h5-border) !important;
         border-color: var(--header-code-h5-border-color) !important;
         border-radius: var(--header-code-border-radius) !important;
         padding: var(--header-code-h5-padding) !important;
         color: var(--header-code-h5-color) !important;
         font-weight: var(--header-code-h5-font-weight) !important;
         font-size: var(--header-code-h5-font-size) !important;
         font-family: var(--font-stack-inputmono) !important;
         letter-spacing: var(--header-code-h5-letter-spacing) !important;
      }
   }

   h6 {
      margin: 20px 0px 6px 0px !important;
      padding: 0 !important;
      border: 0 !important;
      color: #212225 !important;
      font-weight: var(--h6-font-weight) !important;
      font-size: var(--h6-font-size) !important;
      letter-spacing: -.10px !important;
      font-family: var(--font-stack-sfpro) !important;
      width: var(--default-body-max-width);

      code {
         display: inline !important;
         position: relative !important;
         top: -1px !important;
         box-sizing: border-box !important;
         margin: 2px !important;
         border: 1px solid #b7bcc9 !important;
         border-radius: 5.2px !important;
         background-color: #fbfbfb !important;
         padding: 4px 5px !important;
         color: #737579 !important;
         font-weight: 400 !important;
         font-size: 11.3px !important;
         font-family: var(--font-stack-inputmono) !important;
         letter-spacing: .5px !important;
      }

      a {
         display: inline;
         box-sizing: border-box;
         border: 0 !important;
         padding: 0 !important;
         color: #3651fa !important;
         font-weight: 500;
         font-size: 1rem !important;
         font-family: var(--font-stack-helvnow) !important;
         letter-spacing: .1px !important;
      }
      a:hover {
         color: #1025b0 !important;
      }
   }
}

html .mce-content-body #rendered-md,
html .mce-content-body,
html div.exported-note #rendered-md,
html div.exported-note,
html body,
html #rendered-md,
#rendered-md {

   div.admonition {

      border-radius: 9px !important;
      padding: .7rem;
      padding-right: 1.1rem;
      padding-left: 1.1rem;
      word-wrap: break-word;
      margin-top: 1.7rem;
      margin-bottom: 1.5rem;
      font-size: 15px !important;
      word-break: break-word;

      code {
         display: inline !important;
         position: relative;
         top: -1px;
         margin-right: 2px;
         margin-left: 2px;
         border-width: 2px;
         border-style: solid;
         background-color: #feffff;
         font-size: 15px !important;
      }

      p {

         margin-bottom: 8px !important;
         line-height: 1.4rem !important;

         &:first-child {
            font-weight: 500 !important;
            font-size: 18px !important;
            color: #212224 !important;
            margin-bottom: 0 !important;

            &::before {
               position: relative;
               top: -1px;
               vertical-align: middle !important;
               cursor: pointer;
               margin-right: 10px !important;
               background-size: 14px 14px !important;
               pointer-events: all;
               color: #5b5d63 !important;
               font-size: 22px !important;
               font-family: "Font Awesome 6 Sharp Regular";
               filter: invert(0%) sepia(20%) saturate(0%) hue-rotate(237deg) brightness(70%) contrast(107%);
            }
         }
      }

      &.note {
         border: none;
         background-color: #f0f0f2;
         color: #55595f;

         code {
            border-color: none !important;
            background-color: #e0e0e3;
         }

         p:first-child {
            &::before {
               content: "\f05a";
               color: #55595f !important;
            }
         }
      }

      &.info {
         border: #e9ebed;
         background-color: #edf0f3;
         color: #27282b;

         p:first-child {
            &::before {
               content: "\f05a";
               color: #858b92 !important;
            }
         }

         code {
            border-color: #d0d4db !important;
            background-color: #feffff;
         }
      }

      &.abstract {
         border: #e9daff;
         background-color: #e9daff;
         color: #453066;

         code {
            border-color: #c6a8f3 !important;
            background-color: #f9f6ff;
         }
      }

      &.tip {
         border: #e9ebed;
         background-color: #edf0f3;
         color: #27282b;
      }

      &.success {
         border: #d8f4eb;
         background-color: #d8f4eb;
         color: #074946;
      }

      &.question {
         border: #e9ebed;
         background-color: #edf0f3;
         color: #27282b;
      }

      &.warning {
         border: #fff6dd;
         background-color: #fff6dd;
         color: #553304;
      }

      &.failure {
         border: #f3c7cd;
         background-color: #f3c7cd;
         color: #6d0e3a;
      }

      &.danger {
         border: #fff4df;
         background-color: #fff4df;
         color: #553304;
      }

      &.bug {
         border: #e9ebed;
         background-color: #edf0f3;
         color: #27282b;
      }

      &.example {
         border: #e9ebed;
         background-color: #edf0f3;
         color: #27282b;
      }

      &.quote {
         border: #e9ebed;
         background-color: #edf0f3;
         color: #27282b;
      }
   }



}

/* CORRECTIONS AND ADJUSTMENTS ///////////////////////////////////////////////////////////////////*/
/*////////////////////////////////////////////////////////////////////////////////////////////////*/
/*////////////////////////////////////////////////////////////////////////////////////////////////*/

html .mce-content-body #rendered-md table tbody ol,
html div.exported-note #rendered-md table tbody ol {
   margin-bottom: 15px !important;
}

html #rendered-md>h2+img,
html div.exported-note #rendered-md>h2+img {
   margin-top: 80px !important;
   margin-bottom: 30px !important;
}

html .mce-content-body div#rendered-md>img:first-child,
html div.exported-note #rendered-md>img:first-child {
   margin: unset !important;
   margin-top: 20px !important;
   margin-bottom: -14px !important;
   padding: 0 !important;
}

html #rendered-md,
html .mce-content-body #rendered-md,
html .mce-content-body,
html div.exported-note #rendered-md,
html div.exported-note,
#rendered-md {

   /* Set margin for headers that come immmediately after any unordered list ////////////////////////*/
   ul+h1, ul+h2, ul+h3, ul+h4, ul+h5 {
      margin-top: 22px !important;
   }

   /* Set margin for headers that come immediately after any codeblock //////////////////////////////*/
   div.joplin-editable+h1, div.joplin-editable+h2, div.joplin-editable+h3,
   div.joplin-editable+h4, div.joplin-editable+h5 {
      margin-top: 24px !important;
   }

   /* Increase spacing between table and code block /////////////////////////////////////////////////*/
   table+div.joplin-editable {
      margin-top: 24px !important;
   }

   /* Decrease spacing between paragraph and table //////////////////////////////////////////////////*/
   /* p + div.joplin-table-wrapper { margin-top: -12px !important; } */

   /* Increase spacing between paragraph and code block /////////////////////////////////////////////*/
   /* p+div.joplin-editable {
      margin-top: 16px !important;
   } */

   /* Increase spacing between consecutive code blocks //////////////////////////////////////////////*/
   /* div.joplin-editable+div.joplin-editable {
      margin-top: 24px !important;
   } */

   /* Move paragraphs slightly closer to headings ///////////////////////////////////////////////////*/
   h1+p {
      margin-top: 15px !important;
   }

   h2+p {
      margin-top: 0 !important;
   }

   h3+p {
      margin-top: 0 !important;
   }

   h4+p {
      margin-top: -4px !important;
   }

   h5+p {
      margin-top: 0 !important;
   }
   /* Push code blocks away from table bottoms //////////////////////////////////////////////////////*/
   table+div.joplin-editable {
      margin-top: 26px !important;
   }

   /* Push tables away from unordered list bottoms //////////////////////////////////////////////////*/
   ul+table {
      margin-top: 28px !important;
   }




   /* Push unordered lists closer to paragraph endings //////////////////////////////////////////////*/
   p+ul {
      margin-top: -7px !important;
   }

   /* Define horizontal rule margins following a paragraph //////////////////////////////////////////*/
   p+hr {
      margin-top: var(--hr-margin-top) !important;
      margin-bottom: var(--hr-margin-bottom) !important;
   }

   /* Push code blocks away from headers ////////////////////////////////////////////////////////////*/
   h1+div.joplin-editable, h2+div.joplin-editable, h3+div.joplin-editable,
   h4+div.joplin-editable, h5+div.joplin-editable {
      margin-top: 14px !important;
   }

   /* Push code block away from paragraph to equalize p > codeblock > p spacing /////////////////////*/
   div.joplin-editable+p {
      margin-top: 10px;
   }

   /* Push code block away from paragraph in table to equalize p > codeblock > p spacing ////////////*/
   table p+div.joplin-editable {
      margin-top: 12px !important;
   }

   /* Undocumented corrections //////////////////////////////////////////////////////////////////////*/

   p+ul {
      margin-top: -7px !important;
   }

   p+hr {
      margin-top: 18px !important;
      margin-bottom: 18px !important;
   }

   h3+div.joplin-editable, h2+div.joplin-editable, h1+div.joplin-editable,
   h4+div.joplin-editable, h5+div.joplin-editable {
      margin-top: 12px !important;
   }

   /* Push header elements closer to images appearing at the very top of the document ///////////////*/
   /* img:first-child + h1, img:first-child + h2, img:first-child + h3,
    img:first-child + h4, img:first-child + h5 {

    } */

   /* Remove margin from images appearing at the very top of the document ///////////////////////////*/
   /* img:first-child {
        margin: 0 !important;
        margin-top: var(--image-margin-top) !important;
    } */
}

/* SCREEN ONLY STYLES ////////////////////////////////////////////////////////////////////////////*/
/*////////////////////////////////////////////////////////////////////////////////////////////////*/
/*////////////////////////////////////////////////////////////////////////////////////////////////*/

.mce-content-body #rendered-md {
   p {
      line-height: 1.7rem !important;

      strong {
         font-weight: 500 !important;
      }
   }
}


/* INTERFACE GUI ADJUSTMENTS AND CORRECTIONS /////////////////////////////////////////////////////*/
/*////////////////////////////////////////////////////////////////////////////////////////////////*/
/*////////////////////////////////////////////////////////////////////////////////////////////////*/

/* UI TABLE POPUP STYLES /////////////////////////////////////////////////////////////////////////*/
div.tox.tox-silver-sink .tox-pop__dialog .tox-toolbar .tox-toolbar__group .tox-tbtn .tox-icon.tox-tbtn__icon-wrap svg {
   color: #CCC !important;
   fill: #a8adb5 !important;
   scale: 95%;
}

div.exported-note div.exported-note-title {
   display: none !important;
}