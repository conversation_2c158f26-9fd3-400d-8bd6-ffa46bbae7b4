// Generated from csv.g4 by ANTLR 4.8
// jshint ignore: start
var antlr4 = require('antlr4/index');
var csvListener = require('./csvListener').csvListener;
var grammarFileName = "csv.g4";


var serializedATN = ["\u0003\u608b\ua72a\u8133\ub9ed\u417c\u3be7\u7786\u5964",
    "\u0003\u0007-\u0004\u0002\t\u0002\u0004\u0003\t\u0003\u0004\u0004\t",
    "\u0004\u0004\u0005\t\u0005\u0004\u0006\t\u0006\u0004\u0007\t\u0007\u0003",
    "\u0002\u0003\u0002\u0006\u0002\u0011\n\u0002\r\u0002\u000e\u0002\u0012",
    "\u0003\u0003\u0003\u0003\u0003\u0004\u0003\u0004\u0003\u0004\u0007\u0004",
    "\u001a\n\u0004\f\u0004\u000e\u0004\u001d\u000b\u0004\u0003\u0004\u0005",
    "\u0004 \n\u0004\u0003\u0004\u0003\u0004\u0003\u0005\u0003\u0005\u0003",
    "\u0005\u0005\u0005\'\n\u0005\u0003\u0006\u0003\u0006\u0003\u0007\u0003",
    "\u0007\u0003\u0007\u0002\u0002\b\u0002\u0004\u0006\b\n\f\u0002\u0002",
    "\u0002+\u0002\u000e\u0003\u0002\u0002\u0002\u0004\u0014\u0003\u0002",
    "\u0002\u0002\u0006\u0016\u0003\u0002\u0002\u0002\b&\u0003\u0002\u0002",
    "\u0002\n(\u0003\u0002\u0002\u0002\f*\u0003\u0002\u0002\u0002\u000e\u0010",
    "\u0005\u0004\u0003\u0002\u000f\u0011\u0005\u0006\u0004\u0002\u0010\u000f",
    "\u0003\u0002\u0002\u0002\u0011\u0012\u0003\u0002\u0002\u0002\u0012\u0010",
    "\u0003\u0002\u0002\u0002\u0012\u0013\u0003\u0002\u0002\u0002\u0013\u0003",
    "\u0003\u0002\u0002\u0002\u0014\u0015\u0005\u0006\u0004\u0002\u0015\u0005",
    "\u0003\u0002\u0002\u0002\u0016\u001b\u0005\b\u0005\u0002\u0017\u0018",
    "\u0007\u0005\u0002\u0002\u0018\u001a\u0005\b\u0005\u0002\u0019\u0017",
    "\u0003\u0002\u0002\u0002\u001a\u001d\u0003\u0002\u0002\u0002\u001b\u0019",
    "\u0003\u0002\u0002\u0002\u001b\u001c\u0003\u0002\u0002\u0002\u001c\u001f",
    "\u0003\u0002\u0002\u0002\u001d\u001b\u0003\u0002\u0002\u0002\u001e ",
    "\u0007\u0003\u0002\u0002\u001f\u001e\u0003\u0002\u0002\u0002\u001f ",
    "\u0003\u0002\u0002\u0002 !\u0003\u0002\u0002\u0002!\"\u0007\u0004\u0002",
    "\u0002\"\u0007\u0003\u0002\u0002\u0002#\'\u0005\n\u0006\u0002$\'\u0005",
    "\f\u0007\u0002%\'\u0003\u0002\u0002\u0002&#\u0003\u0002\u0002\u0002",
    "&$\u0003\u0002\u0002\u0002&%\u0003\u0002\u0002\u0002\'\t\u0003\u0002",
    "\u0002\u0002()\u0007\u0006\u0002\u0002)\u000b\u0003\u0002\u0002\u0002",
    "*+\u0007\u0007\u0002\u0002+\r\u0003\u0002\u0002\u0002\u0006\u0012\u001b",
    "\u001f&"].join("");


var atn = new antlr4.atn.ATNDeserializer().deserialize(serializedATN);

var decisionsToDFA = atn.decisionToState.map( function(ds, index) { return new antlr4.dfa.DFA(ds, index); });

var sharedContextCache = new antlr4.PredictionContextCache();

var literalNames = [ null, "'\r'", "'\n'" ];

var symbolicNames = [ null, null, null, "DELIMITER", "TEXT", "STRING" ];

var ruleNames =  [ "csvFile", "hdr", "row", "field", "text", "string" ];

function csvParser (input) {
	antlr4.Parser.call(this, input);
    this._interp = new antlr4.atn.ParserATNSimulator(this, atn, decisionsToDFA, sharedContextCache);
    this.ruleNames = ruleNames;
    this.literalNames = literalNames;
    this.symbolicNames = symbolicNames;
    return this;
}

csvParser.prototype = Object.create(antlr4.Parser.prototype);
csvParser.prototype.constructor = csvParser;

Object.defineProperty(csvParser.prototype, "atn", {
	get : function() {
		return atn;
	}
});

csvParser.EOF = antlr4.Token.EOF;
csvParser.T__0 = 1;
csvParser.T__1 = 2;
csvParser.DELIMITER = 3;
csvParser.TEXT = 4;
csvParser.STRING = 5;

csvParser.RULE_csvFile = 0;
csvParser.RULE_hdr = 1;
csvParser.RULE_row = 2;
csvParser.RULE_field = 3;
csvParser.RULE_text = 4;
csvParser.RULE_string = 5;


function CsvFileContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = csvParser.RULE_csvFile;
    return this;
}

CsvFileContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
CsvFileContext.prototype.constructor = CsvFileContext;

CsvFileContext.prototype.hdr = function() {
    return this.getTypedRuleContext(HdrContext,0);
};

CsvFileContext.prototype.row = function(i) {
    if(i===undefined) {
        i = null;
    }
    if(i===null) {
        return this.getTypedRuleContexts(RowContext);
    } else {
        return this.getTypedRuleContext(RowContext,i);
    }
};

CsvFileContext.prototype.enterRule = function(listener) {
    if(listener instanceof csvListener ) {
        listener.enterCsvFile(this);
	}
};

CsvFileContext.prototype.exitRule = function(listener) {
    if(listener instanceof csvListener ) {
        listener.exitCsvFile(this);
	}
};




csvParser.CsvFileContext = CsvFileContext;

csvParser.prototype.csvFile = function() {

    var localctx = new CsvFileContext(this, this._ctx, this.state);
    this.enterRule(localctx, 0, csvParser.RULE_csvFile);
    var _la = 0; // Token type
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 12;
        this.hdr();
        this.state = 14; 
        this._errHandler.sync(this);
        _la = this._input.LA(1);
        do {
            this.state = 13;
            this.row();
            this.state = 16; 
            this._errHandler.sync(this);
            _la = this._input.LA(1);
        } while((((_la) & ~0x1f) == 0 && ((1 << _la) & ((1 << csvParser.T__0) | (1 << csvParser.T__1) | (1 << csvParser.DELIMITER) | (1 << csvParser.TEXT) | (1 << csvParser.STRING))) !== 0));
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};


function HdrContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = csvParser.RULE_hdr;
    return this;
}

HdrContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
HdrContext.prototype.constructor = HdrContext;

HdrContext.prototype.row = function() {
    return this.getTypedRuleContext(RowContext,0);
};

HdrContext.prototype.enterRule = function(listener) {
    if(listener instanceof csvListener ) {
        listener.enterHdr(this);
	}
};

HdrContext.prototype.exitRule = function(listener) {
    if(listener instanceof csvListener ) {
        listener.exitHdr(this);
	}
};




csvParser.HdrContext = HdrContext;

csvParser.prototype.hdr = function() {

    var localctx = new HdrContext(this, this._ctx, this.state);
    this.enterRule(localctx, 2, csvParser.RULE_hdr);
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 18;
        this.row();
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};


function RowContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = csvParser.RULE_row;
    return this;
}

RowContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
RowContext.prototype.constructor = RowContext;

RowContext.prototype.field = function(i) {
    if(i===undefined) {
        i = null;
    }
    if(i===null) {
        return this.getTypedRuleContexts(FieldContext);
    } else {
        return this.getTypedRuleContext(FieldContext,i);
    }
};

RowContext.prototype.DELIMITER = function(i) {
	if(i===undefined) {
		i = null;
	}
    if(i===null) {
        return this.getTokens(csvParser.DELIMITER);
    } else {
        return this.getToken(csvParser.DELIMITER, i);
    }
};


RowContext.prototype.enterRule = function(listener) {
    if(listener instanceof csvListener ) {
        listener.enterRow(this);
	}
};

RowContext.prototype.exitRule = function(listener) {
    if(listener instanceof csvListener ) {
        listener.exitRow(this);
	}
};




csvParser.RowContext = RowContext;

csvParser.prototype.row = function() {

    var localctx = new RowContext(this, this._ctx, this.state);
    this.enterRule(localctx, 4, csvParser.RULE_row);
    var _la = 0; // Token type
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 20;
        this.field();
        this.state = 25;
        this._errHandler.sync(this);
        _la = this._input.LA(1);
        while(_la===csvParser.DELIMITER) {
            this.state = 21;
            this.match(csvParser.DELIMITER);
            this.state = 22;
            this.field();
            this.state = 27;
            this._errHandler.sync(this);
            _la = this._input.LA(1);
        }
        this.state = 29;
        this._errHandler.sync(this);
        _la = this._input.LA(1);
        if(_la===csvParser.T__0) {
            this.state = 28;
            this.match(csvParser.T__0);
        }

        this.state = 31;
        this.match(csvParser.T__1);
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};


function FieldContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = csvParser.RULE_field;
    return this;
}

FieldContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
FieldContext.prototype.constructor = FieldContext;

FieldContext.prototype.text = function() {
    return this.getTypedRuleContext(TextContext,0);
};

FieldContext.prototype.string = function() {
    return this.getTypedRuleContext(StringContext,0);
};

FieldContext.prototype.enterRule = function(listener) {
    if(listener instanceof csvListener ) {
        listener.enterField(this);
	}
};

FieldContext.prototype.exitRule = function(listener) {
    if(listener instanceof csvListener ) {
        listener.exitField(this);
	}
};




csvParser.FieldContext = FieldContext;

csvParser.prototype.field = function() {

    var localctx = new FieldContext(this, this._ctx, this.state);
    this.enterRule(localctx, 6, csvParser.RULE_field);
    try {
        this.state = 36;
        this._errHandler.sync(this);
        switch(this._input.LA(1)) {
        case csvParser.TEXT:
            this.enterOuterAlt(localctx, 1);
            this.state = 33;
            this.text();
            break;
        case csvParser.STRING:
            this.enterOuterAlt(localctx, 2);
            this.state = 34;
            this.string();
            break;
        case csvParser.T__0:
        case csvParser.T__1:
        case csvParser.DELIMITER:
            this.enterOuterAlt(localctx, 3);

            break;
        default:
            throw new antlr4.error.NoViableAltException(this);
        }
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};


function TextContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = csvParser.RULE_text;
    return this;
}

TextContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
TextContext.prototype.constructor = TextContext;

TextContext.prototype.TEXT = function() {
    return this.getToken(csvParser.TEXT, 0);
};

TextContext.prototype.enterRule = function(listener) {
    if(listener instanceof csvListener ) {
        listener.enterText(this);
	}
};

TextContext.prototype.exitRule = function(listener) {
    if(listener instanceof csvListener ) {
        listener.exitText(this);
	}
};




csvParser.TextContext = TextContext;

csvParser.prototype.text = function() {

    var localctx = new TextContext(this, this._ctx, this.state);
    this.enterRule(localctx, 8, csvParser.RULE_text);
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 38;
        this.match(csvParser.TEXT);
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};


function StringContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = csvParser.RULE_string;
    return this;
}

StringContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
StringContext.prototype.constructor = StringContext;

StringContext.prototype.STRING = function() {
    return this.getToken(csvParser.STRING, 0);
};

StringContext.prototype.enterRule = function(listener) {
    if(listener instanceof csvListener ) {
        listener.enterString(this);
	}
};

StringContext.prototype.exitRule = function(listener) {
    if(listener instanceof csvListener ) {
        listener.exitString(this);
	}
};




csvParser.StringContext = StringContext;

csvParser.prototype.string = function() {

    var localctx = new StringContext(this, this._ctx, this.state);
    this.enterRule(localctx, 10, csvParser.RULE_string);
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 40;
        this.match(csvParser.STRING);
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};


exports.csvParser = csvParser;
