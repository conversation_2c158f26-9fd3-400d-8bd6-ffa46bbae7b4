:root {

    --syntax-light-addition: #0f2593;
    --syntax-light-deletion: #101216;
    --syntax-light-name: #101216;
    --syntax-light-section: #101216;
    --syntax-light-selector-tag: #101216;
    --syntax-light-selector-attr: #000000;
    --syntax-light-selector-class: #000000;
    --syntax-light-selector-pseudo: #000000;
    --syntax-light-selector-id: #102bae;
    --syntax-light-subst: #101216;
    --syntax-light-literal: #7d86af;
    --syntax-light-attr: #000000;
    --syntax-light-number: #000000;
    --syntax-light-template-variable: #000000;
    --syntax-light-type: #6c7489;
    --syntax-light-variable: #20576d;
    --syntax-light-attribute: #1d3fe8;
    --syntax-light-meta: #1d3fe8;
    --syntax-light-string: #70757f;
    --syntax-light-regexp: #0f2593;
    --syntax-light-doctag: #101220;
    --syntax-light-formula: #101220;
    --syntax-light-keyword: #1629a1;
    --syntax-light-built_in: #1d3fe8;
    --syntax-light-class: #c52d2d;
    --syntax-light-titleclass: #c52d2d;
    --syntax-light-bullet: #001291;
    --syntax-light-link: #001291;
    --syntax-light-symbol: #001291;
    --syntax-light-title: #1d3fe8;
    --syntax-light-hljs: #838c95;

    --syntax-light-comment: #838c95;
    --syntax-light-quote: #838c95;
    --syntax-dark-addition: #adadad;
    --syntax-dark-deletion: #adadad;
    --syntax-dark-name: #78A9FF;
    --syntax-dark-section: #ffffff;
    --syntax-dark-selector-tag: #f7f7f7;
    --syntax-dark-selector-attr: #c5c9d9;
    --syntax-dark-selector-class: #9196b1;
    --syntax-dark-selector-pseudo: #c5c9d9;
    --syntax-dark-selector-id: #d9dbeb;
    --syntax-dark-subst: #9d9d9d;
    --syntax-dark-literal: #72b1ff;
    --syntax-dark-attr: #9EF0F0;
    --syntax-dark-number: #abb9cf;
    --syntax-dark-template-variable: #9db6d1;
    --syntax-dark-type: #c5c9d9;
    --syntax-dark-variable: #ff7eb6;
    --syntax-dark-attribute: #b6ef87;
    --syntax-dark-meta: #ffffff;
    --syntax-dark-string: #BE95FF;
    --syntax-dark-regexp: #b6ef87;
    --syntax-dark-doctag: #ffffff;
    --syntax-dark-formula: #ffffff;
    --syntax-dark-keyword: #BE95FF;
    --syntax-dark-built_in: #eaf2ff;
    --syntax-dark-class: #ff7eb6;
    --syntax-dark-titleclass: #ffffff;
    --syntax-dark-bullet: #81838f;
    --syntax-dark-link: #81838f;
    --syntax-dark-symbol: #81838f;
    --syntax-dark-title: #98F4F4;
    --syntax-dark-hljs: #6e767e;
    --syntax-dark-comment: #6e767e;
    --syntax-dark-quote: #6e767e;

}

/* LIGHT CODE BLOCKS //////////////////////////////////////////////////////////////*/
/*/////////////////////////////////////////////////////////////////////////////////*/

.hljs-addition { color: var(--syntax-light-addition) !important; }
.hljs-deletion { color: var(--syntax-light-deletion) !important; }
.hljs-name { color: var(--syntax-light-name) !important; }
.hljs-section { color: var(--syntax-light-section) !important; }
.hljs-selector-tag { color: var(--syntax-light-selector-tag) !important; }
.hljs-selector-attr { color: var(--syntax-light-selector-attr) !important; }
.hljs-selector-class { color: var(--syntax-light-selector-class) !important; }
.hljs-selector-pseudo { color: var(--syntax-light-selector-pseudo) !important; }
.hljs-selector-id { color: var(--syntax-light-selector-id) !important; }
.hljs-subst { color: var(--syntax-light-subst) !important; }
.hljs-literal { color: var(--syntax-light-literal) !important; }
.hljs-attr { color: var(--syntax-light-attr) !important; }
.hljs-number { color: var(--syntax-light-number) !important; }
.hljs-template-variable { color: var(--syntax-light-template-variable) !important; }
.hljs-type { color: var(--syntax-light-type) !important; }
.hljs-variable { color: var(--syntax-light-variable) !important; }
.hljs-attribute { color: var(--syntax-light-attribute) !important; }
.hljs-meta { color: var(--syntax-light-meta) !important; }
.hljs-string { color: var(--syntax-light-string) !important; }
.hljs-regexp { color: var(--syntax-light-regexp) !important; }
.hljs-doctag { color: var(--syntax-light-doctag) !important; }
.hljs-formula { color: var(--syntax-light-formula) !important; }
.hljs-keyword { color: var(--syntax-light-keyword) !important; }
.hljs-built_in { color: var(--syntax-light-built_in) !important; }
.hljs-class { color: var(--syntax-light-class) !important; }
.hljs-title.class_ { color: var(--syntax-light-titleclass) !important; }
.hljs-bullet { color: var(--syntax-light-bullet) !important; }
.hljs-link { color: var(--syntax-light-link) !important; }
.hljs-symbol { color: var(--syntax-light-symbol) !important; }
.hljs-title { color: var(--syntax-light-title) !important; }
.hljs { color: var(--syntax-light-hljs) !important; font-style: normal !important; }
.hljs-comment { color: var(--syntax-light-comment) !important; font-style: normal !important; }
.hljs-quote { color: var(--syntax-light-quote) !important; font-style: normal !important; }

/* DARK CODE BLOCKS ///////////////////////////////////////////////////////////////*/
/*/////////////////////////////////////////////////////////////////////////////////*/

/*
.hljs, .hljs code {
    color: #F8F9FA !important;
    background: #202124 !important;
}
.hljs-addition {            color: var(--syntax-dark-addition          ) !important; }
.hljs-deletion {            color: var(--syntax-dark-deletion          ) !important; }
.hljs-name {                color: var(--syntax-dark-name              ) !important; }
.hljs-section {             color: var(--syntax-dark-section           ) !important; }
.hljs-selector-tag {        color: var(--syntax-dark-selector-tag      ) !important; }
.hljs-selector-attr {       color: var(--syntax-dark-selector-attr     ) !important; }
.hljs-selector-class {      color: var(--syntax-dark-selector-class    ) !important; }
.hljs-selector-pseudo {     color: var(--syntax-dark-selector-pseudo   ) !important; }
.hljs-selector-id {         color: var(--syntax-dark-selector-id       ) !important; }
.hljs-subst {               color: var(--syntax-dark-subst             ) !important; }
.hljs-literal {             color: var(--syntax-dark-literal           ) !important; }
.hljs-attr {                color: var(--syntax-dark-attr              ) !important; }
.hljs-number {              color: var(--syntax-dark-number            ) !important; }
.hljs-template-variable {   color: var(--syntax-dark-template-variable ) !important; }
.hljs-type {                color: var(--syntax-dark-type              ) !important; }
.hljs-variable {            color: var(--syntax-dark-variable          ) !important; }
.hljs-attribute {           color: var(--syntax-dark-attribute         ) !important; }
.hljs-meta {                color: var(--syntax-dark-meta              ) !important; }
.hljs-string {              color: var(--syntax-dark-string            ) !important; }
.hljs-regexp {              color: var(--syntax-dark-regexp            ) !important; }
.hljs-doctag {              color: var(--syntax-dark-doctag            ) !important; }
.hljs-formula {             color: var(--syntax-dark-formula           ) !important; }
.hljs-keyword {             color: var(--syntax-dark-keyword           ) !important; }
.hljs-built_in {            color: var(--syntax-dark-built_in          ) !important; }
.hljs-class {               color: var(--syntax-dark-class             ) !important; }
.hljs-title.class_ {        color: var(--syntax-dark-titleclass        ) !important; }
.hljs-bullet {              color: var(--syntax-dark-bullet            ) !important; }
.hljs-link {                color: var(--syntax-dark-link              ) !important; }
.hljs-symbol {              color: var(--syntax-dark-symbol            ) !important; }
.hljs-title {               color: var(--syntax-dark-title             ) !important; }
.hljs { color: var(--syntax-dark-hljs) !important; font-style:normal !important; }
.hljs-comment { color: var(--syntax-dark-comment) !important; font-style:normal !important; }
.hljs-quote { color: var(--syntax-dark-quote) !important; font-style:normal !important; }
*/