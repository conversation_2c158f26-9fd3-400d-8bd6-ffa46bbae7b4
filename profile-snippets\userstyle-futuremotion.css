:root {
   --fm-caption-padding-topbot: 0.125rem;
   --fm-callout-blue-sidecolor: #2d6de2;
   --fm-callout-default-color: #747a88;
   --fm-callout-default-sidecolor: #9c9fa8;
   --fm-callout-default-bgcolor: #fbfbfb;
   --fm-callout-default-titlecolor: #757e8f;
   --fm-callout-light-gray-color: #888d99;
   --fm-callout-light-gray-sidecolor: #b2b5bd;
   --fm-callout-light-gray-bgcolor: #fcfcfc;
   --fm-callout-light-gray-titlecolor: #6a707b;
   --fm-callout-yellow-color: #9f9179;
   --fm-callout-yellow-sidecolor: #b1a053;
   --fm-callout-yellow-bgcolor: #fff3cd;
   --fm-callout-yellow-titlecolor: #776a55;
   --fm-callout-blue1-color: #7381a0;
   --fm-callout-blue1-sidecolor: #2d6de2;
   --fm-callout-blue1-bgcolor: #e3ecfc;
   --fm-callout-blue1-titlecolor: #4172ce;
   --fm-callout-blue2-color: #7381a0;
   --fm-callout-blue2-sidecolor: #2d6de2;
   --fm-callout-blue2-bgcolor: #e3ecfc;
   --fm-callout-blue2-titlecolor: #4172ce;

   --fm-inlinecode-color-border: #b4b4b4;
   --fm-inlinecode-margin: 2px 2px;
   --fm-inlinecode-border: 1px solid var(--fm-inlinecode-color-border);
   --fm-inlinecode-border-radius-default: 3px;
   --fm-inlinecode-color-background: #FBFBFB;
   --fm-inlinecode-padding: 3px 6px 3px 6px;
   --fm-inlinecode-color-default: #6a6f73;
   --fm-inlinecode-font-size: 12.4px;

   --fm-enhanced-link-default-font-size: 15px;
   --fm-enhanced-link-large-font-size: 16px;
   --fm-enhanced-link-xlarge-font-size: 20px;

   --font-stack-neuehaas-display: "Neue Haas Grotesk Display Pro", "Haas Grot Disp", "Helvetica Now Text", "SF Pro Text", sans-serif;
   --font-stack-neuehaas-text: "Haas Grot Text", "Helvetica Now Display", "SF Pro Display", sans-serif;
   --font-stack-helvnow: "Helvetica Now Text", Helvetica, "Helvetica LT Pro", "SF Pro Text", Arial, sans-serif;
   --font-stack-helvnowdisplay: "Helvetica Now Display", "SF Pro Display", "Haas Grot Disp", "Helvetica Neue LT Pro", "Helvetica LT Pro", sans-serif;
   --font-stack-helvnowdisplay-med: "Helvetica Now Display Medium", "SF Pro Display Medium", "SF Pro Display", "Helvetica Neue LT Pro", "Helvetica LT Pro", sans-serif;
   --font-stack-helvnowmed: "Helvetica Now Text Medium", "Helvetica Now Text", Helvetica, "Helvetica LT Pro", "SF Pro Text", Arial, sans-serif;
   --font-stack-inter: Inter, Roboto, "Helvetica Now Text", Helvetica, Arial, sans-serif;
   --font-stack-roboto: Roboto, "Roboto Flex", "Helvetica LT Pro", Helvetica, "SF Pro Text", Arial, sans-serif;
   --font-stack-helv: "Helvetica", "Helvetica LT Pro", "SF Pro Text", "Arial", sans-serif;
   --font-stack-sfpro: "SF Pro Display", "Helvetica Now Text", "Helvetica LT Pro", "Arial", sans-serif;
   --font-stack-robotomono: "Roboto Mono", "SF Mono", "Input Mono", Input, monospace;
   --font-stack-inputmono: "Input Mono", Input, "Roboto Mono", "SF Mono", monospace;
   --font-stack-inputmonomedium: "Input Mono Medium", "Input Mono", Input, "Roboto Mono", "SF Mono", monospace;
   --font-stack-sfmono: "SF Mono", "Input Mono", Input, "Roboto Mono", "SF Mono", monospace;
}

/* fm-admonition /////////////////////////////////////////////////////////////////////////////////*/
/*////////////////////////////////////////////////////////////////////////////////////////////////*/

span.fm-admonition {
   font-family: var(--font-stack-roboto);
   display: block;
   background-color: #f1f7ff;
   border: 2px solid #7894df;
   border-radius: .5rem;
   box-sizing: border-box;
   color: hsl(224deg 61.47% 45.49%);
   font-size: 16px;
   line-height: 1.6;
   padding: 1rem;
   position: relative;
   letter-spacing: .2px;

   .title {
      align-items: center;
      border: 0 solid #e5e7eb;
      box-sizing: border-box;
      color: #123fba;
      display: flex;
      font-size: 17px;
      font-weight: 600;
      line-height: 1.5;
      text-transform: none;
      margin-bottom: 8px !important;

      &::before {
         content: "";
         margin-right: 0.5rem;
         display: inline-block;
         width: 1.25rem;
         height: 1.25rem;
         background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="rgb(29 78 216)" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"/><line x1="12" y1="16" x2="12" y2="12"/><line x1="12" y1="8" x2="12" y2="8"/></svg>');
      }
   }

   .body {
      font-size: 16px;
      font-family: var(--font-stack-roboto);
      font-weight: 400;
   }
}


span.fm-button-container {
   display: inline-block;
   min-height: 26px;
   padding: 9px;
   padding-left: 12px;
   margin-bottom: 4px;
   margin-top: 4px;
   width: var(--default-body-max-width);
   font-size: 14px;
   font-family: var(--font-stack-sfpro);
   font-weight: 400;
   position: relative;
   border-radius: 10px;
   border-collapse: collapse;
   box-sizing: border-box;
   border: 2px solid #dedede;
   background-color: #ffffff;
   &.gray { border: 2px solid #eeeeee; background-color: #eeeeee; }
   &.blue { border: 2px solid #e3eaff; background-color: #e3eaff; }
   &.blue-outline { border: 2px solid #2b5ae6; background-color: #ffffff; }
}

span.fm-button {
   display: inline-block;
   min-height: 15px;
   border: 2px solid #c3c5cd;
   background-color: #f9f9f9;
   font-size: var(--fm-enhanced-link-default-font-size);
   font-family: var(--font-stack-sfpro);
   font-weight: 400;
   padding: 3px 10px 3.1px 7px;
   position: relative;
   cursor: pointer;
   text-decoration: none;
   align-content: center;
   border-radius: 5px;
   margin-bottom: 6px;
   margin-top: 6px;
   margin-right: 8px;
   margin-left: 0px;
   border-collapse: collapse;
   letter-spacing: .4px;
   a { color: #535e6e !important; }
   &.arrowbefore { ::before { content: "➔"; padding-right: 7px; } }
   &.arrowafter { ::after { content: "➔"; padding-left: 7px; } }
   &:hover {
      border: 2px solid #999ca6;
      background-color: #ffffff;
      text-decoration: none;
      a { color: #2e343e !important; }
   }

   &.white {
      border: 2px solid #b2b8c0;
      background-color: #FFFFFF;
      color: #252629 !important;
      a { color: #414448 !important; }
      &:hover {
         border: 2px solid #7a7f87;
         background-color: #FFFFFF;
         a { color: #252629 !important; }
      }
      &::before { color: #767b82; fill: #767b82; padding-right: 7px; }
   }

   &.darkgray {
      border: 2px solid #666f7a;
      background-color: #666f7a;
      color: #fff !important;
      a { color: #fff !important; }
      &:hover {
         border: 2px solid #424d5a;
         background-color: #424d5a;
         a { color: #f0f0f0 !important; }
      }
      &::before { color: #ffffff; fill: #ffffff; padding-right: 7px; }
   }

   &.lightblue {
      border: 2px solid #304bcf;
      background-color: #f0f4ff;
      color: #304bcf !important;
      a {
         color: #304bcf !important;
      }
      &:hover {
         border: 2px solid #1d33a3;
         background-color: #f4f7ff;
         a { color: #122791 !important; }
      }
      &::before { color: #304bcf; fill: #304bcf; padding-right: 7px; }
   }

   &.blue {
      border: 2px solid #3857f0;
      background-color: #3857f0;
      color: #f5f6fd !important;
      a { color: #f5f6fd !important; }
      &:hover {
         border: 2px solid #223ab5;
         background-color: #223ab5;
         a { color: #ffffff !important; }
      }
      &::before { color: #ffffff; fill: #ffffff; padding-right: 7px; }
   }

   &.blue-outline {
      border: 2px solid #304bcf;
      background-color: #ffffff;
      color: #0e30d9 !important;
      a { color: #0e30d9 !important; }
      &:hover {
         border: 2px solid #031fa8;
         a { color: #0722a6 !important; }
      }
      &::before { color: #ffffff; fill: #ffffff; padding-right: 7px; }
   }

   &.large {
      border-width: 2px !important;
      padding: 3px 14px 3px 14px;
      min-height: 32px;
      font-size: var(--fm-enhanced-link-large-font-size);
      font-weight: 400;
      letter-spacing: -.1px;
      &:hover { border-width: 2px !important; }
      &::before { font-size: 19px; padding-right: 7px; }
   }

   &.xlarge {
      border-width: 3px !important;
      padding: 3px 15px 3px 15px;
      min-height: 36px;
      font-size: var(--fm-enhanced-link-xlarge-font-size);
      font-weight: 500;
      letter-spacing: .1px;
      &:hover { border-width: 3px !important; }
      &.arrow {
         ::before {
            font-size: 23px;
            padding-right: 7px !important;
            padding-left: 0px !important;
            position: relative;
            left: -4px !important;
         }
      }
   }
   &.font-helvnow { font-family: var(--font-stack-helvnow); }
   &.font-roboto { font-family: var(--font-stack-roboto); }
   &.font-inter { font-family: var(--font-stack-inter); }
}

span.fm-introtext {

   display: inline-block;
   margin: 8px 0px 8px 0px !important;
   border: none !important;
   color: #8d9198 !important;
   font-weight: 380 !important;
   font-size: 1.62rem !important;
   line-height: 2.2rem !important;
   font-family: var(--font-stack-sfpro) !important;
   letter-spacing: .2px !important;

   a {
      display: inline-flex;
      border: 2px solid #bec5fc !important;
      border-radius: 5px !important;
      padding: 2px 7px 3px 7px !important;
      min-height: 16px !important;
      height: 18px;
      align-content: center !important;
      align-items: center !important;
      background-color: #f1f7ff !important;
      font-size: 17px;
      position: relative;
      top: -1px;
      letter-spacing: .2px;
      margin-right: 2px;
      margin-left: 2px;
      &:hover {
         border: 2px solid #b2b2fd;
         color: #2e2ed9 !important;
      }
   }

   code.inline-code:not(.fm-notebox) {
      display: inline-flex !important;
      min-height: 20px !important;
      align-items: center !important;
      border: 2px solid #CCC !important;
      font-size: 15px !important;
      font-weight: 400 !important;
      padding: 1px 6px !important;
      font-family: var(--font-stack-sfmono) !important;
      position: relative !important;
      top: -1px !important;
      letter-spacing: -.2px !important;
   }
}

p:has(.fm-introtext) {
   margin: 18px 0px 18px 0px !important;
}

/* FM NOTEBOX ////////////////////////////////////////////////////////////////////////////////////*/
/*////////////////////////////////////////////////////////////////////////////////////////////////*/

span.fm-notebox {
   display: -webkit-box;
   display: -ms-flexbox;
   display: flex;
   position: relative;
   box-sizing: border-box;
   margin: 20px 0px 20px 0px;
   border-radius: 10px;
   background-color: #F0F0F2;
   padding: 0;
   font-size: 14px;

   span.symbol {
      box-sizing: border-box;
      background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='20' height='20' fill='none' viewBox='0 0 20 20'%3E%3Cpath fill='%23fff' d='M9 15h2V9H9v6Zm1-8a.968.968 0 0 0 .713-.288A.964.964 0 0 0 11 6a.965.965 0 0 0-.288-.712A.972.972 0 0 0 10 5a.965.965 0 0 0-.712.288A.972.972 0 0 0 9 6c0 .283.096.521.288.713.192.192.43.288.712.287Zm0 13a9.733 9.733 0 0 1-3.9-.788 10.114 10.114 0 0 1-3.175-2.137c-.9-.9-1.612-1.958-2.137-3.175A9.755 9.755 0 0 1 0 10a9.74 9.74 0 0 1 .788-3.9 10.114 10.114 0 0 1 2.137-3.175c.9-.9 1.958-1.612 3.175-2.137A9.755 9.755 0 0 1 10 0a9.74 9.74 0 0 1 3.9.788 10.114 10.114 0 0 1 3.175 2.137c.9.9 1.613 1.958 2.138 3.175A9.72 9.72 0 0 1 20 10a9.733 9.733 0 0 1-.788 3.9 10.114 10.114 0 0 1-2.137 3.175c-.9.9-1.958 1.613-3.175 2.138A9.72 9.72 0 0 1 10 20Z'/%3E%3C/svg%3E");
      width: 45px;
      -ms-flex-negative: 0;
      flex-shrink: 0;
      border-top-left-radius: 10px;
      border-bottom-left-radius: 10px;
      background-position: center 13px;
      background-repeat: no-repeat;
      background-color: var(--doc-box-note, #999);
   }

   span.content-wrapper {

      display: block;
      box-sizing: border-box;
      padding: 16px;
      width: 100%;

      span.title {
         display: block;
         box-sizing: border-box;
         margin: 0px !important;
         margin-block-start: 0em;
         margin-block-end: 0em;
         font-weight: 500;
         font-size: 16px !important;
         line-height: 1;
         font-family: var(--font-stack-helvnow);
      }

      span.title-icon {
         display: block;
         box-sizing: border-box;
         margin: 0px !important;
         margin-block-start: 0em;
         margin-block-end: 0em;
         font-weight: 500;
         font-size: 17.5px !important;
         line-height: 1;
         font-family: var(--font-stack-helvnow);

         &::before {
            position: relative;
            top: -1px;
            vertical-align: middle !important;
            filter: invert(0%) sepia(20%) saturate(0%) hue-rotate(237deg) brightness(70%) contrast(107%);
            cursor: pointer;
            margin-right: 10px !important;
            background-size: 14px 14px !important;
            pointer-events: all;
            content: "\f05a";
            color: #5b5d63 !important;
            font-size: 22px !important;
            font-family: "Font Awesome 6 Sharp Regular";
         }
      }

      span.content {
         display: block;
         margin-top: 14px;
         color: #55595f;
         font-weight: 400;
         font-size: 14.4px;
         line-height: 1.4rem;
         font-family: var(--font-stack-roboto);
         letter-spacing: 0.1px;

         .fm-button {
            margin: 12px 8px 0px 0px;
         }

         code {
            display         : inline !important;
            position        : relative !important;
            vertical-align  : middle !important;
            margin          : var(--fm-inlinecode-margin) !important;
            border          : var(--fm-inlinecode-border) !important;
            border-radius   : var(--fm-inlinecode-border-radius-default) !important;
            background-color: var(--fm-inlinecode-color-background) !important;
            padding         : var(--fm-inlinecode-padding) !important;
            color           : var(--fm-inlinecode-color-default) !important;
            font-size       : var(--fm-inlinecode-font-size) !important;
            font-family     : var(--font-stack-inputmono) !important;
            top             : -1px !important;
         }

         span.bordered-link {
            display: inline-flex !important;
            position: relative !important;
            align-items: center;
            box-sizing: border-box !important;
            margin-right: 1px !important;
            margin-left: 1px !important;
            border-radius: 4px !important;
            background-color: #ebefff !important;
            padding: 3px 5px 3px 5px !important;
            height: 23px !important;
            border: 1px solid #adb5e6 !important;

            &:hover {
               border: 1px solid #808acd !important;
               background-color: #f5f7ff !important;
            }

            a {
               color: #3651fa !important;
               font-family: var(--font-stack-roboto) !important;
               letter-spacing: .1px !important;
               font-weight: 500;
               font-size: 14.4px !important;
               text-decoration: none !important;
               &:hover {
                  color: #1127b6 !important;
                  text-decoration: none !important;
               }
            }
         }
         a:not(.fm-button *) {
            font-family: var(--font-stack-roboto) !important;
            color: #3651fa !important;
            font-weight: 500;
            font-size: 14.4px !important;
            text-decoration: none !important;

            &:hover {
               color: #1127b6 !important;
               text-decoration: none !important;
            }
         }
      }
   }
}
