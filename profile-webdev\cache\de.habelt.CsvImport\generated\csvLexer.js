// Generated from csv.g4 by ANTLR 4.8
// jshint ignore: start
var antlr4 = require('antlr4/index');


	let delimiter = ',';
	
	function createCsvLexer(input, delim)
	{
		const inst = new csvLexer(input);
		delimiter = delim;
		return inst;
	}
	
	function isDelimiter(delim)
	{
		return delimiter == delim;
	}

	exports.createCsvLexer = createCsvLexer;
	exports.isDelimiter = isDelimiter;



var serializedATN = ["\u0003\u608b\ua72a\u8133\ub9ed\u417c\u3be7\u7786\u5964",
    "\u0002\u00073\b\u0001\u0004\u0002\t\u0002\u0004\u0003\t\u0003\u0004",
    "\u0004\t\u0004\u0004\u0005\t\u0005\u0004\u0006\t\u0006\u0004\u0007\t",
    "\u0007\u0003\u0002\u0003\u0002\u0003\u0003\u0003\u0003\u0003\u0004\u0003",
    "\u0004\u0003\u0004\u0003\u0004\u0003\u0004\u0003\u0004\u0005\u0004\u001a",
    "\n\u0004\u0003\u0005\u0003\u0005\u0003\u0005\u0003\u0005\u0003\u0005",
    "\u0003\u0005\u0003\u0005\u0006\u0005#\n\u0005\r\u0005\u000e\u0005$\u0003",
    "\u0006\u0003\u0006\u0003\u0006\u0003\u0006\u0007\u0006+\n\u0006\f\u0006",
    "\u000e\u0006.\u000b\u0006\u0003\u0006\u0003\u0006\u0003\u0007\u0003",
    "\u0007\u0002\u0002\b\u0003\u0003\u0005\u0004\u0007\u0005\t\u0006\u000b",
    "\u0007\r\u0002\u0003\u0002\u0004\u0007\u0002\u000b\f\u000f\u000f$$.",
    ".==\u0003\u0002$$\u00029\u0002\u0003\u0003\u0002\u0002\u0002\u0002\u0005",
    "\u0003\u0002\u0002\u0002\u0002\u0007\u0003\u0002\u0002\u0002\u0002\t",
    "\u0003\u0002\u0002\u0002\u0002\u000b\u0003\u0002\u0002\u0002\u0003\u000f",
    "\u0003\u0002\u0002\u0002\u0005\u0011\u0003\u0002\u0002\u0002\u0007\u0019",
    "\u0003\u0002\u0002\u0002\t\"\u0003\u0002\u0002\u0002\u000b&\u0003\u0002",
    "\u0002\u0002\r1\u0003\u0002\u0002\u0002\u000f\u0010\u0007\u000f\u0002",
    "\u0002\u0010\u0004\u0003\u0002\u0002\u0002\u0011\u0012\u0007\f\u0002",
    "\u0002\u0012\u0006\u0003\u0002\u0002\u0002\u0013\u0014\u0007.\u0002",
    "\u0002\u0014\u001a\u0006\u0004\u0002\u0002\u0015\u0016\u0007=\u0002",
    "\u0002\u0016\u001a\u0006\u0004\u0003\u0002\u0017\u0018\u0007\u000b\u0002",
    "\u0002\u0018\u001a\u0006\u0004\u0004\u0002\u0019\u0013\u0003\u0002\u0002",
    "\u0002\u0019\u0015\u0003\u0002\u0002\u0002\u0019\u0017\u0003\u0002\u0002",
    "\u0002\u001a\b\u0003\u0002\u0002\u0002\u001b#\n\u0002\u0002\u0002\u001c",
    "\u001d\u0007.\u0002\u0002\u001d#\u0006\u0005\u0005\u0002\u001e\u001f",
    "\u0007=\u0002\u0002\u001f#\u0006\u0005\u0006\u0002 !\u0007\u000b\u0002",
    "\u0002!#\u0006\u0005\u0007\u0002\"\u001b\u0003\u0002\u0002\u0002\"\u001c",
    "\u0003\u0002\u0002\u0002\"\u001e\u0003\u0002\u0002\u0002\" \u0003\u0002",
    "\u0002\u0002#$\u0003\u0002\u0002\u0002$\"\u0003\u0002\u0002\u0002$%",
    "\u0003\u0002\u0002\u0002%\n\u0003\u0002\u0002\u0002&,\u0005\r\u0007",
    "\u0002\'(\u0007$\u0002\u0002(+\u0007$\u0002\u0002)+\n\u0003\u0002\u0002",
    "*\'\u0003\u0002\u0002\u0002*)\u0003\u0002\u0002\u0002+.\u0003\u0002",
    "\u0002\u0002,*\u0003\u0002\u0002\u0002,-\u0003\u0002\u0002\u0002-/\u0003",
    "\u0002\u0002\u0002.,\u0003\u0002\u0002\u0002/0\u0005\r\u0007\u00020",
    "\f\u0003\u0002\u0002\u000212\u0007$\u0002\u00022\u000e\u0003\u0002\u0002",
    "\u0002\b\u0002\u0019\"$*,\u0002"].join("");


var atn = new antlr4.atn.ATNDeserializer().deserialize(serializedATN);

var decisionsToDFA = atn.decisionToState.map( function(ds, index) { return new antlr4.dfa.DFA(ds, index); });

function csvLexer(input) {
	antlr4.Lexer.call(this, input);
    this._interp = new antlr4.atn.LexerATNSimulator(this, atn, decisionsToDFA, new antlr4.PredictionContextCache());


    return this;
}

csvLexer.prototype = Object.create(antlr4.Lexer.prototype);
csvLexer.prototype.constructor = csvLexer;

Object.defineProperty(csvLexer.prototype, "atn", {
        get : function() {
                return atn;
        }
});

csvLexer.EOF = antlr4.Token.EOF;
csvLexer.T__0 = 1;
csvLexer.T__1 = 2;
csvLexer.DELIMITER = 3;
csvLexer.TEXT = 4;
csvLexer.STRING = 5;

csvLexer.prototype.channelNames = [ "DEFAULT_TOKEN_CHANNEL", "HIDDEN" ];

csvLexer.prototype.modeNames = [ "DEFAULT_MODE" ];

csvLexer.prototype.literalNames = [ null, "'\r'", "'\n'" ];

csvLexer.prototype.symbolicNames = [ null, null, null, "DELIMITER", "TEXT", 
                                     "STRING" ];

csvLexer.prototype.ruleNames = [ "T__0", "T__1", "DELIMITER", "TEXT", "STRING", 
                                 "QUOTE" ];

csvLexer.prototype.grammarFileName = "csv.g4";

csvLexer.prototype.sempred = function(localctx, ruleIndex, predIndex) {
	switch (ruleIndex) {
		case 2:
			return this.DELIMITER_sempred(localctx, predIndex);
		case 3:
			return this.TEXT_sempred(localctx, predIndex);
    	default:
    		throw "No registered predicate for:" + ruleIndex;
    }
};

csvLexer.prototype.DELIMITER_sempred = function(localctx, predIndex) {
	switch(predIndex) {
		case 0:
			return  isDelimiter(',') ;
		case 1:
			return  isDelimiter(';') ;
		case 2:
			return  isDelimiter('\t') ;
		default:
			throw "No predicate with index:" + predIndex;
	}
};

csvLexer.prototype.TEXT_sempred = function(localctx, predIndex) {
	switch(predIndex) {
		case 3:
			return  !isDelimiter(',') ;
		case 4:
			return  !isDelimiter(';') ;
		case 5:
			return  !isDelimiter('\t') ;
		default:
			throw "No predicate with index:" + predIndex;
	}
};



exports.csvLexer = csvLexer;

