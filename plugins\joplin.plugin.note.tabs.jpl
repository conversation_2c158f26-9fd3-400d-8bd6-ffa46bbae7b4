index.js                                                                                            000644                  0000077273  14104541314 007366  0                                                                                                    ustar 00                                                                000000  000000                                                                                                                                                                         !function(e){var t={};function i(o){if(t[o])return t[o].exports;var n=t[o]={i:o,l:!1,exports:{}};return e[o].call(n.exports,n,n.exports,i),n.l=!0,n.exports}i.m=e,i.c=t,i.d=function(e,t,o){i.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:o})},i.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},i.t=function(e,t){if(1&t&&(e=i(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var o=Object.create(null);if(i.r(o),Object.defineProperty(o,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var n in e)i.d(o,n,function(t){return e[t]}.bind(null,n));return o},i.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return i.d(t,"a",t),t},i.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},i.p="",i(i.s=4)}([function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=joplin},function(e,t,i){"use strict";var o;Object.defineProperty(t,"__esModule",{value:!0}),t.ContentScriptType=t.SettingStorage=t.AppType=t.SettingItemType=t.ToolbarButtonLocation=t.isContextMenuItemLocation=t.MenuItemLocation=t.ImportModuleOutputFormat=t.FileSystemItem=void 0,function(e){e.File="file",e.Directory="directory"}(t.FileSystemItem||(t.FileSystemItem={})),function(e){e.Markdown="md",e.Html="html"}(t.ImportModuleOutputFormat||(t.ImportModuleOutputFormat={})),function(e){e.File="file",e.Edit="edit",e.View="view",e.Note="note",e.Tools="tools",e.Help="help",e.Context="context",e.NoteListContextMenu="noteListContextMenu",e.EditorContextMenu="editorContextMenu",e.FolderContextMenu="folderContextMenu",e.TagContextMenu="tagContextMenu"}(o=t.MenuItemLocation||(t.MenuItemLocation={})),t.isContextMenuItemLocation=function(e){return[o.Context,o.NoteListContextMenu,o.EditorContextMenu,o.FolderContextMenu,o.TagContextMenu].includes(e)},function(e){e.NoteToolbar="noteToolbar",e.EditorToolbar="editorToolbar"}(t.ToolbarButtonLocation||(t.ToolbarButtonLocation={})),function(e){e[e.Int=1]="Int",e[e.String=2]="String",e[e.Bool=3]="Bool",e[e.Array=4]="Array",e[e.Object=5]="Object",e[e.Button=6]="Button"}(t.SettingItemType||(t.SettingItemType={})),function(e){e.Desktop="desktop",e.Mobile="mobile",e.Cli="cli"}(t.AppType||(t.AppType={})),function(e){e[e.Database=1]="Database",e[e.File=2]="File"}(t.SettingStorage||(t.SettingStorage={})),function(e){e.MarkdownItPlugin="markdownItPlugin",e.CodeMirrorPlugin="codeMirrorPlugin"}(t.ContentScriptType||(t.ContentScriptType={}))},function(e,t,i){"use strict";var o,n=this&&this.__awaiter||function(e,t,i,o){return new(i||(i=Promise))((function(n,s){function a(e){try{r(o.next(e))}catch(e){s(e)}}function d(e){try{r(o.throw(e))}catch(e){s(e)}}function r(e){var t;e.done?n(e.value):(t=e.value,t instanceof i?t:new i((function(e){e(t)}))).then(a,d)}r((o=o.apply(e,t||[])).next())}))};Object.defineProperty(t,"__esModule",{value:!0}),t.NoteTabs=t.NoteTabType=void 0,function(e){e[e.Temporary=1]="Temporary",e[e.Open=2]="Open",e[e.Pinned=3]="Pinned"}(o=t.NoteTabType||(t.NoteTabType={}));t.NoteTabs=class{constructor(e){this._settings=e}get tabs(){return this._settings.noteTabs}get length(){return this.tabs.length}get indexOfTemp(){return this.tabs.findIndex(e=>e.type===o.Temporary)}store(){return n(this,void 0,void 0,(function*(){yield this._settings.storeTabs()}))}indexOutOfBounds(e){return e<0||e>=this.length}static isTemporary(e){return!!e&&e.type===o.Temporary}static isPinned(e){return!!e&&e.type===o.Pinned}get(e){if(!this.indexOutOfBounds(e))return this.tabs[e]}indexOf(e){return this.tabs.findIndex(t=>t.id===e)}hasTab(e){return void 0!==this.tabs.find(t=>t.id===e)}add(e,t,i){return n(this,void 0,void 0,(function*(){if(void 0===e||void 0===t)return;const o=this.indexOf(i),n={id:e,type:t};o>=0?this.tabs.splice(o,0,n):this.tabs.push(n),yield this.store()}))}moveWithIndex(e,t){return n(this,void 0,void 0,(function*(){if(this.indexOutOfBounds(e))return;if(this.indexOutOfBounds(t))return;const i=this.tabs[e];this.tabs.splice(e,1),this.tabs.splice(0==t?0:t,0,i),yield this.store()}))}moveWithId(e,t){return n(this,void 0,void 0,(function*(){const i=t?this.indexOf(t):this.length-1;yield this.moveWithIndex(this.indexOf(e),i)}))}changeType(e,t){return n(this,void 0,void 0,(function*(){const i=this.indexOf(e);i>=0&&(this.tabs[i].type=t,yield this.store())}))}replaceTemp(e){return n(this,void 0,void 0,(function*(){if(void 0===e)return;const t=this.indexOfTemp;t>=0&&(this.tabs[t].id=e,yield this.store())}))}delete(e){return n(this,void 0,void 0,(function*(){const t=this.indexOf(e);t>=0&&(this.tabs.splice(t,1),yield this.store())}))}}},function(e,t,i){"use strict";var o=this&&this.__awaiter||function(e,t,i,o){return new(i||(i=Promise))((function(n,s){function a(e){try{r(o.next(e))}catch(e){s(e)}}function d(e){try{r(o.throw(e))}catch(e){s(e)}}function r(e){var t;e.done?n(e.value):(t=e.value,t instanceof i?t:new i((function(e){e(t)}))).then(a,d)}r((o=o.apply(e,t||[])).next())}))};Object.defineProperty(t,"__esModule",{value:!0}),t.Settings=t.LayoutMode=t.UnpinBehavior=t.AddBehavior=void 0;const n=i(0),s=i(1);var a,d,r,l;!function(e){e.Default="default",e.FontFamily="var(--joplin-font-family)",e.FontSize="var(--joplin-font-size)",e.Background="var(--joplin-background-color3)",e.HoverBackground="var(--joplin-background-color-hover3)",e.Foreground="var(--joplin-color-faded)",e.ActiveBackground="var(--joplin-background-color)",e.ActiveForeground="var(--joplin-color)",e.DividerColor="var(--joplin-divider-color)"}(a||(a={})),function(e){e[e.Temporary=0]="Temporary",e[e.Pinned=1]="Pinned"}(d=t.AddBehavior||(t.AddBehavior={})),function(e){e[e.Keep=0]="Keep",e[e.LastActive=1]="LastActive",e[e.LeftTab=2]="LeftTab",e[e.RightTab=3]="RightTab"}(r=t.UnpinBehavior||(t.UnpinBehavior={})),function(e){e[e.Auto=0]="Auto",e[e.Horizontal=1]="Horizontal",e[e.Vertical=2]="Vertical"}(l=t.LayoutMode||(t.LayoutMode={}));t.Settings=class{constructor(){this._noteTabs=new Array,this._enableDragAndDrop=!0,this._showTodoCheckboxes=!1,this._showBreadcrumbs=!1,this._showNavigationButtons=!1,this._showChecklistStatus=!1,this._pinEditedNotes=!1,this._unpinCompletedTodos=!1,this._addBehavior=d.Temporary,this._unpinBehavior=r.Keep,this._layoutMode=l.Auto,this._tabHeight=35,this._minTabWidth=50,this._maxTabWidth=150,this._breadcrumbsMaxWidth=100,this._fontFamily=a.Default,this._fontSize=a.Default,this._background=a.Default,this._hoverBackground=a.Default,this._actBackground=a.Default,this._breadcrumbsBackground=a.Default,this._foreground=a.Default,this._actForeground=a.Default,this._dividerColor=a.Default,this._defaultRegExp=new RegExp(a.Default,"i")}get noteTabs(){return this._noteTabs}get enableDragAndDrop(){return this._enableDragAndDrop}get showTodoCheckboxes(){return this._showTodoCheckboxes}get showBreadcrumbs(){return this._showBreadcrumbs}get showNavigationButtons(){return this._showNavigationButtons}get showChecklistStatus(){return this._showChecklistStatus}get pinEditedNotes(){return this._pinEditedNotes}get unpinCompletedTodos(){return this._unpinCompletedTodos}hasAddBehavior(e){return this._addBehavior===e}get unpinBehavior(){return this._unpinBehavior}hasLayoutMode(e){return this._layoutMode===e}get tabHeight(){return this._tabHeight}get minTabWidth(){return this._minTabWidth}get maxTabWidth(){return this._maxTabWidth}get breadcrumbsMaxWidth(){return this._breadcrumbsMaxWidth}get fontFamily(){return this._fontFamily}get fontSize(){return this._fontSize}get background(){return this._background}get hoverBackground(){return this._hoverBackground}get actBackground(){return this._actBackground}get breadcrumbsBackground(){return this._breadcrumbsBackground}get foreground(){return this._foreground}get actForeground(){return this._actForeground}get dividerColor(){return this._dividerColor}get showCompletedTodos(){return n.default.settings.globalValue("showCompletedTodos")}register(){return o(this,void 0,void 0,(function*(){yield n.default.settings.registerSection("note.tabs.settings",{label:"Note Tabs",iconName:"fas fa-window-maximize"}),yield n.default.settings.registerSettings({noteTabs:{value:[],type:s.SettingItemType.Array,section:"note.tabs.settings",public:!1,label:"Note tabs"},enableDragAndDrop:{value:this._enableDragAndDrop,type:s.SettingItemType.Bool,section:"note.tabs.settings",public:!0,label:"Enable drag & drop of tabs",description:"If disabled, position of tabs can be change via commands or move buttons."},showTodoCheckboxes:{value:this._showTodoCheckboxes,type:s.SettingItemType.Bool,section:"note.tabs.settings",public:!0,label:"Show to-do checkboxes on tabs",description:"If enabled, to-dos can be completed directly on the tabs."},showBreadcrumbs:{value:this._showBreadcrumbs,type:s.SettingItemType.Bool,section:"note.tabs.settings",public:!0,label:"Show breadcrumbs",description:"Display full breadcrumbs for selected note. Displayed below the tabs in horizontal layout only."},showNavigationButtons:{value:this._showNavigationButtons,type:s.SettingItemType.Bool,section:"note.tabs.settings",public:!0,label:"Show navigation buttons",description:"Display history backward and forward buttons. Displayed below the tabs in horizontal layout only."},showChecklistStatus:{value:this._showChecklistStatus,type:s.SettingItemType.Bool,section:"note.tabs.settings",public:!0,label:"Show checklist completion status",description:"Display completion status of all checklists in the selected note. Displayed below the tabs in horizontal layout only."},pinEditedNotes:{value:this._pinEditedNotes,type:s.SettingItemType.Bool,section:"note.tabs.settings",public:!0,label:"Automatically pin notes when edited",description:"Pin notes automatically as soon as the title, content or any other attribute changes."},unpinCompletedTodos:{value:this._unpinCompletedTodos,type:s.SettingItemType.Bool,section:"note.tabs.settings",public:!0,label:"Automatically unpin completed to-dos",description:"Unpin notes automatically as soon as the to-do status changes to completed. Removes the tab completely unless it is the selected note."},addBehavior:{value:d.Temporary,type:s.SettingItemType.Int,section:"note.tabs.settings",isEnum:!0,public:!0,label:"Add tab behavior",description:"Specify how new tabs are added to the panel. Either as temporary or directly as pinned tab. Only one temporary tab (italic font) exists at a time.",options:{0:"Temporary",1:"Pinned"}},unpinBehavior:{value:r.Keep,type:s.SettingItemType.Int,section:"note.tabs.settings",isEnum:!0,public:!0,label:"Unpin active tab behavior",description:"Specify the behavior when unpinning the current active tab. Either keep the active tab selected or select another one, depending on the setting.In case 'Keep selected' is set, the temporary tab (shown with italic font in the tab list) may be replaced with the current active tab.",options:{0:"Keep selected",1:"Select last active tab",2:"Select left tab",3:"Select right tab"}},layoutMode:{value:l.Auto,type:s.SettingItemType.Int,section:"note.tabs.settings",isEnum:!0,public:!0,label:"Force tabs layout",description:"Force tabs horizontal or vertical layout. If Auto, the layout switches automatically at a width of about 400px. Requires restart to be applied.",options:{0:"Auto",1:"Horizontal",2:"Vertical"}},tabHeight:{value:this._tabHeight,type:s.SettingItemType.Int,section:"note.tabs.settings",public:!0,advanced:!0,label:"Note Tabs height (px)",description:"Height of the tabs. Row height in vertical layout."},minTabWidth:{value:this._minTabWidth,type:s.SettingItemType.Int,section:"note.tabs.settings",public:!0,advanced:!0,label:"Minimum Tab width (px)",description:"Minimum width of one tab in pixel."},maxTabWidth:{value:this._maxTabWidth,type:s.SettingItemType.Int,section:"note.tabs.settings",public:!0,advanced:!0,label:"Maximum Tab width (px)",description:"Maximum width of one tab in pixel."},breadcrumbsMaxWidth:{value:this._breadcrumbsMaxWidth,type:s.SettingItemType.Int,section:"note.tabs.settings",public:!0,advanced:!0,label:"Maximum breadcrumb width (px)",description:"Maximum width of one breadcrumb in pixel."},fontFamily:{value:this._fontFamily,type:s.SettingItemType.String,section:"note.tabs.settings",public:!0,advanced:!0,label:"Font family",description:"Font family used in the panel. Font families other than 'default' must be installed on the system. If the font is incorrect or empty, it might default to a generic sans-serif font. (default: App default)"},fontSize:{value:this._fontSize,type:s.SettingItemType.String,section:"note.tabs.settings",public:!0,advanced:!0,label:"Font size",description:"Font size used in the panel. Values other than 'default' must be specified in valid CSS syntax, e.g. '13px'. (default: App default font size)"},mainBackground:{value:this._background,type:s.SettingItemType.String,section:"note.tabs.settings",public:!0,advanced:!0,label:"Background color",description:"Main background color of the panel. (default: Note list background color)"},hoverBackground:{value:this._hoverBackground,type:s.SettingItemType.String,section:"note.tabs.settings",public:!0,advanced:!0,label:"Hover Background color",description:"Background color used when hovering a favorite. (default: Note list hover color)"},activeBackground:{value:this._actBackground,type:s.SettingItemType.String,section:"note.tabs.settings",public:!0,advanced:!0,label:"Active background color",description:"Background color of the current active tab. (default: Editor background color)"},breadcrumbsBackground:{value:this._breadcrumbsBackground,type:s.SettingItemType.String,section:"note.tabs.settings",public:!0,advanced:!0,label:"Infobar background color",description:"Background color of the info bar (incl. breadcrumbs, etc.) below the tabs. (default: Editor background color)"},mainForeground:{value:this._foreground,type:s.SettingItemType.String,section:"note.tabs.settings",public:!0,advanced:!0,label:"Foreground color",description:"Foreground color used for text and icons. (default: App faded color)"},activeForeground:{value:this._actForeground,type:s.SettingItemType.String,section:"note.tabs.settings",public:!0,advanced:!0,label:"Active foreground color",description:"Foreground color of the current active tab. (default: Editor font color)"},dividerColor:{value:this._dividerColor,type:s.SettingItemType.String,section:"note.tabs.settings",public:!0,advanced:!0,label:"Divider color",description:"Color of the divider between the tabs. (default: App default border color)"}}),this._noteTabs=yield n.default.settings.value("noteTabs"),yield this.read()}))}getOrDefault(e,t,i,s){return o(this,void 0,void 0,(function*(){if(!e||e.keys.includes(i)){const e=yield n.default.settings.value(i);return s&&e.match(this._defaultRegExp)?s:e}return t}))}read(e){return o(this,void 0,void 0,(function*(){this._enableDragAndDrop=yield this.getOrDefault(e,this._enableDragAndDrop,"enableDragAndDrop"),this._showTodoCheckboxes=yield this.getOrDefault(e,this._showTodoCheckboxes,"showTodoCheckboxes"),this._showBreadcrumbs=yield this.getOrDefault(e,this._showBreadcrumbs,"showBreadcrumbs"),this._showNavigationButtons=yield this.getOrDefault(e,this._showNavigationButtons,"showNavigationButtons"),this._showChecklistStatus=yield this.getOrDefault(e,this._showChecklistStatus,"showChecklistStatus"),this._pinEditedNotes=yield this.getOrDefault(e,this._pinEditedNotes,"pinEditedNotes"),this._unpinCompletedTodos=yield this.getOrDefault(e,this._unpinCompletedTodos,"unpinCompletedTodos"),this._addBehavior=yield this.getOrDefault(e,this._addBehavior,"addBehavior"),this._unpinBehavior=yield this.getOrDefault(e,this._unpinBehavior,"unpinBehavior"),this._layoutMode=yield this.getOrDefault(e,this._layoutMode,"layoutMode"),this._tabHeight=yield this.getOrDefault(e,this._tabHeight,"tabHeight"),this._minTabWidth=yield this.getOrDefault(e,this._minTabWidth,"minTabWidth"),this._maxTabWidth=yield this.getOrDefault(e,this._maxTabWidth,"maxTabWidth"),this._breadcrumbsMaxWidth=yield this.getOrDefault(e,this._breadcrumbsMaxWidth,"breadcrumbsMaxWidth"),this._fontFamily=yield this.getOrDefault(e,this._fontFamily,"fontFamily",a.FontFamily),this._fontSize=yield this.getOrDefault(e,this._fontSize,"fontSize",a.FontSize),this._background=yield this.getOrDefault(e,this._background,"mainBackground",a.Background),this._hoverBackground=yield this.getOrDefault(e,this._hoverBackground,"hoverBackground",a.HoverBackground),this._actBackground=yield this.getOrDefault(e,this._actBackground,"activeBackground",a.ActiveBackground),this._breadcrumbsBackground=yield this.getOrDefault(e,this._breadcrumbsBackground,"breadcrumbsBackground",a.ActiveBackground),this._foreground=yield this.getOrDefault(e,this._foreground,"mainForeground",a.Foreground),this._actForeground=yield this.getOrDefault(e,this._actForeground,"activeForeground",a.ActiveForeground),this._dividerColor=yield this.getOrDefault(e,this._dividerColor,"dividerColor",a.DividerColor)}))}storeTabs(){return o(this,void 0,void 0,(function*(){yield n.default.settings.setValue("noteTabs",this._noteTabs)}))}clearTabs(){return o(this,void 0,void 0,(function*(){this._noteTabs=[],yield this.storeTabs()}))}}},function(e,t,i){"use strict";var o=this&&this.__awaiter||function(e,t,i,o){return new(i||(i=Promise))((function(n,s){function a(e){try{r(o.next(e))}catch(e){s(e)}}function d(e){try{r(o.throw(e))}catch(e){s(e)}}function r(e){var t;e.done?n(e.value):(t=e.value,t instanceof i?t:new i((function(e){e(t)}))).then(a,d)}r((o=o.apply(e,t||[])).next())}))};Object.defineProperty(t,"__esModule",{value:!0});const n=i(0),s=i(1),a=i(2),d=i(5),r=i(3),l=i(6);n.default.plugins.register({onStart:function(){return o(this,void 0,void 0,(function*(){const e=n.default.commands,t=n.default.data,i=n.default.views.dialogs,c=n.default.settings,u=n.default.workspace,h=new r.Settings;yield h.register();const b=new a.NoteTabs(h),f=new d.LastActiveNote,p=new l.Panel(b,h);function g(e){return o(this,void 0,void 0,(function*(){if(b.hasTab(e))return;h.hasAddBehavior(r.AddBehavior.Pinned)?yield v(e,!0):b.indexOfTemp>=0?b.replaceTemp(e):yield b.add(e,a.NoteTabType.Temporary)}))}function v(e,i,n){return o(this,void 0,void 0,(function*(){const o=yield t.get(["notes",e],{fields:["id","is_todo","todo_completed"]});if(o){if(h.unpinCompletedTodos&&o.is_todo&&o.todo_completed)return;b.hasTab(o.id)?yield b.changeType(o.id,a.NoteTabType.Pinned):i&&(yield b.add(o.id,a.NoteTabType.Pinned,n))}}))}function y(){return o(this,void 0,void 0,(function*(){if(f.length<2)return!1;const t=f.id;return!(b.indexOf(t)<0)&&(yield e.execute("openNote",t),!0)}))}function m(t){return o(this,void 0,void 0,(function*(){const i=b.indexOf(t);return!(i<=0)&&(yield e.execute("openNote",b.get(i-1).id),!0)}))}function _(t){return o(this,void 0,void 0,(function*(){const i=b.indexOf(t);return!(i<0)&&(i!=b.length-1&&(yield e.execute("openNote",b.get(i+1).id),!0))}))}function T(e){return o(this,void 0,void 0,(function*(){const t=yield u.selectedNote();if(t&&t.id==e){let t=!1;switch(h.unpinBehavior){case r.UnpinBehavior.LastActive:if(t=yield y(),t)break;case r.UnpinBehavior.LeftTab:if(t=yield m(e),t)break;case r.UnpinBehavior.RightTab:if(t=yield _(e),t)break;t=yield m(e)}yield b.delete(e),t||(yield g(e))}else yield b.delete(e)}))}yield p.register(),yield e.register({name:"tabsPinNote",label:"Pin note to Tabs",iconName:"fas fa-thumbtack",enabledCondition:"someNotesSelected",execute:(e,t)=>o(this,void 0,void 0,(function*(){let i=e;if(i||(i=yield u.selectedNoteIds()),i){for(const e of i)yield v(e,!0,t);yield p.updateWebview()}}))}),yield e.register({name:"tabsUnpinNote",label:"Unpin note from Tabs",iconName:"fas fa-times",enabledCondition:"someNotesSelected",execute:e=>o(this,void 0,void 0,(function*(){let t=e;if(t||(t=yield u.selectedNoteIds()),t){for(const e of t)yield T(e);yield p.updateWebview()}}))}),yield e.register({name:"tabsMoveLeft",label:"Move active Tab left",iconName:"fas fa-chevron-left",enabledCondition:"oneNoteSelected",execute:()=>o(this,void 0,void 0,(function*(){const e=yield u.selectedNote();if(!e)return;const t=b.indexOf(e.id);yield b.moveWithIndex(t,t-1),yield p.updateWebview()}))}),yield e.register({name:"tabsMoveRight",label:"Move active Tab right",iconName:"fas fa-chevron-right",enabledCondition:"oneNoteSelected",execute:()=>o(this,void 0,void 0,(function*(){const e=yield u.selectedNote();if(!e)return;const t=b.indexOf(e.id);yield b.moveWithIndex(t,t+1),yield p.updateWebview()}))}),yield e.register({name:"tabsSwitchLastActive",label:"Switch to last active Tab",iconName:"fas fa-step-backward",enabledCondition:"oneNoteSelected",execute:()=>o(this,void 0,void 0,(function*(){yield y()}))}),yield e.register({name:"tabsSwitchLeft",label:"Switch to left Tab",iconName:"fas fa-step-backward",enabledCondition:"oneNoteSelected",execute:()=>o(this,void 0,void 0,(function*(){const e=yield u.selectedNote();e&&(yield m(e.id))}))}),yield e.register({name:"tabsSwitchRight",label:"Switch to right Tab",iconName:"fas fa-step-forward",enabledCondition:"oneNoteSelected",execute:()=>o(this,void 0,void 0,(function*(){const e=yield u.selectedNote();e&&(yield _(e.id))}))}),yield e.register({name:"tabsClear",label:"Remove all pinned Tabs",iconName:"fas fa-times",execute:()=>o(this,void 0,void 0,(function*(){if(yield i.showMessageBox("Do you really want to remove all pinned tabs?"))return;yield h.clearTabs();const t=yield u.selectedNoteIds();t.length>0?yield e.execute("openNote",t[0]):yield p.updateWebview()}))}),yield e.register({name:"tabsToggleVisibility",label:"Toggle Tabs visibility",iconName:"fas fa-eye-slash",execute:()=>o(this,void 0,void 0,(function*(){yield p.toggleVisibility()}))});yield n.default.views.menus.create("toolsTabs","Tabs",[{commandName:"tabsPinNote",label:"Pin note to Tabs"},{commandName:"tabsUnpinNote",label:"Unpin note from Tabs"},{commandName:"tabsSwitchLastActive",label:"Switch to last active Tab"},{commandName:"tabsSwitchLeft",label:"Switch to left Tab"},{commandName:"tabsSwitchRight",label:"Switch to right Tab"},{commandName:"tabsMoveLeft",label:"Move active Tab left"},{commandName:"tabsMoveRight",label:"Move active Tab right"},{commandName:"tabsClear",label:"Remove all pinned Tabs"},{commandName:"tabsToggleVisibility",label:"Toggle panel visibility"}],s.MenuItemLocation.Tools),yield n.default.views.menuItems.create("notesContextMenuPinToTabs","tabsPinNote",s.MenuItemLocation.NoteListContextMenu),yield n.default.views.menuItems.create("editorContextMenuPinNote","tabsPinNote",s.MenuItemLocation.EditorContextMenu),c.onChange(e=>o(this,void 0,void 0,(function*(){yield h.read(e),yield p.updateWebview()}))),u.onNoteSelectionChange(()=>o(this,void 0,void 0,(function*(){try{const e=yield u.selectedNote();e&&(yield g(e.id),f.id=e.id),yield p.updateWebview()}catch(e){console.error("onNoteSelectionChange: "+e)}}))),u.onNoteChange(e=>o(this,void 0,void 0,(function*(){try{if(e){if(2==e.event){const i=yield t.get(["notes",e.id],{fields:["id","is_todo","todo_completed"]});if(null==i)return;if(h.pinEditedNotes&&(yield v(i.id,!1)),h.unpinCompletedTodos&&i.is_todo&&i.todo_completed){const e=b.indexOf(i.id);b.indexOfTemp!=e&&(yield T(i.id))}}3==e.event&&(yield b.delete(e.id))}yield p.updateWebview()}catch(e){console.error("onNoteChange: "+e)}}))),u.onSyncComplete(()=>o(this,void 0,void 0,(function*(){yield p.updateWebview()}))),yield p.updateWebview()}))}})},function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.LastActiveNote=void 0;t.LastActiveNote=class{constructor(){this._store=new Array}get id(){return this._store.shift()}set id(e){if(2==this._store.length){if(this._store[1]==e)return;this._store.shift()}this._store.push(e)}get length(){return this._store.length}}},function(e,t,i){"use strict";var o=this&&this.__awaiter||function(e,t,i,o){return new(i||(i=Promise))((function(n,s){function a(e){try{r(o.next(e))}catch(e){s(e)}}function d(e){try{r(o.throw(e))}catch(e){s(e)}}function r(e){var t;e.done?n(e.value):(t=e.value,t instanceof i?t:new i((function(e){e(t)}))).then(a,d)}r((o=o.apply(e,t||[])).next())}))};Object.defineProperty(t,"__esModule",{value:!0}),t.Panel=void 0;const n=i(0),s=i(2),a=i(3);t.Panel=class{constructor(e,t){this._tabs=e,this._settings=t}get tabs(){return this._tabs}get sets(){return this._settings}toggleTodoState(e,t){return o(this,void 0,void 0,(function*(){try{const i=yield n.default.data.get(["notes",e],{fields:["id","is_todo","todo_completed"]});i.is_todo&&t?yield n.default.data.put(["notes",i.id],null,{todo_completed:Date.now()}):yield n.default.data.put(["notes",i.id],null,{todo_completed:0})}catch(e){return}}))}register(){return o(this,void 0,void 0,(function*(){this._panel=yield n.default.views.panels.create("note.tabs.panel"),yield n.default.views.panels.addScript(this._panel,"./webview.css"),this.sets.hasLayoutMode(a.LayoutMode.Auto)&&(yield n.default.views.panels.addScript(this._panel,"./webview_auto.css")),this.sets.hasLayoutMode(a.LayoutMode.Vertical)&&(yield n.default.views.panels.addScript(this._panel,"./webview_vertical.css")),yield n.default.views.panels.addScript(this._panel,"./webview.js"),yield n.default.views.panels.onMessage(this._panel,e=>o(this,void 0,void 0,(function*(){if("tabsOpenFolder"===e.name&&(yield n.default.commands.execute("openFolder",e.id)),"tabsOpen"===e.name&&(yield n.default.commands.execute("openNote",e.id)),"tabsPinNote"===e.name){let t=[e.id];yield n.default.commands.execute("tabsPinNote",t)}if("tabsUnpinNote"===e.name){let t=[e.id];yield n.default.commands.execute("tabsUnpinNote",t)}"tabsToggleTodo"===e.name&&(yield this.toggleTodoState(e.id,e.checked)),"tabsMoveLeft"===e.name&&(yield n.default.commands.execute("tabsMoveLeft")),"tabsMoveRight"===e.name&&(yield n.default.commands.execute("tabsMoveRight")),"tabsBack"===e.name&&(yield n.default.commands.execute("historyBackward")),"tabsForward"===e.name&&(yield n.default.commands.execute("historyForward")),"tabsDrag"===e.name&&(yield this.tabs.moveWithId(e.sourceId,e.targetId),yield this.updateWebview()),"tabsDragNotes"===e.name&&(yield n.default.commands.execute("tabsPinNote",e.noteIds,e.targetId))}))),yield n.default.views.panels.setHtml(this._panel,`\n      <div id="container" style="background:${this.sets.background};font-family:${this.sets.fontFamily},sans-serif;font-size:${this.sets.fontSize};">\n        <div id="tabs-container">\n          <p style="padding-left:8px;">Loading panel...</p>\n        </div>\n      </div>\n    `)}))}escapeHtml(e){return String(e).replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#039;")}getNoteParents(e){return o(this,void 0,void 0,(function*(){const t=new Array;let i=e;for(;i;){const e=yield n.default.data.get(["folders",i],{fields:["id","title","parent_id"]});if(!e)break;i=e.parent_id,t.push(e)}return t}))}getNoteChecklistItems(e){let t=[];for(const i of e.split("\n")){const e=i.match(/^\s*(-\s+\[(x|\s)\])/);e&&t.push({checked:e[2].includes("x",0)})}return t}getNoteTabsHtml(e){return o(this,void 0,void 0,(function*(){const t=yield this.sets.showCompletedTodos,i=[];for(const o of this.tabs.tabs){let a=null;try{a=yield n.default.data.get(["notes",o.id],{fields:["id","title","is_todo","todo_completed"]})}catch(e){yield this.tabs.delete(o.id);continue}if(a){if(!t&&a.todo_completed)continue;const n=this.escapeHtml(a.title),d=e&&a.id==e.id?this.sets.actBackground:this.sets.background,r=e&&a.id==e.id?this.sets.actForeground:this.sets.foreground,l=e&&a.id==e.id?"active":"",c=s.NoteTabs.isTemporary(o)?" new":"",u=s.NoteTabs.isPinned(o)?"fa-times":"fa-thumbtack",h=s.NoteTabs.isPinned(o)?"Unpin":"Pin",b=a.is_todo&&a.todo_completed?"line-through":"";let f="";this.sets.showTodoCheckboxes&&a.is_todo&&(f=`<input id="check" type="checkbox" ${a.todo_completed?"checked":""}>`),i.push(`\n          <div id="tab" ${l} data-id="${a.id}" data-bg="${d}" draggable="${this.sets.enableDragAndDrop}" class="${c}" role="tab" title="${n}"\n            onclick="tabClick(event);" ondblclick="pinNote(event);" onauxclick="onAuxClick(event);" onmouseover="setBackground(event,'${this.sets.hoverBackground}');" onmouseout="resetBackground(this);"\n            ondragstart="dragStart(event);" ondragend="dragEnd(event);" ondragover="dragOver(event, '${this.sets.hoverBackground}');" ondragleave="dragLeave(event);" ondrop="drop(event);"\n            style="height:${this.sets.tabHeight}px;min-width:${this.sets.minTabWidth}px;max-width:${this.sets.maxTabWidth}px;border-color:${this.sets.dividerColor};background:${d};">\n            <span class="tab-inner">\n              ${f}\n              <span class="tab-title" style="color:${r};text-decoration: ${b};">\n                ${n}\n              </span>\n              <a href="#" id="${h}" class="fas ${u}" title="${h}" style="color:${r};"></a>\n            </span>\n          </div>\n        `)}}return i.join("\n")}))}getControlsHtml(){let e="";return this.sets.enableDragAndDrop||(e=`\n        <div id="controls" style="height:${this.sets.tabHeight}px;">\n          <a href="#" class="fas fa-chevron-left" title="Move active tab left" style="color:${this.sets.foreground};" onclick="message('tabsMoveLeft');"></a>\n          <a href="#" class="fas fa-chevron-right" title="Move active tab right" style="color:${this.sets.foreground};" onclick="message('tabsMoveRight');"></a>\n        </div>\n      `),e}getInfoBarHtml(e){return o(this,void 0,void 0,(function*(){let t="",i="",o="",n="";if(this.sets.showNavigationButtons&&(t=`\n          <div class="navigation-icons" style="border-color:${this.sets.dividerColor};">\n            <a href="#" class="fas fa-chevron-left" title="Back" style="color:${this.sets.foreground};" onclick="message('tabsBack');"></a>\n            <a href="#" class="fas fa-chevron-right" title="Forward" style="color:${this.sets.foreground};" onclick="message('tabsForward');"></a>\n          </div>\n        `),this.sets.showChecklistStatus&&e){const t=this.getNoteChecklistItems(e.body);if(t.length>0){const e=t.length,o=t.filter(e=>e.checked).length,n=o==e?"completed":"";i=`\n          <div class="checklist-state" style="color:${this.sets.foreground};border-color:${this.sets.dividerColor};">\n            <div class="checklist-state-inner">\n              <span class="checklist-state-text ${n}">\n                <span class="fas fa-check-square" style=""></span>\n                ${o} / ${e}\n              </span>\n            </div>\n          </div>\n        `}}if(this.sets.showBreadcrumbs&&e){let t=new Array,i=yield this.getNoteParents(e.parent_id);for(;i;){const e=i.pop();if(!e)break;t.push(`\n          <div class="breadcrumb" data-id="${e.id}" onClick="openFolder(event);"\n            style="max-width:${this.sets.breadcrumbsMaxWidth}px;">\n            <span class="breadcrumb-inner">\n              <a href="#" class="breadcrumb-title" style="color:${this.sets.foreground};" title="${e.title}">${e.title}</a>\n              <span class="fas fa-chevron-right" style="color:${this.sets.foreground};"></span>\n            </span>\n          </div>\n        `)}t&&(o=`\n          <div class="breadcrumbs-icon">\n            <span class="fas fa-book" style="color:${this.sets.foreground};"></span>\n          </div>\n          <div id="breadcrumbs-container">\n            ${t.join("\n")}\n          </div>\n        `)}return(t||i||o)&&(n=`\n        <div id="infobar-container" style="background:${this.sets.breadcrumbsBackground};">\n          ${t}\n          ${i}\n          ${o}\n        </div>\n      `),n}))}updateWebview(){return o(this,void 0,void 0,(function*(){const e=yield n.default.workspace.selectedNote(),t=yield this.getNoteTabsHtml(e),i=this.getControlsHtml(),o=yield this.getInfoBarHtml(e);yield n.default.views.panels.setHtml(this._panel,`\n      <div id="container" style="background:${this.sets.background};font-family:${this.sets.fontFamily},sans-serif;font-size:${this.sets.fontSize};">\n        <div id="tabs-container" role="tablist" draggable="${this.sets.enableDragAndDrop}"\n          ondragover="dragOver(event, '${this.sets.hoverBackground}');" ondragleave="dragLeave(event);" ondrop="drop(event);" ondragend="dragEnd(event);">\n          ${t}\n          ${i}\n        </div>\n        ${o}\n      </div>\n    `)}))}toggleVisibility(){return o(this,void 0,void 0,(function*(){const e=yield n.default.views.panels.visible(this._panel);yield n.default.views.panels.show(this._panel,!e)}))}}}]);                                                                                                                                                                                                                                                                                                                                     manifest.json                                                                                       000644                  0000000727  14104541314 010410  0                                                                                                    ustar 00                                                                000000  000000                                                                                                                                                                         {
  "manifest_version": 1,
  "id": "joplin.plugin.note.tabs",
  "app_min_version": "1.8.2",
  "version": "1.4.0",
  "name": "Note Tabs",
  "description": "Allows to open several notes at once in tabs and pin them.",
  "author": "Benji300",
  "homepage_url": "https://github.com/benji300/joplin-note-tabs",
  "repository_url": "https://github.com/benji300/joplin-note-tabs",
  "keywords": [
    "notes",
    "panel",
    "pin",
    "tab",
    "view"
  ]
}                                         webview_auto.css                                                                                    000644                  0000000475  14104541314 011121  0                                                                                                    ustar 00                                                                000000  000000                                                                                                                                                                         /* AUTOMATIC VERTICAL LAYOUT */
@media screen and (max-width: 400px) {
  #tabs-container {
    display: block;
    height: 100%;
  }

  #tab {
    border-right-width: 0;
    border-bottom-width: 1px;
    max-width: 100% !important;
  }

  #breadcrumbs-container {
    display: none !important;
  }
}
                                                                                                                                                                                                   webview_vertical.css                                                                                000644                  0000000366  14104541314 011761  0                                                                                                    ustar 00                                                                000000  000000                                                                                                                                                                         /* FORCE VERTICAL LAYOUT */
#tabs-container {
  display: block;
  height: 100%;
}

#tab {
  border-right-width: 0;
  border-bottom-width: 1px;
  max-width: 100% !important;
}

#breadcrumbs-container {
  display: none !important;
}
                                                                                                                                                                                                                                                                          webview.css                                                                                         000644                  0000007252  14104541314 010071  0                                                                                                    ustar 00                                                                000000  000000                                                                                                                                                                         /* GENERAL STYLES */
html {
  --infobarHeight: 26px;
}

html,
body,
body > div,
div#joplin-plugin-content {
  height: 100%;
}
a {
  text-decoration: none;
}
span {
  cursor: default;
}

/* HORIZONTAL LAYOUT */
#container {
  height: 100%;
  overflow-x: hidden;
  overflow-y: overlay;
  width: 100%;
}

#tabs-container {
  display: flex;
  float: left;
  overflow-x: overlay;
  width: 100%;
}
#tab {
  border-style: solid;
  border-width: 0;
  border-right-width: 1px;
  display: flex;
  padding: 0 8px;
  transition: 0.2s;
}
#tab .fas {
  margin-left: auto;
  margin-right: 0;
  padding: 2px;
  visibility: hidden;
}
#tab:hover .fas {
  visibility: visible;
}
.new {
  font-style: italic;
}
.tab-inner {
  align-items: center;
  display: flex;
  height: 100%;
  width: 100%;
}
.tab-inner > input {
  margin: 0;
  margin-right: 3px;
}
.tab-title {
  overflow: hidden;
  padding-right: 3px;
  text-overflow: ellipsis;
  white-space: nowrap;
}

#controls {
  align-items: center;
  display: flex;
  float: right;
  padding: 0 5px;
  margin-left: auto;
  margin-right: 0;
}
#controls .fas {
  align-items: center;
  display: flex;
  font-size: 14.4px;
  height: 26px;
  justify-content: center;
  max-width: 26px;
  min-height: 26px;
  opacity: 0.8;
  width: 26px;
}
#controls .fas:hover {
  opacity: 1;
}

#infobar-container {
  display: flex;
  float: left;
  height: var(--infobarHeight);
  width: 100%;
}
#infobar-container .fas {
  font-size: 1.3em;
}
#infobar-container .fas.fa-book {
  font-size: 1.2em;
}

.navigation-icons {
  border-style: solid;
  border-width: 0;
  border-right-width: 1px;
  margin: auto 0;
  min-width: fit-content;
  padding: 0 4px;
}
.navigation-icons a:hover {
  background: var(--joplin-background-color-hover3);
  border-radius: 3px;
}
.navigation-icons .fas {
  line-height: calc(var(--infobarHeight) - 2px);
  min-width: 12px;
  padding: 0 4px;
}

.checklist-state {
  border-style: solid;
  border-width: 0;
  border-right-width: 1px;
  margin: auto 0;
  min-width: fit-content;
  padding: 0 4px;
}
.checklist-state-inner {
  align-items: center;
  display: flex;
  line-height: calc(var(--infobarHeight) - 2px);
}
.checklist-state-text {
  align-items: center;
  display: flex;
  margin: 0 2px;
  padding: 0 4px;
}
.checklist-state-text > .fas {
  padding-right: 4px;
}
.checklist-state-text.completed {
  background: #46ba61;
  color: white;
  border-radius: 3px;
}

.breadcrumbs-icon {
  margin: auto 0;
  padding-left: 8px;
}
#breadcrumbs-container {
  display: flex;
  float: left;
  overflow-x: overlay;
  overflow-y: hidden;
  width: 100%;
}
.breadcrumb {
  display: flex;
  height: var(--infobarHeight);
  min-width: 36px;
}
.breadcrumb:last-of-type .fas {
  display: none;
}
.breadcrumb-inner {
  align-items: center;
  display: flex;
  line-height: calc(var(--infobarHeight) - 2px);
  width: 100%;
}
.breadcrumb-inner a:hover {
  background: var(--joplin-background-color-hover3);
  border-radius: 3px;
}
.breadcrumb-inner .fas {
  margin-left: auto;
  margin-right: 0px;
}
.breadcrumb-title {
  margin: 0 2px;
  overflow: hidden;
  padding: 0 4px;
  text-align: center;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 100%;
}

/* DRAG AND DROP */
[draggable="true"] {
  /* To prevent user selecting inside the drag source */
  user-select: none;
  -moz-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
}

/* SCROLLBARS */
::-webkit-scrollbar {
  height: 4px;
  width: 7px;
}
::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 5px;
}
                                                                                                                                                                                                                                                                                                                                                      webview.js                                                                                          000644                  0000011061  14104541314 007706  0                                                                                                    ustar 00                                                                000000  000000                                                                                                                                                                         let sourceId = '';

function cancelDefault(event) {
  event.preventDefault();
  event.stopPropagation();
  return false;
}

function getDataId(event) {
  if (event.currentTarget.id === 'tab' || event.currentTarget.className === 'breadcrumb') {
    return event.currentTarget.dataset.id;
  } else {
    return;
  }
}

/* EVENT HANDLER */

function message(message) {
  webviewApi.postMessage({ name: message });
}

function openFolder(event) {
  const dataId = getDataId(event);
  if (dataId) {
    webviewApi.postMessage({ name: 'tabsOpenFolder', id: dataId });
  }
}

function pinNote(event) {
  const dataId = getDataId(event);
  if (dataId) {
    webviewApi.postMessage({ name: 'tabsPinNote', id: dataId });
  }
}

function unpinNote(event) {
  const dataId = getDataId(event);
  if (dataId) {
    webviewApi.postMessage({ name: 'tabsUnpinNote', id: dataId });
  }
}

// default click handler
function tabClick(event) {
  const dataId = getDataId(event);
  if (dataId) {
    if (event.target.id === 'Pin') {
      pinNote(event);
    } else if (event.target.id === 'Unpin') {
      unpinNote(event);
    } else if (event.target.id === 'check') {
      webviewApi.postMessage({ name: 'tabsToggleTodo', id: dataId, checked: event.target.checked });
    } else {
      webviewApi.postMessage({ name: 'tabsOpen', id: dataId });
    }
  }
}

function onAuxClick(event) {
  cancelDefault(event);
  // middle button clicked
  if (event.button == 1) {
    unpinNote(event);
  }
}

// scroll active tab into view
document.addEventListener('DOMSubtreeModified', (event) => {
  const activeTab = document.querySelector("div#tab[active]");
  if (activeTab) {
    activeTab.scrollIntoView({ block: "nearest", inline: "nearest" });
  }
});

// scroll horizontally without 'shift' key
document.addEventListener('wheel', (event) => {
  let element;
  const path = event.composedPath();
  if (path.findIndex(x => x.id === 'tabs-container') >= 0) {
    element = document.getElementById('tabs-container');
  } else if (path.findIndex(x => x.id === 'breadcrumbs-container') >= 0) {
    element = document.getElementById('breadcrumbs-container');
  }
  if (element) {
    element.scrollLeft -= (-event.deltaY);
  }
});

/* DRAG AND DROP */

function setBackground(event, background) {
  event.currentTarget.style.background = background;
}

function resetBackground(element) {
  if (element.dataset.bg) {
    element.style.background = element.dataset.bg;
  } else {
    element.style.background = 'none';
  }
}

function resetTabBackgrounds() {
  document.querySelectorAll('#tab').forEach(x => { resetBackground(x); });

  container = document.querySelector('#tabs-container');
  if (container) {
    container.style.background = 'none';
  }
}

function dragStart(event) {
  const dataId = getDataId(event);
  if (dataId) {
    event.dataTransfer.setData('text/x-plugin-note-tabs-id', dataId);
    sourceId = dataId;
  }
}

function dragEnd(event) {
  resetTabBackgrounds();
  cancelDefault(event);
  sourceId = '';
}

function dragOver(event, hoverColor) {
  resetTabBackgrounds();
  cancelDefault(event);
  if (sourceId !== getDataId(event)) {
    setBackground(event, hoverColor);
  }
}

function dragLeave(event) {
  cancelDefault(event);
}

function drop(event) {
  resetTabBackgrounds();
  cancelDefault(event);
  const dataTargetId = getDataId(event);

  // check whether plugin tab was dragged - trigger tabsDrag message
  const noteTabsId = event.dataTransfer.getData('text/x-plugin-note-tabs-id');
  if (noteTabsId) {
    if (dataTargetId !== sourceId) {
      webviewApi.postMessage({ name: 'tabsDrag', targetId: dataTargetId, sourceId: noteTabsId });
      return;
    }
  }

  // check whether note was dragged from app onto the panel - add new tab at dropped index
  const joplinNoteIds = event.dataTransfer.getData('text/x-jop-note-ids');
  if (joplinNoteIds) {
    const noteIds = new Array();
    for (const noteId of JSON.parse(joplinNoteIds)) {
      noteIds.push(noteId);
    }
    webviewApi.postMessage({ name: 'tabsDragNotes', noteIds: noteIds, targetId: dataTargetId });
    return;
  }

  // check whether favorite (from joplin.plugin.benji.favorites plugin) was dragged onto the panel - add new tab at dropped index
  const favoritesId = event.dataTransfer.getData('text/x-plugin-favorites-id');
  if (favoritesId) {
    const noteIds = new Array(favoritesId);
    webviewApi.postMessage({ name: 'tabsDragNotes', noteIds: noteIds, targetId: dataTargetId });
    return;
  }
}
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               