@font-face {
   font-family: 'Helvetica Now Variable';
   src: url('./fonts/HelveticaNowVF/HelveticaNowVF.woff2') format('woff2-variations'),
        url("./fonts/HelveticaNowVF/HelveticaNowVF.ttf") format("truetype-variations");
   font-weight: 50 1000; /* 50-1000 */
   font-stretch: 50 100; /* 50-100 */
}

.helvnowvf-text-regular {

}

 /* text: Font Playground */
 #text1 {
    font-family: 'Dunbar';
    font-size: 85px;
    font-variation-settings:
       'wght' 712.00,
       'wdth' 432.83,
       'opsz' 36; /* 4-30 */
}


@font-face {
   font-family: "Helvetica Now Variable";
   src: url("../fonts/HelveticaNowVF.woff2") format("woff2-variations"),
        url("../fonts/HelveticaNowVF.ttf") format("truetype-variations");
        font-weight: 501000;
}


/*
Helvetica Now Variable
wght - Has a range of 50-1000
wdth - Has a range of 50-100
opsz - Has a range of 4-30
*/

@font-face {
   font-family: "Helvetica Now Variable";
   src: url("../fonts/HelveticaNowVF.woff2") format("woff2-variations");
   font-weight: 50 1000; /* Has a range of 50-1000 */
   font-stretch: 50 100; /* Has a range of 50-100 */
}