2025-06-28 20:11:23: Starting server using secret key: C:/Users/<USER>/.config/joplin-desktop/ipc_secret_key.txt
2025-06-28 20:33:16: Starting server using secret key: C:/Users/<USER>/.config/joplin-desktop/ipc_secret_key.txt
2025-06-28 20:33:16: Sending message: {"action":"onSecondInstance","data":{"senderPort":2659,"profilePath":"C:/Users/<USER>/.config/joplin-desktop","argv":["C:\\Users\\<USER>\\AppData\\Local\\Programs\\Joplin\\Joplin.exe"]},"secretKey":"MWX0AYnaAXZ2Q5hETXGl8xaoK6XQBm8CvjkL8H3EW5Zn6z7HkFdBBGEdJwx2Xktw"}
2025-06-28 20:33:16: Got message: {"action":"onSecondInstance","data":{"senderPort":2659,"profilePath":"C:/Users/<USER>/.config/joplin-desktop","argv":["C:\\Users\\<USER>\\AppData\\Local\\Programs\\Joplin\\Joplin.exe"]},"secretKey":"MWX0AYnaAXZ2Q5hETXGl8xaoK6XQBm8CvjkL8H3EW5Zn6z7HkFdBBGEdJwx2Xktw","sourcePort":2659}
2025-06-28 20:33:22: Starting server using secret key: C:/Users/<USER>/.config/joplin-desktop/ipc_secret_key.txt
2025-06-28 20:34:00: Starting server using secret key: C:/Users/<USER>/.config/joplin-desktop/ipc_secret_key.txt
2025-06-28 20:36:52: Starting server using secret key: C:/Users/<USER>/.config/joplin-desktop/ipc_secret_key.txt
2025-06-28 20:38:31: Starting server using secret key: C:/Users/<USER>/.config/joplin-desktop/ipc_secret_key.txt
2025-06-28 21:12:30: Starting server using secret key: C:/Users/<USER>/.config/joplin-desktop/ipc_secret_key.txt
2025-06-30 10:47:07: Starting server using secret key: C:/Users/<USER>/.config/joplin-desktop/ipc_secret_key.txt
2025-06-30 18:10:23: Starting server using secret key: C:/Users/<USER>/.config/joplin-desktop/ipc_secret_key.txt
2025-06-30 18:21:25: Starting server using secret key: C:/Users/<USER>/.config/joplin-desktop/ipc_secret_key.txt
2025-07-08 22:22:26: Starting server using secret key: C:/Users/<USER>/.config/joplin-desktop/ipc_secret_key.txt
2025-07-09 12:17:56: Starting server using secret key: C:/Users/<USER>/.config/joplin-desktop/ipc_secret_key.txt
2025-07-10 22:33:05: Starting server using secret key: C:/Users/<USER>/.config/joplin-desktop/ipc_secret_key.txt
2025-07-10 22:55:34: Starting server using secret key: C:/Users/<USER>/.config/joplin-desktop/ipc_secret_key.txt
2025-07-10 23:19:42: Starting server using secret key: C:/Users/<USER>/.config/joplin-desktop/ipc_secret_key.txt
2025-07-11 09:14:02: Starting server using secret key: C:/Users/<USER>/.config/joplin-desktop/ipc_secret_key.txt
2025-07-11 09:14:02: Sending message: {"action":"onSecondInstance","data":{"senderPort":2659,"profilePath":"C:/Users/<USER>/.config/joplin-desktop","argv":["C:\\Users\\<USER>\\AppData\\Local\\Programs\\Joplin\\Joplin.exe"]},"secretKey":"MWX0AYnaAXZ2Q5hETXGl8xaoK6XQBm8CvjkL8H3EW5Zn6z7HkFdBBGEdJwx2Xktw"}
2025-07-11 09:14:02: [error] Could not send message on port 2658: FetchError: invalid json response body at http://localhost:2658/ reason: Unexpected end of JSON input
FetchError: invalid json response body at http://localhost:2658/ reason: Unexpected end of JSON input
    at C:\Users\<USER>\AppData\Local\Programs\Joplin\resources\app.asar\node_modules\@joplin\utils\node_modules\node-fetch\lib\index.js:273:32
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async sendMessage (C:\Users\<USER>\AppData\Local\Programs\Joplin\resources\app.asar\node_modules\@joplin\utils\dist\ipc.js:189:27)
    at async ElectronAppWrapper.sendCrossAppIpcMessage (C:\Users\<USER>\AppData\Local\Programs\Joplin\resources\app.asar\ElectronAppWrapper.js:515:20)
    at async ElectronAppWrapper.ensureSingleInstance (C:\Users\<USER>\AppData\Local\Programs\Joplin\resources\app.asar\ElectronAppWrapper.js:621:13)
    at async ElectronAppWrapper.start (C:\Users\<USER>\AppData\Local\Programs\Joplin\resources\app.asar\ElectronAppWrapper.js:644:32)
2025-07-11 09:14:02: Got message: {"action":"onSecondInstance","data":{"senderPort":2659,"profilePath":"C:/Users/<USER>/.config/joplin-desktop","argv":["C:\\Users\\<USER>\\AppData\\Local\\Programs\\Joplin\\Joplin.exe"]},"secretKey":"MWX0AYnaAXZ2Q5hETXGl8xaoK6XQBm8CvjkL8H3EW5Zn6z7HkFdBBGEdJwx2Xktw","sourcePort":2659}
2025-07-11 19:54:44: Starting server using secret key: C:/Users/<USER>/.config/joplin-desktop/ipc_secret_key.txt
2025-07-11 19:57:06: Starting server using secret key: C:/Users/<USER>/.config/joplin-desktop/ipc_secret_key.txt
2025-07-12 01:20:01: Starting server using secret key: C:/Users/<USER>/.config/joplin-desktop/ipc_secret_key.txt
2025-07-12 03:16:09: Starting server using secret key: C:/Users/<USER>/.config/joplin-desktop/ipc_secret_key.txt
2025-07-16 00:54:42: Starting server using secret key: C:/Users/<USER>/.config/joplin-desktop/ipc_secret_key.txt
2025-07-16 00:55:24: Starting server using secret key: C:/Users/<USER>/.config/joplin-desktop/ipc_secret_key.txt
2025-07-16 06:34:30: Starting server using secret key: C:/Users/<USER>/.config/joplin-desktop/ipc_secret_key.txt
2025-07-18 18:53:38: Starting server using secret key: C:/Users/<USER>/.config/joplin-desktop/ipc_secret_key.txt
2025-07-18 21:56:34: Starting server using secret key: C:/Users/<USER>/.config/joplin-desktop/ipc_secret_key.txt
2025-07-18 22:14:09: Starting server using secret key: C:/Users/<USER>/.config/joplin-desktop/ipc_secret_key.txt
2025-07-18 22:14:09: Sending message: {"action":"onSecondInstance","data":{"senderPort":2659,"profilePath":"C:/Users/<USER>/.config/joplin-desktop","argv":["C:\\Users\\<USER>\\AppData\\Local\\Programs\\Joplin\\Joplin.exe"]},"secretKey":"MWX0AYnaAXZ2Q5hETXGl8xaoK6XQBm8CvjkL8H3EW5Zn6z7HkFdBBGEdJwx2Xktw"}
2025-07-18 22:14:09: [error] Could not send message on port 2658: FetchError: invalid json response body at http://localhost:2658/ reason: Unexpected end of JSON input
FetchError: invalid json response body at http://localhost:2658/ reason: Unexpected end of JSON input
    at C:\Users\<USER>\AppData\Local\Programs\Joplin\resources\app.asar\node_modules\@joplin\utils\node_modules\node-fetch\lib\index.js:273:32
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async sendMessage (C:\Users\<USER>\AppData\Local\Programs\Joplin\resources\app.asar\node_modules\@joplin\utils\dist\ipc.js:189:27)
    at async ElectronAppWrapper.sendCrossAppIpcMessage (C:\Users\<USER>\AppData\Local\Programs\Joplin\resources\app.asar\ElectronAppWrapper.js:515:20)
    at async ElectronAppWrapper.ensureSingleInstance (C:\Users\<USER>\AppData\Local\Programs\Joplin\resources\app.asar\ElectronAppWrapper.js:621:13)
    at async ElectronAppWrapper.start (C:\Users\<USER>\AppData\Local\Programs\Joplin\resources\app.asar\ElectronAppWrapper.js:644:32)
2025-07-18 22:14:09: Got message: {"action":"onSecondInstance","data":{"senderPort":2659,"profilePath":"C:/Users/<USER>/.config/joplin-desktop","argv":["C:\\Users\\<USER>\\AppData\\Local\\Programs\\Joplin\\Joplin.exe"]},"secretKey":"MWX0AYnaAXZ2Q5hETXGl8xaoK6XQBm8CvjkL8H3EW5Zn6z7HkFdBBGEdJwx2Xktw","sourcePort":2659}
2025-07-19 04:16:33: Starting server using secret key: C:/Users/<USER>/.config/joplin-desktop/ipc_secret_key.txt
2025-07-19 04:16:33: Sending message: {"action":"onSecondInstance","data":{"senderPort":2659,"profilePath":"C:/Users/<USER>/.config/joplin-desktop","argv":["C:\\Users\\<USER>\\AppData\\Local\\Programs\\Joplin\\Joplin.exe"]},"secretKey":"MWX0AYnaAXZ2Q5hETXGl8xaoK6XQBm8CvjkL8H3EW5Zn6z7HkFdBBGEdJwx2Xktw"}
2025-07-19 04:16:33: [error] Could not send message on port 2658: FetchError: invalid json response body at http://localhost:2658/ reason: Unexpected end of JSON input
FetchError: invalid json response body at http://localhost:2658/ reason: Unexpected end of JSON input
    at C:\Users\<USER>\AppData\Local\Programs\Joplin\resources\app.asar\node_modules\@joplin\utils\node_modules\node-fetch\lib\index.js:273:32
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async sendMessage (C:\Users\<USER>\AppData\Local\Programs\Joplin\resources\app.asar\node_modules\@joplin\utils\dist\ipc.js:189:27)
    at async ElectronAppWrapper.sendCrossAppIpcMessage (C:\Users\<USER>\AppData\Local\Programs\Joplin\resources\app.asar\ElectronAppWrapper.js:515:20)
    at async ElectronAppWrapper.ensureSingleInstance (C:\Users\<USER>\AppData\Local\Programs\Joplin\resources\app.asar\ElectronAppWrapper.js:621:13)
    at async ElectronAppWrapper.start (C:\Users\<USER>\AppData\Local\Programs\Joplin\resources\app.asar\ElectronAppWrapper.js:644:32)
2025-07-19 04:16:33: Got message: {"action":"onSecondInstance","data":{"senderPort":2659,"profilePath":"C:/Users/<USER>/.config/joplin-desktop","argv":["C:\\Users\\<USER>\\AppData\\Local\\Programs\\Joplin\\Joplin.exe"]},"secretKey":"MWX0AYnaAXZ2Q5hETXGl8xaoK6XQBm8CvjkL8H3EW5Zn6z7HkFdBBGEdJwx2Xktw","sourcePort":2659}
2025-07-19 04:16:42: Starting server using secret key: C:/Users/<USER>/.config/joplin-desktop/ipc_secret_key.txt
2025-07-19 05:30:51: Starting server using secret key: C:/Users/<USER>/.config/joplin-desktop/ipc_secret_key.txt
2025-07-19 05:30:51: Sending message: {"action":"onSecondInstance","data":{"senderPort":2659,"profilePath":"C:/Users/<USER>/.config/joplin-desktop","argv":["C:\\Users\\<USER>\\AppData\\Local\\Programs\\Joplin\\Joplin.exe"]},"secretKey":"MWX0AYnaAXZ2Q5hETXGl8xaoK6XQBm8CvjkL8H3EW5Zn6z7HkFdBBGEdJwx2Xktw"}
2025-07-19 05:30:51: Got message: {"action":"onSecondInstance","data":{"senderPort":2659,"profilePath":"C:/Users/<USER>/.config/joplin-desktop","argv":["C:\\Users\\<USER>\\AppData\\Local\\Programs\\Joplin\\Joplin.exe"]},"secretKey":"MWX0AYnaAXZ2Q5hETXGl8xaoK6XQBm8CvjkL8H3EW5Zn6z7HkFdBBGEdJwx2Xktw","sourcePort":2659}
2025-07-19 05:30:51: [error] Could not send message on port 2658: FetchError: invalid json response body at http://localhost:2658/ reason: Unexpected end of JSON input
FetchError: invalid json response body at http://localhost:2658/ reason: Unexpected end of JSON input
    at C:\Users\<USER>\AppData\Local\Programs\Joplin\resources\app.asar\node_modules\@joplin\utils\node_modules\node-fetch\lib\index.js:273:32
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async sendMessage (C:\Users\<USER>\AppData\Local\Programs\Joplin\resources\app.asar\node_modules\@joplin\utils\dist\ipc.js:189:27)
    at async ElectronAppWrapper.sendCrossAppIpcMessage (C:\Users\<USER>\AppData\Local\Programs\Joplin\resources\app.asar\ElectronAppWrapper.js:515:20)
    at async ElectronAppWrapper.ensureSingleInstance (C:\Users\<USER>\AppData\Local\Programs\Joplin\resources\app.asar\ElectronAppWrapper.js:621:13)
    at async ElectronAppWrapper.start (C:\Users\<USER>\AppData\Local\Programs\Joplin\resources\app.asar\ElectronAppWrapper.js:644:32)
2025-07-19 05:31:01: Starting server using secret key: C:/Users/<USER>/.config/joplin-desktop/ipc_secret_key.txt
2025-07-22 20:10:36: Starting server using secret key: C:/Users/<USER>/.config/joplin-desktop/ipc_secret_key.txt
