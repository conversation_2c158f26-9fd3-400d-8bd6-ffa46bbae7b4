@import url("userstyle-syntax");
@import url("userstyle-futuremotion.css");

:root {

    --default-body-max-width-print: 65.75rem;
    --default-body-max-width: 1300px;

    /* MAIN ///////////////////////////////////////////////////////////////////////*/
    /*/////////////////////////////////////////////////////////////////////////////*/

    --default-text-color: #202122;
    --default-font-size: 16.3px;
    --default-font-size-table: 15.5px;
    --default-font-size-table-inline-code: .75rem;
    --default-line-height: 1.53rem;
    --default-line-height-caption: 1.3125rem;
    --default-p-margin-bottom: .8rem;
    --default-body-margin-left: 0;
    --default-body-margin-right: 0;
    --default-font-family: var(--font-stack-roboto);
    --link-color-primary-blue-dark: #123ba3;
    --link-color-primary-blue: #2D6DE2;

    /* CODE STYLES ////////////////////////////////////////////////////////////////*/
    /*/////////////////////////////////////////////////////////////////////////////*/

    --inlinecode-color: #333538;
    --inlinecode-color-hover: #333538;
    --inlinecode-border-width: .0625rem;
    --inlinecode-border-color: #d1d3d6;
    --inlinecode-border-color-hover: #bebebe;
    --inlinecode-background-color: #F9F9F9;
    --inlinecode-background-color-hover: #fbfbfb;
    --inlinecode-font-size: .8125rem;
    --inlinecode-font-weight-strong: 500;
    --inlinecode-font-family: var(--font-stack-inputmono);
    --inlinecode-border: .0625rem solid var(--inlinecode-border-color);
    --inlinecode-border-2px: .125rem solid var(--inlinecode-border-color);
    --inlinecode-border-hover: .0625rem solid var(--inlinecode-border-color-hover);
    --inlinecode-border-radius: .1875rem;
    --inlinecode-table-border-radius: .1875rem;
    --inlinecode-table-padding: 0.2em 0.4em 0.2em 0.4em;
    --inlinecode-table-font-size: .8225rem;
    --inlinecode-table-margin-rightleft: .15rem;
    --inlinecode-table-letter-spacing: .0063rem;
    --codeblock-border-color: #aab0b9;
    --codeblock-padding: .7025rem;
    --codeblock-border-radius: 0.2275rem;
    --codeblock-line-height: 1.45rem;

    /* TABLE STYLES ///////////////////////////////////////////////////////////////*/
    /*/////////////////////////////////////////////////////////////////////////////*/

    --table-color-border-header: #bfbfbf;
    --table-color-border-header-hover: #acacac;
    --table-color-border-default: #d2d2d2;
    --table-color-border-default-hover: #c6c6c6;
    --table-color-text-header: #2a2a2a;
    --table-color-text-header-hover: #2a2a2a;
    --table-color-text-default: #2a2a2a;
    --table-color-text-default-hover: #2a2a2a;
    --table-color-bg-row-header: #f9f9f9;
    --table-color-bg-row-header-hover: #f9f9f9;
    --table-color-bg-row-default: #ffffff;
    --table-color-bg-row-default-hover: #ffffff;
    --table-color-bg-row-even: #ffffff;
    --table-color-bg-row-even-hover: #ffffff;
    --table-font-size-header: .9875rem;
    --table-font-size-default: .9875rem;
    --table-font-size-code: .9875rem;
    --table-margin-top: 20px;
    --table-margin-bot: 20px;
    --table-row-height: 20px;
    --table-border-color: #CCCCCC;
    --table-border-color-head: #CCCCCC;

    /* HEADER STYLES //////////////////////////////////////////////////////////////*/
    /*/////////////////////////////////////////////////////////////////////////////*/

    --headers-padding: 0 0 9px 0;
    --headers-color: #1a1b1e;
    --headers-code-color: #3c3d42;
    --headers-h1-font-size: 32px;
    --headers-h2-font-size: 24px;
    --headers-h3-font-size: 20.5px;
    --headers-h4-font-size: 19px;
    --headers-h5-font-size: 18.4px;
    --headers-h6-font-size: 22px;
    --headers-h6-line-height: 2.125rem;
    --headers-h6-link-icon-url: url(../svg/external-link-fm-thick-4-normal.svg);

    /* LIST STYLES ////////////////////////////////////////////////////////////////*/
    /*/////////////////////////////////////////////////////////////////////////////*/

    --list-ol-padding-left: .1rem;
    --list-ul-marker-color: #71767e;

    /* MARK STYLES ////////////////////////////////////////////////////////////////*/
    /*/////////////////////////////////////////////////////////////////////////////*/

    --mark-color: #3d3428;
    --mark-color-bg: #f8f9c1;
    --mark-padding: .4375rem .375rem .3125rem .375rem;
    --mark-border-radius: 0.1875rem;

    /* IMAGE STYLES ///////////////////////////////////////////////////////////////*/
    /*/////////////////////////////////////////////////////////////////////////////*/

    --image-margin-bottom: .3125rem;
    --image-margin-top: .625rem;

    /* FONT STACKS ////////////////////////////////////////////////////////////////*/
    /*/////////////////////////////////////////////////////////////////////////////*/

    --font-stack-helvnow: "Helvetica Now Text", Helvetica, "Helvetica LT Pro", "SF Pro Text", Arial, sans-serif;
    --font-stack-helvnowmed: "Helvetica Now Text Medium", "Helvetica Now Text", Helvetica, "Helvetica LT Pro", "SF Pro Text", Arial, sans-serif;
    --font-stack-inter: Inter, Roboto, "Helvetica Now Text", Helvetica, Arial, sans-serif;
    --font-stack-roboto: Roboto, "Roboto Flex", "Helvetica LT Pro", Helvetica, "SF Pro Text", Arial, sans-serif;
    --font-stack-helv: "Helvetica", "Helvetica LT Pro", "SF Pro Text", "Arial", sans-serif;
    --font-stack-sfpro: "SF Pro Display", "Helvetica Now Text", "Helvetica LT Pro", "Arial", sans-serif;
    --font-stack-robotomono: "Roboto Mono", "SF Mono", "Input Mono", Input, monospace;
    --font-stack-inputmono: "Input Mono", Input, "Roboto Mono", "SF Mono", monospace;
    --font-stack-inputmonomedium: "Input Mono Medium", "Input Mono", Input, "Roboto Mono", "SF Mono", monospace;
    --font-stack-sfmono: "SF Mono", "Input Mono", Input, "Roboto Mono", "SF Mono", monospace;
}

/* RTE DOCUMENT WIDTH (NON PRINT) /////////////////////////////////////////////////*/
/*/////////////////////////////////////////////////////////////////////////////////*/

.mce-content-body,
.mce-content-body #rendered-md {
    max-width: var(--default-body-max-width) !important;
    width: var(--default-body-max-width) !important;
}


/* RESET /////////////////////////////////////////////////////////////////////////////////////////*/
/*////////////////////////////////////////////////////////////////////////////////////////////////*/
/*////////////////////////////////////////////////////////////////////////////////////////////////*/

p, h1, h2, h3, h4, h5, h6, ul, table { margin-bottom: unset !important }
h1, h2, h3, h4, h5, h6, ul, table { margin-bottom: unset !important }
div#rendered-md {
    margin: 0 !important;
    padding: 0 !important;
    max-width: 100% !important;
    box-sizing: border-box !important;
}
html .mce-content-body {
    margin: 0 !important;
}

/* GENERAL STYLES BOTH PRINT AND SCREEN //////////////////////////////////////////////////////////*/
/*////////////////////////////////////////////////////////////////////////////////////////////////*/
/*////////////////////////////////////////////////////////////////////////////////////////////////*/

html #rendered-md {

    font-family: var(--default-font-family);
    word-wrap: break-word !important;
    line-height: var(--default-line-height) !important;
    font-size: var(--default-font-size) !important;
    color: var(--default-text-color) !important;
    box-sizing: border-box !important;

    p {
        margin-bottom: var(--default-p-margin-bottom) !important;
        unicode-bidi: plaintext;
        strong {
            font-weight: 600 !important;
        }
    }

    strong {
        font-weight: 600 !important;
        letter-spacing: -.2px !important;
    }

    li p {
        margin-top: 0.2em;
        margin-bottom: 0 !important;
    }

    hr {
        border-bottom: 1px solid #c9ced5 !important;
        margin-bottom: 18px !important;
        margin-top: 18px !important;
    }

    a {
        color: var(--link-color-primary-blue);
        background-color: transparent;
        text-decoration: none !important;
    }

    a:hover {
        color: var(--link-color-primary-blue-dark);
        cursor: pointer !important;
    }

    img {
        margin-bottom: var(--image-margin-bottom) !important;
        margin-top: var(--image-margin-top) !important;
        max-width: 100% !important;
    }

    div.joplin-table-wrapper {
        display: inline-flex;
        box-sizing: border-box !important;
        width: var(--default-body-max-width) !important;
        margin: 0 !important;
        padding: 0 !important;
    }

    table {
        width: var(--default-body-max-width) !important;

    }

    mark,
    body mark {
        background: var(--mark-color-bg) !important;
        color: var(--mark-color) !important;
        padding: var(--mark-padding) !important;
        border-radius: var(--mark-border-radius) !important;
    }

}



.jop-tinymce #rendered-md p {
    line-height: 1.7rem !important;
}

.jop-tinymce #rendered-md p strong {
    font-weight: 500 !important;
}

table.jop-noMdConv tbody strong {
    font-weight: 500 !important;
    letter-spacing: .2px !important;
}

#rendered-md strong {
    font-weight: 500 !important;
    letter-spacing: .1px !important;
}

.mce-content-body table + div.joplin-editable,
#rendered-md table + div.joplin-editable {
    margin-top: 26px !important;
}



/* TABLE POPUP STYLES /////////////////////////////////////////////////////////*/
/*/////////////////////////////////////////////////////////////////////////////*/

div.tox.tox-silver-sink .tox-pop__dialog .tox-toolbar .tox-toolbar__group .tox-tbtn .tox-icon.tox-tbtn__icon-wrap svg {
    color: #CCC !important;
    fill: #a8adb5 !important;
    scale: 95%;
}

/* HEADER STYLES //////////////////////////////////////////////////////////////*/
/*/////////////////////////////////////////////////////////////////////////////*/
/*/////////////////////////////////////////////////////////////////////////////*/



.mce-content-body #rendered-md, #rendered-md {

    div.joplin-editable + h1, div.joplin-editable + h2,
    div.joplin-editable + h3, div.joplin-editable + h4,
    div.joplin-editable + h5 {
        margin-top: 24px !important;
    }

    /* div.joplin-editable {
        max-width: var(--default-body-max-width) !important;
    } */

    ul + h1, ul + h2, ul + h3, ul + h4, ul + h5 {
        margin-top: 22px !important;
    }

    h1, h2, h3, h4, h5, h6 {
        font-family: var(--font-stack-helvnow) !important;
        color: var(--headers-color) !important;
        border-bottom: 1px solid #c9ced5 !important;
        letter-spacing: -0.1px !important;
        strong { font-weight: 600 !important; }
    }

    h1, h2, h3, h4, h5 {
        padding: var(--headers-padding) !important;
        &:first-child { margin-top: 8px !important }
    }

    h1, h2, h3 { margin: 24px 0px 18px 0px !important; font-weight: 500 !important; }
    h4, h5 { font-weight: 400 !important; margin: 28px 0px 16px 0px !important; }
    h1 { font-size: var(--headers-h1-font-size) !important; font-weight: 500 !important; }
    h2 { font-size: var(--headers-h2-font-size) !important; }
    h3 { font-size: var(--headers-h3-font-size) !important; }
    h4 {
        margin-top: 24px !important;
        font-size: var(--headers-h4-font-size) !important;
        font-family: var(--font-stack-helvnowmed) !important;
    }
    h5 {
        font-size: var(--headers-h5-font-size) !important;
        font-family: var(--font-stack-helvnowmed) !important;
        border-bottom: 1px solid #d6d3d3 !important;
        margin: 12px 0px 16px 0px !important;
        padding: 0px 0px 7px 0px !important;
    }
    h6 {
        border: none !important;
        color: #7c8088 !important;
        font-size: var(--headers-h6-font-size) !important;
        font-weight: 380 !important;
        letter-spacing: .3px !important;
        border: 0 !important;
        line-height: var(--headers-h6-line-height) !important;
        margin: 16px 0px 16px 0px !important;
        font-family: var(--font-stack-sfpro) !important;
        strong { font-weight: 500 !important; }


        a {
            display: inline-flex;
            position: relative;
            padding: 4px 8px 4px 8px;
            background-color: #ebefff !important;
            border-radius: 4px !important;
            align-items: center;
            margin-right: 4px !important;
            margin-left: 2px !important;
            font-size: 16px !important;
            color: #3651fa !important;
            height: 20px !important;
            top: -1px !important;
            font-weight: 400;
        }

        a:hover {
            color: #3651fa !important;
            outline: 2px solid #829aff;
            outline-offset: -1px !important;
        }

        a::after {
            content: '';

            background-image: var(--headers-h6-link-icon-url);
            background-image: url("data:image/svg+xml,%3Csvg version='1.1' id='Layer_1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' x='0px' y='0px' viewBox='0 0 2048 2048' enable-background='new 0 0 2048 2048' xml:space='preserve'%3E%3Cpath fill='%233651FA' d='M853.88806,1028.37781l159.10468,159.10474l575.85931-575.85925l265.17456,265.17456V193.97342h-682.82446 l251.91589,251.9158L853.88806,1028.37781z M193.97342,262.97946v1591.04712h225.39835h1365.64868v-689.45374H1552.9928v457.42603 H419.37177V488.37781h457.42609V262.97946H193.97342z'/%3E%3C/svg%3E");
            background-repeat: no-repeat;
            position: relative;
            left: 6px;
            width: 15px !important;
            height: 15px !important;
            display: inline-flex;
            margin-right: 8px;
            align-items: center;
            opacity: 100% !important;
        }
    }

    h1, h2, h3, h4, h5 {
        code {
            margin: 0 4px 0 4px !important;
            display: inline-flex !important;
            font-weight: 400 !important;
            position: relative !important;
            align-items: center !important;
            font-size: 15px !important;
            top: -1px !important;
            height: 16px !important;
            font-family: var(--font-stack-sfmono) !important;
            color: var(--headers-code-color) !important;
            padding: 4px 7px !important;
            background-color: var(--inlinecode-background-color) !important;
            border-color: var(--inlinecode-border-color) !important;
            border-width: 1px !important;
            margin: 0px 2px !important;

            &:hover {
                border-color: var(--inlinecode-border-color-hover) !important;
                background-color: var(--inlinecode-background-color-hover) !important;
            }
        }
    }
    h1 code {
        letter-spacing: -0.1px !important;
        border: var(--inlinecode-border-2px) !important;
    }
    h2 code {
        top: -1.5px !important;
        letter-spacing: -0.1px !important;
        border: var(--inlinecode-border-2px) !important;
    }
    h3 code {
        font-size: 13px !important;
        letter-spacing: 0.2px !important;
        padding: 4px 5px 3px 5px !important;
        border-color: var(--inlinecode-border-color) !important;
        font-family: var(--inlinecode-font-family) !important;
    }
    h4 code {
        font-size: 13px !important;
        margin: 1px !important;
        border-color: var(--inlinecode-border-color) !important;
    }
    h5 code {
        font-weight: 500 !important;
        font-size: 12px !important;
        height: 14px !important;
        border-color: var(--inlinecode-border-color) !important;
    }
    h6 code {
        margin: 0 4px 0 4px !important;
        display: inline-flex !important;
        font-weight: 400 !important;
        position: relative !important;
        align-items: center !important;
        font-size: 14px !important;
        font-family: var(--font-stack-sfmono) !important;
        color: #858585 !important;
        padding: 4px 7px !important;
        border-color: #e0e0e0 !important;
        margin: 0px 1px 1px 1px !important;
        letter-spacing: -0.1px !important;
        border-width: 2px !important;
        background-color: #F9F9F9 !important;
        top: -.4px;

        &:hover {
            border-color: #e0e0e0 !important;
            color: #727272 !important;
            background-color: #fbfbfb !important;
        }
    }
}



.mce-content-body #rendered-md ul li p code,
#rendered-md ul li p code {
    display: inline-flex;
    height: 11px !important;
    padding: 6px 4px 6px 4px !important;
    font-size: 12px !important;
    letter-spacing: .1px !important;
}

.mce-content-body #rendered-md ul li p code:hover,
#rendered-md ul li p code:hover {
    border-color: var(--inlinecode-border-color-hover) !important;
    background-color: var(--inlinecode-background-color-hover) !important;
}

.mce-content-body #rendered-md p code:hover,
#rendered-md p code:hover {
    border-color: var(--inlinecode-border-color-hover) !important;
    background-color: var(--inlinecode-background-color-hover) !important;
}


/* TABLE STYLES ///////////////////////////////////////////////////////////////*/
/*/////////////////////////////////////////////////////////////////////////////*/
/*/////////////////////////////////////////////////////////////////////////////*/

.jop-tinymce #rendered-md table,
#rendered-md table {
    width: 100% !important;
    background-color: var(--table-color-bg-row-default) !important;
    /* margin-top: var(--table-margin-top) !important;
    margin-bottom: var(--table-margin-bot) !important; */
    font-size: var(--table-font-size-default) !important;

    tbody tr td strong, tbody tr td p strong {
        font-family: var(--font-stack-helvnowmed);
        font-weight: 400 !important;
    }

    tbody tr td p hr {
        margin-bottom: 12px !important;
    }

    p, div, td, tr, tr td,
    th, code, div code, tr code,
    tr div code, td code, td div code,
    th code, th div code, tr p,
    tr div, th p, th div, td p,
    td div, tbody, thead {
        font-size: var(--table-font-size-default);
    }

    td {
        border: 1px solid var(--table-border-color) !important;
    }

    th {
        border: 1px solid var(--table-border-color-head) !important;
        background-color: #f9f9f9 !important;
    }

    tbody tr td pre.hljs {

        margin-top: 0.6rem !important;
        margin-bottom: 0.6rem !important;
        white-space: break-spaces !important;
    }

    tbody tr td code + code,
    tbody tr td code + strong code,
    tbody tr td ol li code + code,
    tbody tr td ol li code + strong code,
    tbody tr td ul li code + code,
    tbody tr td ul li code + strong code {
        margin-left: 3px !important;
    }

    tbody tr td code,
    tbody code,
    thead tr th code,
    thead code
    {
        display: inline !important;
        position: relative !important;
        box-sizing: border-box !important;
        margin-left: 0 !important;
        margin-right: 0 !important;
        font-size: 12.5px !important;
        font-family: var(--font-stack-inputmono) !important;
        vertical-align: middle !important;
        padding: 4px 6px !important;
        line-height: 1.9rem !important;
        color: #6b6d6f !important;
        border: var(--inlinecode-border) !important;
        border-radius: var(--inlinecode-border-radius) !important;
        background-color: var(--inlinecode-background-color) !important;

        strong {
            font-family: var(--font-stack-inputmono);
            font-weight: 400 !important;
        }

        &:hover {
            color: var(--inlinecode-color-hover) !important;
            border: var(--inlinecode-border-hover) !important;
            background-color: var(--inlinecode-background-color-hover) !important;
        }
    }

    thead tr th code {
        font-weight: 400 !important;
        font-size: 13px !important;
        border: 1px solid #cecece !important;
        margin-left: 3px !important;
        top: -.5px !important;

        strong {
            font-family: var(--font-stack-inputmono);
            font-weight: 400 !important;
        }

    }

    tbody tr td strong code,
    tbody tr td ol li strong code,
    tbody tr td ul li strong code {
        font-family: "Input Mono Medium" !important;
        font-weight: 400 !important;
    }

    tbody tr td ol li code,
    tbody tr td ul li code.inline-code,
    tbody tr td ul li code {
        font-weight: 400;
        border-radius: var(--inlinecode-border-radius) !important;
        border: var(--inlinecode-border) !important;
        font-family: var(--font-stack-inputmono) !important;
        background-color: var(--inlinecode-background-color) !important;
        &:hover {
            color: var(--inlinecode-color-hover) !important;
            border: var(--inlinecode-border-hover) !important;
            background-color: var(--inlinecode-background-color-hover) !important;
        }
    }

    tbody tr td pre.hljs,
    tbody tr td div pre.hljs,
    tbody tr td pre.hljs,
    tbody tr td div pre.hljs {
        margin-top: 6px !important;
        margin-bottom: 6px !important;

        code {
            padding: 12px !important;
        }
    }

    tr:nth-child(even) {
        background-color: var(--table-color-bg-row-even);
    }
    tr:nth-child(even):hover {
        background-color: var(--table-color-bg-row-even-hover);
    }

    tr td {
        background-color: #ffffff;
        height: var(--table-row-height) !important;
        padding: 8px 14px 8px 14px !important;
        vertical-align: middle !important;
    }

    tr th {
        background-color: #ffffff;
        height: var(--table-row-height) !important;
    }

    td, th {
        padding: .5rem !important;
        text-align: left !important;
        vertical-align: top !important;
        text-align: left;
        padding: .5em 1em .5em 1em;
        color: #32373F;
        font-family: var(--font-stack-helvnow) !important;
        font-size: var(--default-font-size-table) !important;
    }

    tr:hover {
        background-color: #ffffff;
    }

    td p {
        text-align: left !important;
        padding: 0 !important;
        font-size: var(--default-font-size-table) !important;
        color: var(--default-text-color);
        font-family: var(--font-stack-helvnow) !important;
        margin: 6px 0px 6px 0px !important;
        line-height: 1.50rem !important;
    }

    tbody tr td ol {
        margin-left: 1.1em !important;
        margin: 0px 0px 0px 18px !important;
    }

    tbody td pre.hljs code {
        display: inline-block !important;
        height: unset !important;
        white-space: break-spaces;
        word-wrap: break-word;
    }

    hr {
        margin: .875rem 0rem .875rem 0rem !important;
        padding: 0px !important;
    }

    ol li {
        margin-bottom: 4px !important;
    }

    p ol li {
        margin-bottom: 1rem !important;
    }

    thead tr th {
        font-weight: 500 !important;
        background: #F9F9F9 !important;
        font-size: 16px !important;
        color: #0f0f0f !important;
        font-family: var(--font-stack-helvnow) !important;
        padding: 10px 14px 10px 14px !important;
    }

    td {
        font-size: 15px !important;
        line-height: 1.32rem !important;
        color: #393939 !important;
        font-family: var(--font-stack-helvnow) !important;
        font-weight: 400;
    }

    strong {
        font-weight: 600 !important;
    }
}

/* Push consecutive tables away from eachother ////////////////////////////////////*/
/*/////////////////////////////////////////////////////////////////////////////////*/

#rendered-md div.joplin-table-wrapper + div.joplin-table-wrapper {
    margin-top: 20px !important;
}

.inline-code strong, strong .inline-code {
    font-weight: var(--inlinecode-font-weight-strong) !important;
    color: var(--inlinecode-color) !important;
    font-size: var(--inlinecode-font-size) !important;
    font-family: var(--inlinecode-font-family) !important;
}

a .inline-code {
    color: var(--link-color-primary-blue) !important;
    font-family: var(--inlinecode-font-family) !important;
}

.inline-code,
.mce-content-body code,
#rendered-md code {
    border: var(--inlinecode-border) !important;
    background-color: var(--inlinecode-background-color) !important;
    padding: 0.27em 0.4em 0.27em 0.4em !important;
    border-radius: var(--inlinecode-border-radius) !important;
    font-size: .7805rem !important;
    font-family: var(--font-stack-inputmono) !important;
    margin-right: var(--inlinecode-table-margin-rightleft) !important;
    margin-left: var(--inlinecode-table-margin-rightleft) !important;
    color: var(--inlinecode-color) !important;
    vertical-align: middle !important;
    letter-spacing: .4px !important;
    height: 16px !important;
    display: inline-flex;
    align-items: center;
    margin-top: 3px !important;
    margin-bottom: 3px !important;
}

.inline-code,
#rendered-md code,
.mce-content-body code,
#rendered-md table code strong {
    font-size: 12px !important;
}

.mce-content-body ol li code,
ol li code {
    font-size: 13px !important;
    padding-top: 2px !important;
    position: relative;
    top: -1px !important;
}

html .jop-tinymce #rendered-md .mce-item-table .inline-code:hover,
html div.exported-note #rendered-md .inline-code:hover,
#rendered-md .inline-code:hover
{
    border: var(--inlinecode-border-hover) !important;
    background-color: var(--inlinecode-background-color-hover) !important;
}

/* TABLE SPECIFIC CORRECTIONS /////////////////////////////////////////////////*/
/*/////////////////////////////////////////////////////////////////////////////*/
/*/////////////////////////////////////////////////////////////////////////////*/

.jop-tinymce #rendered-md h6+table,
#rendered-md h6+table {
    margin-top: 32px !important;
}

.jop-tinymce #rendered-md h2+table,
.jop-tinymce #rendered-md h1+table,
#rendered-md h1+table,
#rendered-md h1+table {
    margin-top: 32px !important;
}

.jop-tinymce #rendered-md table+div.joplin-editable,
#rendered-md table+div.joplin-editable {
    margin-top: 26px !important;
}

.jop-tinymce #rendered-md ul+table,
#rendered-md ul+table {
    margin-top: 28px !important;
}




/*// CODE BLOCK STYLES ////////////////////////////////////////////////////////*/
/*/////////////////////////////////////////////////////////////////////////////*/
/*/////////////////////////////////////////////////////////////////////////////*/

pre.hljs {
    overflow-x: auto !important;
    border-width: 1px !important;
    border-style: solid !important;
    border-color: var(--codeblock-border-color) !important;
    margin-top: 1rem !important;
    margin-bottom: 1rem !important;
    display: block !important;
    text-wrap: wrap !important;
}

.mce-content-body .hljs code,
#rendered-md pre.hljs code,
#rendered-md ol li pre.hljs code,
.mce-content-body #rendered-md ol li div.joplin-editable pre.hljs code
{
    border: 0px !important;
    background-color: #fbfbfb !important;
    padding: var(--codeblock-padding) !important;
    border-radius: var(--codeblock-border-radius) !important;
    font-size: .86rem !important;
    font-family: var(--font-stack-inputmono) !important;
    font-weight: 400 !important;
    line-height: var(--codeblock-line-height) !important;
    letter-spacing: -.2px !important;
    height: unset !important;
    margin-top: 0px !important;
    margin-bottom: 0px !important;
    display: block !important;
}

.jop-tinymce #rendered-md table tbody tr td pre.hljs code,
#rendered-md table tbody tr td pre.hljs code,
table tbody tr td pre.hljs code
{
    display: inline-flex;
    font-weight: 400;
    font-family: var(--font-stack-inputmono) !important;
    position: relative;
    padding: 10px !important;
    align-items: center;
    border-radius: 3px !important;
    background: #FBFBFB !important;
    height: unset !important;
    margin-top: 0px !important;
    margin-bottom: 0px !important;
    border: 0 !important;
}

.jop-tinymce #rendered-md table div.joplin-editable,
#rendered-md table div.joplin-editable {
    margin-top: 0px !important;
}

.hljs,
.hljs code {
    border-radius: var(--codeblock-border-radius) !important;
    padding: 0 !important;
    font-family: var(--font-stack-inputmono) !important;
    display: block !important;
}

/*// LIST STYLES //////////////////////////////////////////////////////////////*/
/*/////////////////////////////////////////////////////////////////////////////*/
/*/////////////////////////////////////////////////////////////////////////////*/

.mce-content-body #rendered-md,
#rendered-md {

    ul, ol {
        margin-top: 0px !important;
        margin-bottom: 16px !important;
    }

    ul li ul,
    ol li ol {
        margin-bottom: 0px !important;
    }

    ul li,
    ol li {
        line-height: 28px !important;
    }

    ul {

        &:not(.joplin-checklist) {

            position: relative;

            li::before {
                content: "";
                display: inline-flex;
                height: 0.55rem;
                position: relative;
                width: 7px;
                background-image: url(../svg/bullet_new_3.svg);
                background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' x='4px' y='4px' width='8px' height='8px' viewBox='0 0 8 8' xml:space='preserve'%3E%3Ccircle cx='4' cy='4' r='4' /%3E%3C/svg%3E");
                background-size: 6px 6px;
                background-repeat: no-repeat;
                margin-right: 12px;
                margin-left: -21px !important;
                scale: 100%;
                filter: invert(29%) sepia(10%) saturate(704%) hue-rotate(173deg) brightness(74%) contrast(78%);
            }

            li {
                margin-left: -5px !important;
                list-style-type: none !important;
                margin-bottom: 0px !important;
                line-height: 26px !important;
            }
        }

        &.joplin-checklist {

            padding-left: 0 !important;
            margin-left: 19px !important;
            margin-right: 0px !important;

            list-style: none !important;

            li {

                margin: 0px 0px 0px 8px !important;
                position: relative !important;
                list-style: none !important;
                margin-left: 6px !important;
                display: list-item;
                color: #000000 !important;
                opacity: 1 !important;

                &::before {
                    font-family: "Font Awesome 5 Free";
                    font-size: 16px !important;
                    color: #373737 !important;
                    vertical-align: middle !important;
                    position: absolute;
                    background-size: 16px 16px !important;
                    pointer-events: all;
                    cursor: pointer;
                    width: 1em;
                    height: 1em;
                    margin-left: -2em;
                    padding: 0 !important;
                    top: 1.6px;
                    padding-left: 7px !important;
                }
                &:not(.checked)::before {
                    content: "\f0c8" !important;
                }
                &.checked::before {
                    content: "\f14a" !important;
                }


            }
        }
    }

    ol {
        padding: 0;
        margin-left: 17px !important;

        li {
            margin-bottom: 0px !important;
            margin-top: 0px !important;
            list-style: decimal !important;
            padding-left: var(--list-ol-padding-left) !important;

            &::marker {
                color: var(--list-ul-marker-color);
                font-weight: 400;
                font-size: 15px;
            }
        }
    }
}

.mce-content-body #rendered-md p code,
#rendered-md p code
{
    font-size: 12.5px !important;
    border: 1px solid #d0d0d0 !important;
    font-family: var(--font-stack-inputmono) !important;
    display: unset;
    color: #474747 !important;
    line-height: normal !important;
    padding: 4px 6px !important;
    color: #404040 !important;
}

#rendered-md blockquote {
    opacity: .8 !important;
    margin: unset;
    border-left: 3px solid #CCCCCC;
    color: #626161;
    margin-left: 8px;
}

#rendered-md .mce-content-body:not([dir=rtl]) blockquote {
    border-left: 3px solid #ccc !important;
    padding-left: .9rem !important;
    color: #464d54 !important;
    margin: 14px 0px 14px 0px !important;
}

html .mce-content-body #rendered-md table ul:not(.joplin-checklist) li,
#rendered-md table ul:not(.joplin-checklist) li,
#rendered-md table ul:not(.joplin-checklist) li
{
    margin-left: -3px !important;
    margin-bottom: 1px !important;
    line-height: 1.6rem !important;
    color: #404040 !important;
}

html .mce-content-body #rendered-md table ul:not(.joplin-checklist) li::before,
html #rendered-md table ul:not(.joplin-checklist) li::before
{
    margin-right: 8px !important;
    opacity: .7 !important;
}


/* CORRECTIONS AND ADJUSTMENTS ///////////////////////////////////////////////////////////////////*/
/*////////////////////////////////////////////////////////////////////////////////////////////////*/
/*////////////////////////////////////////////////////////////////////////////////////////////////*/

/* Increase spacing between table and code block /////////////////////////////////////////////////*/
table + div.joplin-editable { margin-top: 24px !important; }

/* Decrease spacing between paragraph and table //////////////////////////////////////////////////*/
p + div.joplin-table-wrapper { margin-top: -12px !important; }

/* Increase spacing between paragraph and code block /////////////////////////////////////////////*/
p + div.joplin-editable { margin-top: 24px !important; }

/* Increase spacing between consecutive code blocks //////////////////////////////////////////////*/
div.joplin-editable + div.joplin-editable { margin-top: 24px !important; }

/* Move paragraphs slightly closer to headings ///////////////////////////////////////////////////*/
h1 + p, h2 + p, h3 + p, h4 + p, h5 + p { margin-top: -4px !important; }

/* Push code blocks away from table bottoms //////////////////////////////////////////////////////*/
table + div.joplin-editable { margin-top: 26px !important; }

/* Push tables away from unordered list bottoms //////////////////////////////////////////////////*/
ul + table { margin-top: 28px !important; }

/* Create additional space for H6 headers following H1 headers for extra breathing room //////////*/
h1 + h6 { margin-top: 20px !important; margin-bottom: 20px !important; }

/* Create additional space for H6 headers following paragraphs for extra breathing room //////////*/
p + h6 { margin-top: 25px !important; margin-bottom: 25px !important; }

/* Push unordered lists closer to paragraph endings //////////////////////////////////////////////*/
p + ul { margin-top: -7px !important; }

/* Define horizontal rule margins following a paragraph //////////////////////////////////////////*/
p + hr { margin-top: var(--hr-margin-top) !important; margin-bottom: var(--hr-margin-bottom) !important; }

/* Push code blocks away from headers ////////////////////////////////////////////////////////////*/
h1 + div.joplin-editable,
h2 + div.joplin-editable,
h3 + div.joplin-editable,
h4 + div.joplin-editable,
h5 + div.joplin-editable {
    margin-top: 23px !important;
}

/* Push code block away from paragraph to equalize p > codeblock > p spacing /////////////////////*/
.jop-tinymce #rendered-md div.joplin-editable + p,
#rendered-md div.joplin-editable + p
{ margin-top: 22px; }

/* Push code block away from paragraph in table to equalize p > codeblock > p spacing ////////////*/
.jop-tinymce #rendered-md table p + div.joplin-editable,
#rendered-md table p + div.joplin-editable
{ margin-top: 12px !important; }

/* Undocumented corrections //////////////////////////////////////////////////////////////////////*/
#rendered-md h1 + h6 { margin-top: 25px !important; margin-bottom: 25px !important; }
#rendered-md p+h6    { margin-top: 25px !important; margin-bottom: 25px !important; }
#rendered-md p + ul  { margin-top: -7px !important; }
#rendered-md p + hr  { margin-top: 18px !important; margin-bottom: 18px !important; }
#rendered-md h3 + div.joplin-editable,
#rendered-md h2 + div.joplin-editable,
#rendered-md h1 + div.joplin-editable,
#rendered-md h4 + div.joplin-editable,
#rendered-md h5 + div.joplin-editable {
    margin-top: 23px !important;
}

/* Push header elements closer to images appearing at the very top of the document ///////////////*/
html body.mce-content-body #rendered-md img:first-child + h1,
html body.mce-content-body #rendered-md img:first-child + h2,
html body.mce-content-body #rendered-md img:first-child + h3,
html body.mce-content-body #rendered-md img:first-child + h4,
html body.mce-content-body #rendered-md img:first-child + h5,
#rendered-md img:first-child + h1,
#rendered-md img:first-child + h2,
#rendered-md img:first-child + h3,
#rendered-md img:first-child + h4,
#rendered-md img:first-child + h5 {
    margin-top: -10px !important;
}

/* Remove margin from images appearing at the very top of the document ///////////////////////////*/
html body.mce-content-body #rendered-md img:first-child,
#rendered-md img:first-child {
    margin: 0 !important;
}


/* PRINT STYLES ONLY //////////////////////////////////////////////////////////////*/
/*/////////////////////////////////////////////////////////////////////////////////*/
/*/////////////////////////////////////////////////////////////////////////////////*/

/* SET WIDTH OF PRINTED/EXPORTED DOCUMENT /////////////////////////////////////////*/
html div.exported-note {
    width: var(--default-body-max-width-print) !important;
    max-width: var(--default-body-max-width-print) !important;
    padding: 0 !important;
    margin: 0 !important;
    box-sizing: border-box !important;
}

/* AVOID PAGE BREAKS ON IMPORTANT ELEMENTS ////////////////////////////////////////*/
div:not(.exported-note-title), pre, table, img,
h1, h2, h3, h4, h5, h6 {
    page-break-inside: avoid !important;
}

/* HIDE AUTOMATIC NOTE TITLE INSERTION ////////////////////////////////////////////*/
div.exported-note-title {
    padding: 0 !important;
    margin: 0 !important;
    visibility: hidden !important;
    display: none !important;
}