// Generated from csvAnalyze.g4 by ANTLR 4.8
// jshint ignore: start
var antlr4 = require('antlr4/index');
var csvAnalyzeListener = require('./csvAnalyzeListener').csvAnalyzeListener;
var grammarFileName = "csvAnalyze.g4";


var serializedATN = ["\u0003\u608b\ua72a\u8133\ub9ed\u417c\u3be7\u7786\u5964",
    "\u0003\t<\u0004\u0002\t\u0002\u0004\u0003\t\u0003\u0004\u0004\t\u0004",
    "\u0004\u0005\t\u0005\u0004\u0006\t\u0006\u0004\u0007\t\u0007\u0004\b",
    "\t\b\u0004\t\t\t\u0004\n\t\n\u0003\u0002\u0006\u0002\u0016\n\u0002\r",
    "\u0002\u000e\u0002\u0017\u0003\u0003\u0003\u0003\u0003\u0003\u0003\u0003",
    "\u0007\u0003\u001e\n\u0003\f\u0003\u000e\u0003!\u000b\u0003\u0003\u0003",
    "\u0005\u0003$\n\u0003\u0003\u0003\u0003\u0003\u0003\u0004\u0003\u0004",
    "\u0003\u0004\u0005\u0004+\n\u0004\u0003\u0005\u0003\u0005\u0003\u0006",
    "\u0003\u0006\u0003\u0007\u0003\u0007\u0003\u0007\u0005\u00074\n\u0007",
    "\u0003\b\u0003\b\u0003\t\u0003\t\u0003\n\u0003\n\u0003\n\u0002\u0002",
    "\u000b\u0002\u0004\u0006\b\n\f\u000e\u0010\u0012\u0002\u0002\u00029",
    "\u0002\u0015\u0003\u0002\u0002\u0002\u0004\u0019\u0003\u0002\u0002\u0002",
    "\u0006*\u0003\u0002\u0002\u0002\b,\u0003\u0002\u0002\u0002\n.\u0003",
    "\u0002\u0002\u0002\f3\u0003\u0002\u0002\u0002\u000e5\u0003\u0002\u0002",
    "\u0002\u00107\u0003\u0002\u0002\u0002\u00129\u0003\u0002\u0002\u0002",
    "\u0014\u0016\u0005\u0004\u0003\u0002\u0015\u0014\u0003\u0002\u0002\u0002",
    "\u0016\u0017\u0003\u0002\u0002\u0002\u0017\u0015\u0003\u0002\u0002\u0002",
    "\u0017\u0018\u0003\u0002\u0002\u0002\u0018\u0003\u0003\u0002\u0002\u0002",
    "\u0019\u001f\u0005\u0006\u0004\u0002\u001a\u001b\u0005\f\u0007\u0002",
    "\u001b\u001c\u0005\u0006\u0004\u0002\u001c\u001e\u0003\u0002\u0002\u0002",
    "\u001d\u001a\u0003\u0002\u0002\u0002\u001e!\u0003\u0002\u0002\u0002",
    "\u001f\u001d\u0003\u0002\u0002\u0002\u001f \u0003\u0002\u0002\u0002",
    " #\u0003\u0002\u0002\u0002!\u001f\u0003\u0002\u0002\u0002\"$\u0007\b",
    "\u0002\u0002#\"\u0003\u0002\u0002\u0002#$\u0003\u0002\u0002\u0002$%",
    "\u0003\u0002\u0002\u0002%&\u0007\t\u0002\u0002&\u0005\u0003\u0002\u0002",
    "\u0002\'+\u0005\b\u0005\u0002(+\u0005\n\u0006\u0002)+\u0003\u0002\u0002",
    "\u0002*\'\u0003\u0002\u0002\u0002*(\u0003\u0002\u0002\u0002*)\u0003",
    "\u0002\u0002\u0002+\u0007\u0003\u0002\u0002\u0002,-\u0007\u0003\u0002",
    "\u0002-\t\u0003\u0002\u0002\u0002./\u0007\u0004\u0002\u0002/\u000b\u0003",
    "\u0002\u0002\u000204\u0005\u000e\b\u000214\u0005\u0010\t\u000224\u0005",
    "\u0012\n\u000230\u0003\u0002\u0002\u000231\u0003\u0002\u0002\u00023",
    "2\u0003\u0002\u0002\u00024\r\u0003\u0002\u0002\u000256\u0007\u0005\u0002",
    "\u00026\u000f\u0003\u0002\u0002\u000278\u0007\u0006\u0002\u00028\u0011",
    "\u0003\u0002\u0002\u00029:\u0007\u0007\u0002\u0002:\u0013\u0003\u0002",
    "\u0002\u0002\u0007\u0017\u001f#*3"].join("");


var atn = new antlr4.atn.ATNDeserializer().deserialize(serializedATN);

var decisionsToDFA = atn.decisionToState.map( function(ds, index) { return new antlr4.dfa.DFA(ds, index); });

var sharedContextCache = new antlr4.PredictionContextCache();

var literalNames = [ null, null, null, "'\t'", "';'", "','", "'\r'", "'\n'" ];

var symbolicNames = [ null, "TEXT", "STRING", "TAB", "SEMI", "COMMA", "CR", 
                      "LF" ];

var ruleNames =  [ "csvFile", "row", "field", "text", "string", "delimiter", 
                   "tab", "semi", "comma" ];

function csvAnalyzeParser (input) {
	antlr4.Parser.call(this, input);
    this._interp = new antlr4.atn.ParserATNSimulator(this, atn, decisionsToDFA, sharedContextCache);
    this.ruleNames = ruleNames;
    this.literalNames = literalNames;
    this.symbolicNames = symbolicNames;
    return this;
}

csvAnalyzeParser.prototype = Object.create(antlr4.Parser.prototype);
csvAnalyzeParser.prototype.constructor = csvAnalyzeParser;

Object.defineProperty(csvAnalyzeParser.prototype, "atn", {
	get : function() {
		return atn;
	}
});

csvAnalyzeParser.EOF = antlr4.Token.EOF;
csvAnalyzeParser.TEXT = 1;
csvAnalyzeParser.STRING = 2;
csvAnalyzeParser.TAB = 3;
csvAnalyzeParser.SEMI = 4;
csvAnalyzeParser.COMMA = 5;
csvAnalyzeParser.CR = 6;
csvAnalyzeParser.LF = 7;

csvAnalyzeParser.RULE_csvFile = 0;
csvAnalyzeParser.RULE_row = 1;
csvAnalyzeParser.RULE_field = 2;
csvAnalyzeParser.RULE_text = 3;
csvAnalyzeParser.RULE_string = 4;
csvAnalyzeParser.RULE_delimiter = 5;
csvAnalyzeParser.RULE_tab = 6;
csvAnalyzeParser.RULE_semi = 7;
csvAnalyzeParser.RULE_comma = 8;


function CsvFileContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = csvAnalyzeParser.RULE_csvFile;
    return this;
}

CsvFileContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
CsvFileContext.prototype.constructor = CsvFileContext;

CsvFileContext.prototype.row = function(i) {
    if(i===undefined) {
        i = null;
    }
    if(i===null) {
        return this.getTypedRuleContexts(RowContext);
    } else {
        return this.getTypedRuleContext(RowContext,i);
    }
};

CsvFileContext.prototype.enterRule = function(listener) {
    if(listener instanceof csvAnalyzeListener ) {
        listener.enterCsvFile(this);
	}
};

CsvFileContext.prototype.exitRule = function(listener) {
    if(listener instanceof csvAnalyzeListener ) {
        listener.exitCsvFile(this);
	}
};




csvAnalyzeParser.CsvFileContext = CsvFileContext;

csvAnalyzeParser.prototype.csvFile = function() {

    var localctx = new CsvFileContext(this, this._ctx, this.state);
    this.enterRule(localctx, 0, csvAnalyzeParser.RULE_csvFile);
    var _la = 0; // Token type
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 19; 
        this._errHandler.sync(this);
        _la = this._input.LA(1);
        do {
            this.state = 18;
            this.row();
            this.state = 21; 
            this._errHandler.sync(this);
            _la = this._input.LA(1);
        } while((((_la) & ~0x1f) == 0 && ((1 << _la) & ((1 << csvAnalyzeParser.TEXT) | (1 << csvAnalyzeParser.STRING) | (1 << csvAnalyzeParser.TAB) | (1 << csvAnalyzeParser.SEMI) | (1 << csvAnalyzeParser.COMMA) | (1 << csvAnalyzeParser.CR) | (1 << csvAnalyzeParser.LF))) !== 0));
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};


function RowContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = csvAnalyzeParser.RULE_row;
    return this;
}

RowContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
RowContext.prototype.constructor = RowContext;

RowContext.prototype.field = function(i) {
    if(i===undefined) {
        i = null;
    }
    if(i===null) {
        return this.getTypedRuleContexts(FieldContext);
    } else {
        return this.getTypedRuleContext(FieldContext,i);
    }
};

RowContext.prototype.LF = function() {
    return this.getToken(csvAnalyzeParser.LF, 0);
};

RowContext.prototype.delimiter = function(i) {
    if(i===undefined) {
        i = null;
    }
    if(i===null) {
        return this.getTypedRuleContexts(DelimiterContext);
    } else {
        return this.getTypedRuleContext(DelimiterContext,i);
    }
};

RowContext.prototype.CR = function() {
    return this.getToken(csvAnalyzeParser.CR, 0);
};

RowContext.prototype.enterRule = function(listener) {
    if(listener instanceof csvAnalyzeListener ) {
        listener.enterRow(this);
	}
};

RowContext.prototype.exitRule = function(listener) {
    if(listener instanceof csvAnalyzeListener ) {
        listener.exitRow(this);
	}
};




csvAnalyzeParser.RowContext = RowContext;

csvAnalyzeParser.prototype.row = function() {

    var localctx = new RowContext(this, this._ctx, this.state);
    this.enterRule(localctx, 2, csvAnalyzeParser.RULE_row);
    var _la = 0; // Token type
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 23;
        this.field();
        this.state = 29;
        this._errHandler.sync(this);
        _la = this._input.LA(1);
        while((((_la) & ~0x1f) == 0 && ((1 << _la) & ((1 << csvAnalyzeParser.TAB) | (1 << csvAnalyzeParser.SEMI) | (1 << csvAnalyzeParser.COMMA))) !== 0)) {
            this.state = 24;
            this.delimiter();
            this.state = 25;
            this.field();
            this.state = 31;
            this._errHandler.sync(this);
            _la = this._input.LA(1);
        }
        this.state = 33;
        this._errHandler.sync(this);
        _la = this._input.LA(1);
        if(_la===csvAnalyzeParser.CR) {
            this.state = 32;
            this.match(csvAnalyzeParser.CR);
        }

        this.state = 35;
        this.match(csvAnalyzeParser.LF);
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};


function FieldContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = csvAnalyzeParser.RULE_field;
    return this;
}

FieldContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
FieldContext.prototype.constructor = FieldContext;

FieldContext.prototype.text = function() {
    return this.getTypedRuleContext(TextContext,0);
};

FieldContext.prototype.string = function() {
    return this.getTypedRuleContext(StringContext,0);
};

FieldContext.prototype.enterRule = function(listener) {
    if(listener instanceof csvAnalyzeListener ) {
        listener.enterField(this);
	}
};

FieldContext.prototype.exitRule = function(listener) {
    if(listener instanceof csvAnalyzeListener ) {
        listener.exitField(this);
	}
};




csvAnalyzeParser.FieldContext = FieldContext;

csvAnalyzeParser.prototype.field = function() {

    var localctx = new FieldContext(this, this._ctx, this.state);
    this.enterRule(localctx, 4, csvAnalyzeParser.RULE_field);
    try {
        this.state = 40;
        this._errHandler.sync(this);
        switch(this._input.LA(1)) {
        case csvAnalyzeParser.TEXT:
            this.enterOuterAlt(localctx, 1);
            this.state = 37;
            this.text();
            break;
        case csvAnalyzeParser.STRING:
            this.enterOuterAlt(localctx, 2);
            this.state = 38;
            this.string();
            break;
        case csvAnalyzeParser.TAB:
        case csvAnalyzeParser.SEMI:
        case csvAnalyzeParser.COMMA:
        case csvAnalyzeParser.CR:
        case csvAnalyzeParser.LF:
            this.enterOuterAlt(localctx, 3);

            break;
        default:
            throw new antlr4.error.NoViableAltException(this);
        }
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};


function TextContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = csvAnalyzeParser.RULE_text;
    return this;
}

TextContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
TextContext.prototype.constructor = TextContext;

TextContext.prototype.TEXT = function() {
    return this.getToken(csvAnalyzeParser.TEXT, 0);
};

TextContext.prototype.enterRule = function(listener) {
    if(listener instanceof csvAnalyzeListener ) {
        listener.enterText(this);
	}
};

TextContext.prototype.exitRule = function(listener) {
    if(listener instanceof csvAnalyzeListener ) {
        listener.exitText(this);
	}
};




csvAnalyzeParser.TextContext = TextContext;

csvAnalyzeParser.prototype.text = function() {

    var localctx = new TextContext(this, this._ctx, this.state);
    this.enterRule(localctx, 6, csvAnalyzeParser.RULE_text);
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 42;
        this.match(csvAnalyzeParser.TEXT);
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};


function StringContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = csvAnalyzeParser.RULE_string;
    return this;
}

StringContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
StringContext.prototype.constructor = StringContext;

StringContext.prototype.STRING = function() {
    return this.getToken(csvAnalyzeParser.STRING, 0);
};

StringContext.prototype.enterRule = function(listener) {
    if(listener instanceof csvAnalyzeListener ) {
        listener.enterString(this);
	}
};

StringContext.prototype.exitRule = function(listener) {
    if(listener instanceof csvAnalyzeListener ) {
        listener.exitString(this);
	}
};




csvAnalyzeParser.StringContext = StringContext;

csvAnalyzeParser.prototype.string = function() {

    var localctx = new StringContext(this, this._ctx, this.state);
    this.enterRule(localctx, 8, csvAnalyzeParser.RULE_string);
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 44;
        this.match(csvAnalyzeParser.STRING);
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};


function DelimiterContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = csvAnalyzeParser.RULE_delimiter;
    return this;
}

DelimiterContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
DelimiterContext.prototype.constructor = DelimiterContext;

DelimiterContext.prototype.tab = function() {
    return this.getTypedRuleContext(TabContext,0);
};

DelimiterContext.prototype.semi = function() {
    return this.getTypedRuleContext(SemiContext,0);
};

DelimiterContext.prototype.comma = function() {
    return this.getTypedRuleContext(CommaContext,0);
};

DelimiterContext.prototype.enterRule = function(listener) {
    if(listener instanceof csvAnalyzeListener ) {
        listener.enterDelimiter(this);
	}
};

DelimiterContext.prototype.exitRule = function(listener) {
    if(listener instanceof csvAnalyzeListener ) {
        listener.exitDelimiter(this);
	}
};




csvAnalyzeParser.DelimiterContext = DelimiterContext;

csvAnalyzeParser.prototype.delimiter = function() {

    var localctx = new DelimiterContext(this, this._ctx, this.state);
    this.enterRule(localctx, 10, csvAnalyzeParser.RULE_delimiter);
    try {
        this.state = 49;
        this._errHandler.sync(this);
        switch(this._input.LA(1)) {
        case csvAnalyzeParser.TAB:
            this.enterOuterAlt(localctx, 1);
            this.state = 46;
            this.tab();
            break;
        case csvAnalyzeParser.SEMI:
            this.enterOuterAlt(localctx, 2);
            this.state = 47;
            this.semi();
            break;
        case csvAnalyzeParser.COMMA:
            this.enterOuterAlt(localctx, 3);
            this.state = 48;
            this.comma();
            break;
        default:
            throw new antlr4.error.NoViableAltException(this);
        }
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};


function TabContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = csvAnalyzeParser.RULE_tab;
    return this;
}

TabContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
TabContext.prototype.constructor = TabContext;

TabContext.prototype.TAB = function() {
    return this.getToken(csvAnalyzeParser.TAB, 0);
};

TabContext.prototype.enterRule = function(listener) {
    if(listener instanceof csvAnalyzeListener ) {
        listener.enterTab(this);
	}
};

TabContext.prototype.exitRule = function(listener) {
    if(listener instanceof csvAnalyzeListener ) {
        listener.exitTab(this);
	}
};




csvAnalyzeParser.TabContext = TabContext;

csvAnalyzeParser.prototype.tab = function() {

    var localctx = new TabContext(this, this._ctx, this.state);
    this.enterRule(localctx, 12, csvAnalyzeParser.RULE_tab);
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 51;
        this.match(csvAnalyzeParser.TAB);
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};


function SemiContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = csvAnalyzeParser.RULE_semi;
    return this;
}

SemiContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
SemiContext.prototype.constructor = SemiContext;

SemiContext.prototype.SEMI = function() {
    return this.getToken(csvAnalyzeParser.SEMI, 0);
};

SemiContext.prototype.enterRule = function(listener) {
    if(listener instanceof csvAnalyzeListener ) {
        listener.enterSemi(this);
	}
};

SemiContext.prototype.exitRule = function(listener) {
    if(listener instanceof csvAnalyzeListener ) {
        listener.exitSemi(this);
	}
};




csvAnalyzeParser.SemiContext = SemiContext;

csvAnalyzeParser.prototype.semi = function() {

    var localctx = new SemiContext(this, this._ctx, this.state);
    this.enterRule(localctx, 14, csvAnalyzeParser.RULE_semi);
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 53;
        this.match(csvAnalyzeParser.SEMI);
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};


function CommaContext(parser, parent, invokingState) {
	if(parent===undefined) {
	    parent = null;
	}
	if(invokingState===undefined || invokingState===null) {
		invokingState = -1;
	}
	antlr4.ParserRuleContext.call(this, parent, invokingState);
    this.parser = parser;
    this.ruleIndex = csvAnalyzeParser.RULE_comma;
    return this;
}

CommaContext.prototype = Object.create(antlr4.ParserRuleContext.prototype);
CommaContext.prototype.constructor = CommaContext;

CommaContext.prototype.COMMA = function() {
    return this.getToken(csvAnalyzeParser.COMMA, 0);
};

CommaContext.prototype.enterRule = function(listener) {
    if(listener instanceof csvAnalyzeListener ) {
        listener.enterComma(this);
	}
};

CommaContext.prototype.exitRule = function(listener) {
    if(listener instanceof csvAnalyzeListener ) {
        listener.exitComma(this);
	}
};




csvAnalyzeParser.CommaContext = CommaContext;

csvAnalyzeParser.prototype.comma = function() {

    var localctx = new CommaContext(this, this._ctx, this.state);
    this.enterRule(localctx, 16, csvAnalyzeParser.RULE_comma);
    try {
        this.enterOuterAlt(localctx, 1);
        this.state = 55;
        this.match(csvAnalyzeParser.COMMA);
    } catch (re) {
    	if(re instanceof antlr4.error.RecognitionException) {
	        localctx.exception = re;
	        this._errHandler.reportError(this, re);
	        this._errHandler.recover(this, re);
	    } else {
	    	throw re;
	    }
    } finally {
        this.exitRule();
    }
    return localctx;
};


exports.csvAnalyzeParser = csvAnalyzeParser;
exports.CsvFileContext = CsvFileContext;
csvAnalyzeParser.CsvFileContext = CsvFileContext;
exports.RowContext = RowContext;
csvAnalyzeParser.RowContext = RowContext;
exports.FieldContext = FieldContext;
csvAnalyzeParser.FieldContext = FieldContext;
exports.TextContext = TextContext;
csvAnalyzeParser.TextContext = TextContext;
exports.StringContext = StringContext;
csvAnalyzeParser.StringContext = StringContext;
exports.DelimiterContext = DelimiterContext;
csvAnalyzeParser.DelimiterContext = DelimiterContext;
exports.TabContext = TabContext;
csvAnalyzeParser.TabContext = TabContext;
exports.SemiContext = SemiContext;
csvAnalyzeParser.SemiContext = SemiContext;
exports.CommaContext = CommaContext;
csvAnalyzeParser.CommaContext = CommaContext;
