!function(e){var t={};function n(o){if(t[o])return t[o].exports;var i=t[o]={i:o,l:!1,exports:{}};return e[o].call(i.exports,i,i.exports,n),i.l=!0,i.exports}n.m=e,n.c=t,n.d=function(e,t,o){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:o})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var o=Object.create(null);if(n.r(o),Object.defineProperty(o,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var i in e)n.d(o,i,function(t){return e[t]}.bind(null,i));return o},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s=0)}([function(e,t,n){"use strict";var o=this&&this.__awaiter||function(e,t,n,o){return new(n||(n=Promise))((function(i,a){function r(e){try{u(o.next(e))}catch(e){a(e)}}function l(e){try{u(o.throw(e))}catch(e){a(e)}}function u(e){var t;e.done?i(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(r,l)}u((o=o.apply(e,t||[])).next())}))};Object.defineProperty(t,"__esModule",{value:!0});const i=n(1),a=n(2);i.default.plugins.register({onStart:function(){return o(this,void 0,void 0,(function*(){const e=i.default.views.dialogs,t=yield e.create("tags_dialog");yield i.default.views.dialogs.addScript(t,"modal_view.js"),yield i.default.views.dialogs.addScript(t,"modal_view.css"),yield e.setButtons(t,[{id:"ok",title:"Add"},{id:"save",title:"Save custom tag"},{id:"cancel",title:"Cancel"}]),yield i.default.settings.registerSection("settings.quickHTMLtags",{label:"Quick HTML tags",iconName:"fas fa-code"}),yield i.default.settings.registerSettings({tags:{value:"",type:a.SettingItemType.String,section:"settings.quickHTMLtags",public:!0,label:"HTML predefined tags",description:"Insert your predefined HTML tags here, seperated by semicolon ;"},enable_newlines:{value:!1,type:a.SettingItemType.Bool,section:"settings.quickHTMLtags",public:!0,label:"Enable new line breaking",description:"Enable new line breaking when selected text contains newlines. Inserts newlines at the front and back so that it renders correctly."},enable_markdown:{value:!1,type:a.SettingItemType.Bool,section:"settings.quickHTMLtags",public:!0,label:"Enable markdown inside tags",description:"Enables markdown styling inside HTML tags."}}),yield i.default.commands.register({name:"insert_quick_HTML_tag",label:"Quick HTML tag",iconName:"fas fa-code",execute:()=>o(this,void 0,void 0,(function*(){const n=yield i.default.settings.value("tags"),o=yield i.default.settings.value("enable_newlines"),a=yield i.default.settings.value("enable_markdown");var r=yield i.default.commands.execute("selectedText");let l=o&&/\r|\n/.exec(r);l&&(r="\n\n"+r+"\n"),yield e.setHtml(t,function(e){let t;1==e.length&&""===e[0]?t="<p>You can save a custom one here or set them under:<br><code>Tools > Options > Quick HTML tags</code></p>":(t='<label for="pretags">Choose a tag:</label><select name="pretag" id="pretags">',e.forEach(e=>t+=""!==e?`<option value=${e}>${e}</option>`:""),t+="</select>");return`\n\t<h2>Quick HTML tags</h2>\n\t<form id="qt_form" name="tag">\n\t\t${t}\n\t\t<br>\n\t\t<label for="custom_tag">Enter custom tag:</label>\n\t\t<input id="ctag" type="text" name="custom_tag"/>\n\t\t<br>\n\t\t<h2>Add attributes:</h2>\n\t\t<select name="attribute" id="attr">\n\t\t\t<option value="none">none</option>\n            <option value="style">style</option>\n            <option value="id">id</option>\n            <option value="class">class</option>\n        </select>\n        <label for="attrValue">Enter Attribute Value:</label>\n        <input type="text" name="attrValue">\n\t</form>`}(n.split(";")));const u=yield e.open(t);var s=u.formData.tag;if("save"==u.id&&s.custom_tag)return yield i.default.settings.setValue("tags",""===n?s.custom_tag:n+";"+s.custom_tag),void(yield i.default.commands.execute("editor.focus"));if("cancel"==u.id)return void(yield i.default.commands.execute("editor.focus"));let c,d=s.custom_tag?s.custom_tag:s.pretag;d||(d="div"),c="none"==s.attribute?`${l?"\n":""}<${d}${a?" markdown=1":""}>${r.length>1?r:""}</${d}>${l?"\n":""}`:`${l?"\n":""}<${d}${a?" markdown=1":""} ${s.attribute}="${s.attrValue}">${r.length>1?r:""}</${d}>${l?"\n":""}`,yield i.default.commands.execute("replaceSelection",c),yield i.default.commands.execute("editor.focus")}))}),yield i.default.views.toolbarButtons.create("insert_quick_HTML_tag","insert_quick_HTML_tag",a.ToolbarButtonLocation.EditorToolbar),yield i.default.views.menus.create("quick_HTML_tag","Insert HTML tag",[{commandName:"insert_quick_HTML_tag",accelerator:"Ctrl+H"}])}))}})},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=joplin},function(e,t,n){"use strict";var o;Object.defineProperty(t,"__esModule",{value:!0}),t.ContentScriptType=t.SettingStorage=t.AppType=t.SettingItemType=t.ToolbarButtonLocation=t.isContextMenuItemLocation=t.MenuItemLocation=t.ModelType=t.ImportModuleOutputFormat=t.FileSystemItem=void 0,function(e){e.File="file",e.Directory="directory"}(t.FileSystemItem||(t.FileSystemItem={})),function(e){e.Markdown="md",e.Html="html"}(t.ImportModuleOutputFormat||(t.ImportModuleOutputFormat={})),function(e){e[e.Note=1]="Note",e[e.Folder=2]="Folder",e[e.Setting=3]="Setting",e[e.Resource=4]="Resource",e[e.Tag=5]="Tag",e[e.NoteTag=6]="NoteTag",e[e.Search=7]="Search",e[e.Alarm=8]="Alarm",e[e.MasterKey=9]="MasterKey",e[e.ItemChange=10]="ItemChange",e[e.NoteResource=11]="NoteResource",e[e.ResourceLocalState=12]="ResourceLocalState",e[e.Revision=13]="Revision",e[e.Migration=14]="Migration",e[e.SmartFilter=15]="SmartFilter",e[e.Command=16]="Command"}(t.ModelType||(t.ModelType={})),function(e){e.File="file",e.Edit="edit",e.View="view",e.Note="note",e.Tools="tools",e.Help="help",e.Context="context",e.NoteListContextMenu="noteListContextMenu",e.EditorContextMenu="editorContextMenu",e.FolderContextMenu="folderContextMenu",e.TagContextMenu="tagContextMenu"}(o=t.MenuItemLocation||(t.MenuItemLocation={})),t.isContextMenuItemLocation=function(e){return[o.Context,o.NoteListContextMenu,o.EditorContextMenu,o.FolderContextMenu,o.TagContextMenu].includes(e)},function(e){e.NoteToolbar="noteToolbar",e.EditorToolbar="editorToolbar"}(t.ToolbarButtonLocation||(t.ToolbarButtonLocation={})),function(e){e[e.Int=1]="Int",e[e.String=2]="String",e[e.Bool=3]="Bool",e[e.Array=4]="Array",e[e.Object=5]="Object",e[e.Button=6]="Button"}(t.SettingItemType||(t.SettingItemType={})),function(e){e.Desktop="desktop",e.Mobile="mobile",e.Cli="cli"}(t.AppType||(t.AppType={})),function(e){e[e.Database=1]="Database",e[e.File=2]="File"}(t.SettingStorage||(t.SettingStorage={})),function(e){e.MarkdownItPlugin="markdownItPlugin",e.CodeMirrorPlugin="codeMirrorPlugin"}(t.ContentScriptType||(t.ContentScriptType={}))}]);