{"$schema": "https://joplinapp.org/schema/settings.json", "altInstanceId": "", "editor.codeView": false, "sync.target": 3, "sync.2.path": "C:\\Data\\Joplin Backup", "sync.10.username": "796434d7-425e-4bbd-9e28-3d95ec22c4d9", "richTextBannerDismissed": true, "locale": "en_GB", "dateFormat": "MM/DD/YYYY", "timeFormat": "h:mm A", "ocr.enabled": false, "ocr.languageDataPath": "https://cdn.jsdelivr.net/npm/@tesseract.js-data/", "themeAutoDetect": false, "layoutButtonSequence": 0, "notes.sortOrder.field": "order", "editor.pastePreserveColors": true, "notes.sortOrder.reverse": true, "notes.perFieldReverse": {"user_updated_time": true, "user_created_time": true, "title": false, "order": true, "todo_due": true, "todo_completed": true}, "notes.perFolderSortOrders": {"8fc09ffdbcaf416eb1c15df30066e431$field": "order", "8fc09ffdbcaf416eb1c15df30066e431$reverse": false, "5a8021a6c7e949bdba981a366b95edd3$field": "order", "5a8021a6c7e949bdba981a366b95edd3$reverse": false}, "trackLocation": false, "newNoteFocus": "title", "imageResizing": "neverResize", "notes.listRendererId": "compact", "markdown.plugin.softbreaks": false, "markdown.plugin.typographer": false, "markdown.plugin.linkify": true, "markdown.plugin.katex": false, "markdown.plugin.fountain": false, "markdown.plugin.mermaid": false, "markdown.plugin.audioPlayer": true, "markdown.plugin.videoPlayer": true, "markdown.plugin.pdfViewer": true, "markdown.plugin.sub": true, "markdown.plugin.sup": true, "markdown.plugin.emoji": true, "markdown.plugin.insert": true, "markdown.plugin.multitable": true, "renderer.fileUrls": true, "style.editor.fontSize": 11, "style.editor.fontFamily": "Input Mono", "style.editor.monospaceFontFamily": "Input Mono", "style.editor.contentMaxWidth": 1300, "ui.layout": {"key": "root", "children": [{"key": "tempContainer-fR9jOMxzt7GkcmLtxQxzNI", "children": [{"key": "sideBar", "visible": true}], "visible": true, "width": 239}, {"key": "tempContainer-TmyVZqHQhQAwEuX78zuiUx", "children": [{"key": "noteList", "visible": true}], "visible": true, "width": 257}, {"key": "tempContainer-brNvwgA0eUg2RYMwaV1czd", "children": [{"key": "plugin-view-joplin.plugin.benji.favorites-favorites.panel", "context": {"pluginId": "joplin.plugin.benji.favorites"}, "visible": true, "height": 40}, {"key": "plugin-view-joplin.plugin.note.tabs-note.tabs.panel", "context": {"pluginId": "joplin.plugin.note.tabs"}, "visible": true, "height": 40}, {"key": "editor", "visible": true}], "visible": true}], "visible": true}, "autoUpdate.includePreReleases": true, "clipperServer.autoStart": true, "sync.interval": 1800, "noteVisiblePanes": ["editor"], "editor": "\"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe\"", "editor.spellcheckBeta": true, "linking.extraAllowedExtensions": [".exe", ".201"], "sync.wipeOutFailSafe": true, "api.token": "410625f33f2165f247d3bdba328c95f6f15f1ecd7c32f7e64b5560fefd1111f2c717f091b1375dc6b57bc6ce5aca17b4619f2f2d779cf641b8d1e16b60514bbf", "spellChecker.enabled": false, "spellChecker.languages": ["en-US"], "windowContentZoomFactor": 100, "featureFlag.autoUpdaterServiceEnabled": true, "featureFlag.richText.useStrictContentSecurityPolicy": false, "plugin-io.github.personalizedrefrigerator.snippets-and-autocomplete.snippets-note-id": "[<PERSON><PERSON><PERSON> Snippets](:/762d7e78c4ce46349da6257d535e31c9)"}