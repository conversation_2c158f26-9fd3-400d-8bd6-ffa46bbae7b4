// Generated from csv.g4 by ANTLR 4.8
// jshint ignore: start
var antlr4 = require('antlr4/index');

// This class defines a complete listener for a parse tree produced by csvParser.
function csvListener() {
	antlr4.tree.ParseTreeListener.call(this);
	return this;
}

csvListener.prototype = Object.create(antlr4.tree.ParseTreeListener.prototype);
csvListener.prototype.constructor = csvListener;

// Enter a parse tree produced by csvParser#csvFile.
csvListener.prototype.enterCsvFile = function(ctx) {
};

// Exit a parse tree produced by csvParser#csvFile.
csvListener.prototype.exitCsvFile = function(ctx) {
};


// Enter a parse tree produced by csvParser#hdr.
csvListener.prototype.enterHdr = function(ctx) {
};

// Exit a parse tree produced by csvParser#hdr.
csvListener.prototype.exitHdr = function(ctx) {
};


// Enter a parse tree produced by csvParser#row.
csvListener.prototype.enterRow = function(ctx) {
};

// Exit a parse tree produced by csvParser#row.
csvListener.prototype.exitRow = function(ctx) {
};


// Enter a parse tree produced by csvParser#field.
csvListener.prototype.enterField = function(ctx) {
};

// Exit a parse tree produced by csvParser#field.
csvListener.prototype.exitField = function(ctx) {
};


// Enter a parse tree produced by csvParser#text.
csvListener.prototype.enterText = function(ctx) {
};

// Exit a parse tree produced by csvParser#text.
csvListener.prototype.exitText = function(ctx) {
};


// Enter a parse tree produced by csvParser#string.
csvListener.prototype.enterString = function(ctx) {
};

// Exit a parse tree produced by csvParser#string.
csvListener.prototype.exitString = function(ctx) {
};



exports.csvListener = csvListener;