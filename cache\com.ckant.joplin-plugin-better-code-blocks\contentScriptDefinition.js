(()=>{var e={4523:(e,n)=>{var t=Object.defineProperty;function r(e){return void 0===e?{BS_PRIVATE_NESTED_SOME_NONE:0}:null!==e&&void 0!==e.BS_PRIVATE_NESTED_SOME_NONE?{BS_PRIVATE_NESTED_SOME_NONE:e.BS_PRIVATE_NESTED_SOME_NONE+1|0}:e}function o(e){if(null===e||void 0===e.BS_PRIVATE_NESTED_SOME_NONE)return e;var n=e.BS_PRIVATE_NESTED_SOME_NONE;return 0===n?void 0:{BS_PRIVATE_NESTED_SOME_NONE:n-1|0}}function i(e,n){return e<n?-1:e===n?0:1}function u(e,n){return e===n?0:e<n?-1:1}function c(e,n){return e>n?e:n}((e,n)=>{for(var r in(e=>{t(e,"__esModule",{value:!0})})(e),n)t(e,r,{get:n[r],enumerable:!0})})(n,{all:()=>Ct,any:()=>St,append:()=>X,at:()=>re,concat:()=>un,concatMany:()=>cn,copy:()=>bn,deepFlat:()=>ut,difference:()=>wt,drop:()=>we,dropExactly:()=>ke,dropWhile:()=>Ae,eq:()=>mn,every:()=>an,filter:()=>je,filterMap:()=>dt,filterWithIndex:()=>Ie,find:()=>fe,flat:()=>rt,flatten:()=>it,flip:()=>lt,forEach:()=>Vn,forEachWithIndex:()=>Hn,get:()=>ne,getBy:()=>ae,getIndexBy:()=>Un,getUndefined:()=>ce,getUnsafe:()=>ie,groupBy:()=>tt,head:()=>de,includes:()=>zn,init:()=>pe,initOrEmpty:()=>me,insertAt:()=>Bn,intersection:()=>At,intersperse:()=>Z,isEmpty:()=>W,isNotEmpty:()=>H,join:()=>Jn,keep:()=>Le,keepMap:()=>vt,keepWithIndex:()=>Ge,last:()=>ge,length:()=>V,make:()=>I,makeEmpty:()=>x,makeWithIndex:()=>L,map:()=>Me,mapWithIndex:()=>Te,partition:()=>rn,placeholder:()=>T,prepend:()=>K,prependToAll:()=>Q,range:()=>En,rangeBy:()=>Cn,reduce:()=>Ue,reduceReverse:()=>ze,reduceWithIndex:()=>Je,reject:()=>Ve,rejectWithIndex:()=>He,removeAt:()=>Ln,removeFirst:()=>_t,removeFirstBy:()=>pt,repeat:()=>q,replaceAt:()=>An,reverse:()=>$,shuffle:()=>nn,slice:()=>gn,sliceToEnd:()=>hn,some:()=>fn,sort:()=>Yn,sortBy:()=>et,splitAt:()=>Ye,splitEvery:()=>en,swapAt:()=>xn,tail:()=>ve,tailOrEmpty:()=>he,take:()=>Ee,takeExactly:()=>Ce,takeWhile:()=>Se,tap:()=>at,toTuple:()=>ct,uncons:()=>Ne,union:()=>kt,uniq:()=>Gn,uniqBy:()=>In,unzip:()=>kn,updateAt:()=>Rn,zip:()=>On,zipWith:()=>Fn,zipWithIndex:()=>Et});var s=function(e,n){for(var t in e)n(t)};function a(e,n){if(e===n)return 0;var t,r=typeof e,o=typeof n;switch(r){case"boolean":if("boolean"===o)return t=n,e?t?0:1:t?-1:0;break;case"function":if("function"===o)throw{RE_EXN_ID:"Invalid_argument",_1:"compare: functional value",Error:new Error};break;case"number":if("number"===o)return i(e,n);break;case"string":return"string"===o?u(e,n):1;case"undefined":return-1}switch(o){case"string":return-1;case"undefined":return 1;default:if("boolean"===r)return 1;if("boolean"===o)return-1;if("function"===r)return 1;if("function"===o)return-1;if("number"===r)return null===n||void 0!==n.BS_PRIVATE_NESTED_SOME_NONE?1:-1;if("number"===o)return null===e||void 0!==e.BS_PRIVATE_NESTED_SOME_NONE?-1:1;if(null===e)return void 0!==n.BS_PRIVATE_NESTED_SOME_NONE?1:-1;if(null===n)return void 0!==e.BS_PRIVATE_NESTED_SOME_NONE?-1:1;if(void 0!==e.BS_PRIVATE_NESTED_SOME_NONE)return void 0!==n.BS_PRIVATE_NESTED_SOME_NONE?l(e,n):-1;var c=0|e.TAG,s=0|n.TAG;if(248===c)return i(e[1],n[1]);if(251===c)throw{RE_EXN_ID:"Invalid_argument",_1:"equal: abstract value",Error:new Error};if(c!==s)return c<s?-1:1;var f=0|e.length,d=0|n.length;if(f===d){if(!Array.isArray(e))return e instanceof Date&&n instanceof Date?e-n:l(e,n);for(var g=0;;){var v=g;if(v===f)return 0;var h=a(e[v],n[v]);if(0!==h)return h;g=v+1|0}}else if(f<d)for(var p=0;;){var m=p;if(m===f)return-1;var _=a(e[m],n[m]);if(0!==_)return _;p=m+1|0}else for(var E=0;;){var y=E;if(y===d)return 1;var C=a(e[y],n[y]);if(0!==C)return C;E=y+1|0}}}function l(e,n){var t={contents:void 0},r={contents:void 0},o=function(e,n){var t=e[2],r=e[1];if(!Object.prototype.hasOwnProperty.call(r,n)||a(e[0][n],r[n])>0){var o=t.contents;return void 0!==o&&n>=o?void 0:void(t.contents=n)}},i=[e,n,r],c=[n,e,t];s(e,(function(e){return o(i,e)})),s(n,(function(e){return o(c,e)}));var l=t.contents,f=r.contents;return void 0!==l?void 0!==f?u(l,f):-1:void 0!==f?1:0}function f(e,n){if(e===n)return!0;var t=typeof e;if("string"===t||"number"===t||"boolean"===t||"undefined"===t||null===e)return!1;var r=typeof n;if("function"===t||"function"===r)throw{RE_EXN_ID:"Invalid_argument",_1:"equal: functional value",Error:new Error};if("number"===r||"undefined"===r||null===n)return!1;var o=0|e.TAG,i=0|n.TAG;if(248===o)return e[1]===n[1];if(251===o)throw{RE_EXN_ID:"Invalid_argument",_1:"equal: abstract value",Error:new Error};if(o!==i)return!1;var u=0|e.length;if(u!==(0|n.length))return!1;if(!Array.isArray(e)){if(e instanceof Date&&n instanceof Date)return!(e>n||e<n);var c={contents:!0};return s(e,(function(e){Object.prototype.hasOwnProperty.call(n,e)||(c.contents=!1)})),c.contents&&s(n,(function(t){Object.prototype.hasOwnProperty.call(e,t)&&f(n[t],e[t])||(c.contents=!1)})),c.contents}for(var a=0;;){var l=a;if(l===u)return!0;if(!f(e[l],n[l]))return!1;a=l+1|0}}var d=2147483647,g=-2147483648;function v(e,n){if(n>=0&&n<e.length)return r(e[n])}function h(e,n){if(!(n>=0&&n<e.length))throw{RE_EXN_ID:"Assert_failure",_1:["belt_Array.ml",27,4],Error:new Error};return e[n]}function p(e,n,t){var r=e[n];e[n]=e[t],e[t]=r}function m(e,n){if(e<=0)return[];for(var t=new Array(e),r=0;r<e;++r)t[r]=n;return t}function _(e,n){for(var t=e.length,r=n.length,o=new Array(t+r|0),i=0;i<t;++i)o[i]=e[i];for(var u=0;u<r;++u)o[t+u|0]=n[u];return o}function E(e,n,t){if(t<=0)return[];var r=e.length,o=n<0?c(r+n|0,0):n,i=r-o|0,u=i<t?i:t;if(u<=0)return[];for(var s=new Array(u),a=0;a<u;++a)s[a]=e[o+a|0];return s}function y(e,n){for(var t=e.length,r=n<0?c(t+n|0,0):n,o=t-r|0,i=new Array(o),u=0;u<o;++u)i[u]=e[r+u|0];return i}function C(e,n,t,r,o){if(r<=n)for(var i=0;i<o;++i)t[i+r|0]=e[i+n|0];else for(var u=o-1|0;u>=0;--u)t[u+r|0]=e[u+n|0]}function b(e,n){for(var t=0,r=e.length;t<r;++t)n(e[t])}function S(e,n){for(var t,o=e.length,i=0;void 0===t&&i<o;){var u=e[i];n(u)&&(t=r(u)),i=i+1|0}return t}function O(e,n){for(var t=e.length,r=new Array(t),i=0,u=0;u<t;++u){var c=n(e[u]);void 0!==c&&(r[i]=o(c),i=i+1|0)}return r.length=i,r}function w(e,n){for(var t=e.length,r=new Array(t),o=0;o<t;++o)r[o]=n(o,e[o]);return r}function F(e,n,t){for(var r=n,o=0,i=e.length;o<i;++o)r=t(r,e[o]);return r}function k(e,n,t){for(var r=n,o=0,i=e.length;o<i;++o)r=t(r,e[o],o);return r}function P(e,n){for(var t=e.length,r=0;;){var o=r;if(o===t)return!0;if(!n(e[o]))return!1;r=o+1|0}}function A(e,n){for(var t=e.length,r=0;;){var o=r;if(o===t)return!1;if(n(e[o]))return!0;r=o+1|0}}function N(e,n,t,r,o,i,u,c,s){for(var a=n+t|0,l=o+i|0,f=n,d=e[n],g=o,v=r[o],h=c;;){var p=h,m=v,_=g,E=d,y=f;if(s(E,m)<=0){u[p]=E;var b=y+1|0;if(b>=a)return C(r,_,u,p+1|0,l-_|0);h=p+1|0,d=e[b],f=b}else{u[p]=m;var S=_+1|0;if(S>=l)return C(e,y,u,p+1|0,a-y|0);h=p+1|0,v=r[S],g=S}}}function B(e,n,t,r,o,i){for(var u=0;u<o;++u){for(var c=e[n+u|0],s=(r+u|0)-1|0;s>=r&&i(t[s],c)>0;)t[s+1|0]=t[s],s=s-1|0;t[s+1|0]=c}}function M(e,n,t,r,o,i){if(o<=5)return B(e,n,t,r,o,i);var u=o/2|0,c=o-u|0;return M(e,n+u|0,t,r+u|0,c,i),M(e,n,e,n+c|0,u,i),N(e,n+c|0,u,t,r+u|0,c,t,r,i)}function R(e,n){var t=e.slice(0);return function(e,n){var t=e.length;if(t<=5)return B(e,0,e,0,t,n);var r=t/2|0,o=t-r|0,i=new Array(o);M(e,r,i,0,o,n),M(e,0,e,o,r,n),N(e,o,r,i,0,o,e,0,n)}(t,n),t}function T(e){}function x(e){return[]}var j=function(e,n){if(e<=0)return[];for(var t=new Array(e),r=0;r<e;++r)t[r]=n(r);return t};function L(){if(1===arguments.length){const e=arguments;return function(n){return j(n,e[0])}}return j(arguments[0],arguments[1])}var D=m;function I(){if(1===arguments.length){const e=arguments;return function(n){return D(n,e[0])}}return D(arguments[0],arguments[1])}var G=m;function q(){if(1===arguments.length){const e=arguments;return function(n){return G(n,e[0])}}return G(arguments[0],arguments[1])}function V(e){return e.length}function W(e){return 0===e.length}function H(e){return 0!==e.length}var $=function(e){for(var n=e.length,t=new Array(n),r=0;r<n;++r)t[r]=e[(n-1|0)-r|0];return t};function U(e,n){return _(e,[n])}function X(){if(1===arguments.length){const e=arguments;return function(n){return U(n,e[0])}}return U(arguments[0],arguments[1])}function z(e,n){return _([n],e)}function K(){if(1===arguments.length){const e=arguments;return function(n){return z(n,e[0])}}return z(arguments[0],arguments[1])}function J(e,n){return F(e,[],(function(e,t){return _(e,[n,t])}))}function Q(){if(1===arguments.length){const e=arguments;return function(n){return J(n,e[0])}}return J(arguments[0],arguments[1])}function Y(e,n){return k(e,[],(function(t,r,o){return(e.length-1|0)===o?t.push(r):t.push(r,n),t}))}function Z(){if(1===arguments.length){const e=arguments;return function(n){return Y(n,e[0])}}return Y(arguments[0],arguments[1])}var ee=v;function ne(){if(1===arguments.length){const e=arguments;return function(n){return ee(n,e[0])}}return ee(arguments[0],arguments[1])}var te=v;function re(){if(1===arguments.length){const e=arguments;return function(n){return te(n,e[0])}}return te(arguments[0],arguments[1])}function oe(e,n){return e[n]}function ie(){if(1===arguments.length){const e=arguments;return function(n){return oe(n,e[0])}}return oe(arguments[0],arguments[1])}function ue(e,n){return e[n]}function ce(){if(1===arguments.length){const e=arguments;return function(n){return ue(n,e[0])}}return ue(arguments[0],arguments[1])}var se=S;function ae(){if(1===arguments.length){const e=arguments;return function(n){return se(n,e[0])}}return se(arguments[0],arguments[1])}var le=S;function fe(){if(1===arguments.length){const e=arguments;return function(n){return le(n,e[0])}}return le(arguments[0],arguments[1])}function de(e){return v(e,0)}function ge(e){var n=e.length;return 0===n?void 0:v(e,n-1|0)}function ve(e){var n=e.length;if(1===n)return[];if(0!==n){var t=y(e,1);return 0!==t.length?t:void 0}}function he(e){var n=ve(e);return void 0!==n?n:[]}function pe(e){var n=e.length;return 0===n?void 0:E(e,0,n-1|0)}function me(e){var n=pe(e);return void 0!==n?n:[]}function _e(e,n){var t=e.length;return E(e,0,n<0?0:t<n?t:n)}function Ee(){if(1===arguments.length){const e=arguments;return function(n){return _e(n,e[0])}}return _e(arguments[0],arguments[1])}function ye(e,n){return n<0||n>e.length?void 0:E(e,0,n)}function Ce(){if(1===arguments.length){const e=arguments;return function(n){return ye(n,e[0])}}return ye(arguments[0],arguments[1])}function be(e,n){return F(e,[],(function(e,t){return n(t)&&e.push(t),e}))}function Se(){if(1===arguments.length){const e=arguments;return function(n){return be(n,e[0])}}return be(arguments[0],arguments[1])}function Oe(e,n){var t=e.length;return y(e,n<0?0:t<n?t:n)}function we(){if(1===arguments.length){const e=arguments;return function(n){return Oe(n,e[0])}}return Oe(arguments[0],arguments[1])}function Fe(e,n){return n<0||n>e.length?void 0:y(e,n)}function ke(){if(1===arguments.length){const e=arguments;return function(n){return Fe(n,e[0])}}return Fe(arguments[0],arguments[1])}function Pe(e,n){return F(e,[],(function(e,t){return n(t)||e.push(t),e}))}function Ae(){if(1===arguments.length){const e=arguments;return function(n){return Pe(n,e[0])}}return Pe(arguments[0],arguments[1])}function Ne(e){if(0!==e.length)return[h(e,0),y(e,1)]}function Be(e,n){return function(e,n){for(var t=e.length,r=new Array(t),o=0;o<t;++o)r[o]=n(e[o]);return r}(e,n)}function Me(){if(1===arguments.length){const e=arguments;return function(n){return Be(n,e[0])}}return Be(arguments[0],arguments[1])}var Re=w;function Te(){if(1===arguments.length){const e=arguments;return function(n){return Re(n,e[0])}}return Re(arguments[0],arguments[1])}function xe(e,n){for(var t=0,r=[];t<e.length;){var o=e[t];n(o)&&r.push(o),t=t+1|0}return r}function je(){if(1===arguments.length){const e=arguments;return function(n){return xe(n,e[0])}}return xe(arguments[0],arguments[1])}var Le=je;function De(e,n){for(var t=0,r=[];t<e.length;){var o=e[t];n(t,o)&&r.push(o),t=t+1|0}return r}function Ie(){if(1===arguments.length){const e=arguments;return function(n){return De(n,e[0])}}return De(arguments[0],arguments[1])}var Ge=Ie;function qe(e,n){return je(e,(function(e){return!n(e)}))}function Ve(){if(1===arguments.length){const e=arguments;return function(n){return qe(n,e[0])}}return qe(arguments[0],arguments[1])}function We(e,n){return Ie(e,(function(e,t){return!n(e,t)}))}function He(){if(1===arguments.length){const e=arguments;return function(n){return We(n,e[0])}}return We(arguments[0],arguments[1])}var $e=F;function Ue(){if(2===arguments.length){const e=arguments;return function(n){return $e(n,e[0],e[1])}}return $e(arguments[0],arguments[1],arguments[2])}var Xe=function(e,n,t){for(var r=n,o=e.length-1|0;o>=0;--o)r=t(r,e[o]);return r};function ze(){if(2===arguments.length){const e=arguments;return function(n){return Xe(n,e[0],e[1])}}return Xe(arguments[0],arguments[1],arguments[2])}var Ke=k;function Je(){if(2===arguments.length){const e=arguments;return function(n){return Ke(n,e[0],e[1])}}return Ke(arguments[0],arguments[1],arguments[2])}function Qe(e,n){return n<0||n>e.length?void 0:[E(e,0,n),y(e,n)]}function Ye(){if(1===arguments.length){const e=arguments;return function(n){return Qe(n,e[0])}}return Qe(arguments[0],arguments[1])}function Ze(e,n){if(n<1||e.length<=n)return[e];for(var t=0,r=[];t<e.length;){var o=t+n|0;r.push(E(e,t,n)),t=o}return r}function en(){if(1===arguments.length){const e=arguments;return function(n){return Ze(n,e[0])}}return Ze(arguments[0],arguments[1])}var nn=function(e){var n=e.slice(0);return function(e){for(var n=e.length,t=0;t<n;++t)p(e,t,(r=t,o=n,i=void 0,0|((i=Math.random()*(o-r|0))>d?d:i<g?g:Math.floor(i))+r));var r,o,i}(n),n},tn=function(e,n){for(var t=e.length,r=0,o=0,i=new Array(t),u=new Array(t),c=0;c<t;++c){var s=e[c];n(s)?(i[r]=s,r=r+1|0):(u[o]=s,o=o+1|0)}return i.length=r,u.length=o,[i,u]};function rn(){if(1===arguments.length){const e=arguments;return function(n){return tn(n,e[0])}}return tn(arguments[0],arguments[1])}var on=_;function un(){if(1===arguments.length){const e=arguments;return function(n){return on(n,e[0])}}return on(arguments[0],arguments[1])}var cn=function(e){for(var n=e.length,t=0,r=0;r<n;++r)t=t+e[r].length|0;var o=new Array(t);t=0;for(var i=0;i<n;++i)for(var u=e[i],c=0,s=u.length;c<s;++c)o[t]=u[c],t=t+1|0;return o},sn=P;function an(){if(1===arguments.length){const e=arguments;return function(n){return sn(n,e[0])}}return sn(arguments[0],arguments[1])}var ln=A;function fn(){if(1===arguments.length){const e=arguments;return function(n){return ln(n,e[0])}}return ln(arguments[0],arguments[1])}var dn=E;function gn(){if(2===arguments.length){const e=arguments;return function(n){return dn(n,e[0],e[1])}}return dn(arguments[0],arguments[1],arguments[2])}var vn=y;function hn(){if(1===arguments.length){const e=arguments;return function(n){return vn(n,e[0])}}return vn(arguments[0],arguments[1])}var pn=function(e,n,t){var r=e.length;return r===n.length&&function(e,n,t,r,o){for(;;){var i=t;if(i===o)return!0;if(!r(e[i],n[i]))return!1;t=i+1|0}}(e,n,0,t,r)};function mn(){if(2===arguments.length){const e=arguments;return function(n){return pn(n,e[0],e[1])}}return pn(arguments[0],arguments[1],arguments[2])}var _n=function(e,n){var t=n-e|0;if(t<0)return[];for(var r=new Array(t+1|0),o=0;o<=t;++o)r[o]=e+o|0;return r};function En(){if(1===arguments.length){const e=arguments;return function(n){return _n(n,e[0])}}return _n(arguments[0],arguments[1])}var yn=function(e,n,t){var r=n-e|0;if(r<0||t<=0)return[];for(var o=1+(r/t|0)|0,i=new Array(o),u=e,c=0;c<o;++c)i[c]=u,u=u+t|0;return i};function Cn(){if(2===arguments.length){const e=arguments;return function(n){return yn(n,e[0],e[1])}}return yn(arguments[0],arguments[1],arguments[2])}function bn(e){return e.slice(0)}var Sn=function(e,n){for(var t=e.length,r=n.length,o=t<r?t:r,i=new Array(o),u=0;u<o;++u)i[u]=[e[u],n[u]];return i};function On(){if(1===arguments.length){const e=arguments;return function(n){return Sn(n,e[0])}}return Sn(arguments[0],arguments[1])}var wn=function(e,n,t){for(var r=e.length,o=n.length,i=r<o?r:o,u=new Array(i),c=0;c<i;++c)u[c]=t(e[c],n[c]);return u};function Fn(){if(2===arguments.length){const e=arguments;return function(n){return wn(n,e[0],e[1])}}return wn(arguments[0],arguments[1],arguments[2])}var kn=function(e){for(var n=e.length,t=new Array(n),r=new Array(n),o=0;o<n;++o){var i=e[o];t[o]=i[0],r[o]=i[1]}return[t,r]};function Pn(e,n,t){return w(e,(function(e,r){return e===n?t:r}))}function An(){if(2===arguments.length){const e=arguments;return function(n){return Pn(n,e[0],e[1])}}return Pn(arguments[0],arguments[1],arguments[2])}function Nn(e,n,t){var r=Ye(e,n);return void 0!==r?_(r[0],_([t],r[1])):e}function Bn(){if(2===arguments.length){const e=arguments;return function(n){return Nn(n,e[0],e[1])}}return Nn(arguments[0],arguments[1],arguments[2])}function Mn(e,n,t){return w(e,(function(e,r){return e===n?t(r):r}))}function Rn(){if(2===arguments.length){const e=arguments;return function(n){return Mn(n,e[0],e[1])}}return Mn(arguments[0],arguments[1],arguments[2])}function Tn(e,n,t){var r=v(e,n),i=v(e,t);if(void 0===r)return e;if(void 0===i)return e;var u=o(i),c=o(r);return w(e,(function(e,r){return n===e?u:t===e?c:r}))}function xn(){if(2===arguments.length){const e=arguments;return function(n){return Tn(n,e[0],e[1])}}return Tn(arguments[0],arguments[1],arguments[2])}function jn(e,n){return Ie(e,(function(e,t){return e!==n}))}function Ln(){if(1===arguments.length){const e=arguments;return function(n){return jn(n,e[0])}}return jn(arguments[0],arguments[1])}function Dn(e,n){for(var t=0,r=[];t<e.length;){var o=e[t];A(r,function(e){return function(t){return f(n(t),n(e))}}(o))||r.push(o),t=t+1|0}return r}function In(){if(1===arguments.length){const e=arguments;return function(n){return Dn(n,e[0])}}return Dn(arguments[0],arguments[1])}function Gn(e){return In(e,(function(e){return e}))}var qn=b;function Vn(){if(1===arguments.length){const e=arguments;return function(n){return qn(n,e[0])}}return qn(arguments[0],arguments[1])}var Wn=function(e,n){for(var t=0,r=e.length;t<r;++t)n(t,e[t])};function Hn(){if(1===arguments.length){const e=arguments;return function(n){return Wn(n,e[0])}}return Wn(arguments[0],arguments[1])}var $n=function(e,n){for(var t,r=e.length,o=0;void 0===t&&o<r;)n(e[o])&&(t=o),o=o+1|0;return t};function Un(){if(1===arguments.length){const e=arguments;return function(n){return $n(n,e[0])}}return $n(arguments[0],arguments[1])}function Xn(e,n){return A(e,(function(e){return f(e,n)}))}function zn(){if(1===arguments.length){const e=arguments;return function(n){return Xn(n,e[0])}}return Xn(arguments[0],arguments[1])}function Kn(e,n){return e.join(n)}function Jn(){if(1===arguments.length){const e=arguments;return function(n){return Kn(n,e[0])}}return Kn(arguments[0],arguments[1])}var Qn=R;function Yn(){if(1===arguments.length){const e=arguments;return function(n){return Qn(n,e[0])}}return Qn(arguments[0],arguments[1])}function Zn(e,n){return R(e,(function(e,t){var r=n(e),o=n(t);return r===o?0:function(e,n){return a(e,n)<0}(r,o)?-1:1}))}function et(){if(1===arguments.length){const e=arguments;return function(n){return Zn(n,e[0])}}return Zn(arguments[0],arguments[1])}function nt(e,n){return F(e,{},(function(e,t){var o=n(t),i=function(e,n){if(n in e)return r(e[n])}(e,o);return void 0!==i?i.push(t):e[o]=[t],e}))}function tt(){if(1===arguments.length){const e=arguments;return function(n){return nt(n,e[0])}}return nt(arguments[0],arguments[1])}function rt(e){return F(e,[],(function(e,n){return Array.isArray(n)?b(n,(function(n){e.push(n)})):e.push(n),e}))}function ot(e,n){for(var t=0;t<e.length;){var r=e[t];Array.isArray(r)?it(r,n):n.push(r),t=t+1|0}return n}function it(){if(1===arguments.length){const e=arguments;return function(n){return ot(n,e[0])}}return ot(arguments[0],arguments[1])}function ut(e){return it(e,[])}function ct(e){return e}function st(e,n){return b(e,n),e}function at(){if(1===arguments.length){const e=arguments;return function(n){return st(n,e[0])}}return st(arguments[0],arguments[1])}function lt(e){return[e[1],e[0]]}var ft=O;function dt(){if(1===arguments.length){const e=arguments;return function(n){return ft(n,e[0])}}return ft(arguments[0],arguments[1])}var gt=O;function vt(){if(1===arguments.length){const e=arguments;return function(n){return gt(n,e[0])}}return gt(arguments[0],arguments[1])}function ht(e,n,t){return F(e,[!1,[]],(function(e,r){var o=e[1];return e[0]?(o.push(r),[!0,o]):t(r,n)?[!0,o]:(o.push(r),[!1,o])}))[1]}function pt(){if(2===arguments.length){const e=arguments;return function(n){return ht(n,e[0],e[1])}}return ht(arguments[0],arguments[1],arguments[2])}function mt(e,n){return pt(e,n,f)}function _t(){if(1===arguments.length){const e=arguments;return function(n){return mt(n,e[0])}}return mt(arguments[0],arguments[1])}function Et(e){return k(e,[],(function(e,n,t){return e.push([n,t]),e}))}function yt(e,n){return P(e,n)}function Ct(){if(1===arguments.length){const e=arguments;return function(n){return yt(n,e[0])}}return yt(arguments[0],arguments[1])}function bt(e,n){return A(e,n)}function St(){if(1===arguments.length){const e=arguments;return function(n){return bt(n,e[0])}}return bt(arguments[0],arguments[1])}function Ot(e,n){return Ve(In(e,(function(e){return e})),(function(e){return zn(n,e)}))}function wt(){if(1===arguments.length){const e=arguments;return function(n){return Ot(n,e[0])}}return Ot(arguments[0],arguments[1])}function Ft(e,n){return In(_(e,n),(function(e){return e}))}function kt(){if(1===arguments.length){const e=arguments;return function(n){return Ft(n,e[0])}}return Ft(arguments[0],arguments[1])}function Pt(e,n){var t=e.length>n.length?[e,n]:[n,e],r=t[1];return In(je(t[0],(function(e){return zn(r,e)})),(function(e){return e}))}function At(){if(1===arguments.length){const e=arguments;return function(n){return Pt(n,e[0])}}return Pt(arguments[0],arguments[1])}},1982:(e,n)=>{var t=Object.defineProperty;function r(e){}function o(e,n,t){return e?n(void 0):t(void 0)}function i(){if(2===arguments.length){const e=arguments;return function(n){return o(n,e[0],e[1])}}return o(arguments[0],arguments[1],arguments[2])}function u(e){return!e}function c(e){return!e}function s(e,n){return!!e&&n}function a(){if(1===arguments.length){const e=arguments;return function(n){return s(n,e[0])}}return s(arguments[0],arguments[1])}function l(e,n){return!!e||n}function f(){if(1===arguments.length){const e=arguments;return function(n){return l(n,e[0])}}return l(arguments[0],arguments[1])}function d(e,n){return!(e&&n)}function g(){if(1===arguments.length){const e=arguments;return function(n){return d(n,e[0])}}return d(arguments[0],arguments[1])}function v(e,n){return!(e||n)}function h(){if(1===arguments.length){const e=arguments;return function(n){return v(n,e[0])}}return v(arguments[0],arguments[1])}function p(e,n){return!(e||!n)||!!e&&!n}function m(){if(1===arguments.length){const e=arguments;return function(n){return p(n,e[0])}}return p(arguments[0],arguments[1])}function _(e,n){return!m(e,n)}function E(){if(1===arguments.length){const e=arguments;return function(n){return _(n,e[0])}}return _(arguments[0],arguments[1])}function y(e,n){return!e||n}function C(){if(1===arguments.length){const e=arguments;return function(n){return y(n,e[0])}}return y(arguments[0],arguments[1])}((e,n)=>{for(var r in(e=>{t(e,"__esModule",{value:!0})})(e),n)t(e,r,{get:n[r],enumerable:!0})})(n,{and:()=>a,ifElse:()=>i,implies:()=>C,inverse:()=>u,nand:()=>g,nor:()=>h,not:()=>c,or:()=>f,placeholder:()=>r,xnor:()=>E,xor:()=>m})},6198:(e,n)=>{var t=Object.defineProperty;function r(e,n){if(n in e)return void 0===(t=e[n])?{BS_PRIVATE_NESTED_SOME_NONE:0}:null!==t&&void 0!==t.BS_PRIVATE_NESTED_SOME_NONE?{BS_PRIVATE_NESTED_SOME_NONE:t.BS_PRIVATE_NESTED_SOME_NONE+1|0}:t;var t}((e,n)=>{for(var r in(e=>{t(e,"__esModule",{value:!0})})(e),n)t(e,r,{get:n[r],enumerable:!0})})(n,{deleteKey:()=>R,deleteKeys:()=>x,filter:()=>q,filterWithKey:()=>W,fromPairs:()=>S,get:()=>m,getUnsafe:()=>h,isEmpty:()=>J,isNotEmpty:()=>Q,keys:()=>b,makeEmpty:()=>g,map:()=>L,mapWithKey:()=>I,merge:()=>w,placeholder:()=>d,prop:()=>E,reject:()=>$,rejectWithKey:()=>X,selectKeys:()=>K,set:()=>k,toPairs:()=>y,update:()=>A,updateUnsafe:()=>B,values:()=>C});var o=function(e,n){delete e[n]};function i(e){for(var n={},t=e.length,r=0;r<t;++r){var o=e[r];n[o[0]]=o[1]}return n}var u=function(e,n){for(var t in e)n(t)};function c(e,n){if(e===n)return!0;var t=typeof e;if("string"===t||"number"===t||"boolean"===t||"undefined"===t||null===e)return!1;var r=typeof n;if("function"===t||"function"===r)throw{RE_EXN_ID:"Invalid_argument",_1:"equal: functional value",Error:new Error};if("number"===r||"undefined"===r||null===n)return!1;var o=0|e.TAG,i=0|n.TAG;if(248===o)return e[1]===n[1];if(251===o)throw{RE_EXN_ID:"Invalid_argument",_1:"equal: abstract value",Error:new Error};if(o!==i)return!1;var s=0|e.length;if(s!==(0|n.length))return!1;if(!Array.isArray(e)){if(e instanceof Date&&n instanceof Date)return!(e>n||e<n);var a={contents:!0};return u(e,(function(e){Object.prototype.hasOwnProperty.call(n,e)||(a.contents=!1)})),a.contents&&u(n,(function(t){Object.prototype.hasOwnProperty.call(e,t)&&c(n[t],e[t])||(a.contents=!1)})),a.contents}for(var l=0;;){var f=l;if(f===s)return!0;if(!c(e[f],n[f]))return!1;l=f+1|0}}function s(e,n){for(var t=e.length,r=new Array(t),o=0;o<t;++o)r[o]=n(e[o]);return r}function a(e,n,t){for(var r=n,o=0,i=e.length;o<i;++o)r=t(r,e[o]);return r}function l(e,n){return function(e,n){for(var t=e.length,r=n.length,o=new Array(t+r|0),i=0;i<t;++i)o[i]=e[i];for(var u=0;u<r;++u)o[t+u|0]=n[u];return o}(e,[n])}function f(){if(1===arguments.length){const e=arguments;return function(n){return l(n,e[0])}}return l(arguments[0],arguments[1])}function d(e){}function g(e){return{}}function v(e,n){return e[n]}function h(){if(1===arguments.length){const e=arguments;return function(n){return v(n,e[0])}}return v(arguments[0],arguments[1])}var p=r;function m(){if(1===arguments.length){const e=arguments;return function(n){return p(n,e[0])}}return p(arguments[0],arguments[1])}function _(e,n){return e[n]}function E(){if(1===arguments.length){const e=arguments;return function(n){return _(n,e[0])}}return _(arguments[0],arguments[1])}function y(e){return Object.entries(e)}var C=function(e){for(var n=Object.keys(e),t=n.length,r=new Array(t),o=0;o<t;++o)r[o]=e[n[o]];return r};function b(e){return Object.keys(e)}var S=i;function O(e,n){return Object.assign({},e,n)}function w(){if(1===arguments.length){const e=arguments;return function(n){return O(n,e[0])}}return O(arguments[0],arguments[1])}function F(e,n,t){var r=w({},e);return r[n]=t,r}function k(){if(2===arguments.length){const e=arguments;return function(n){return F(n,e[0],e[1])}}return F(arguments[0],arguments[1],arguments[2])}function P(e,n,t){return k(e,n,t(r(e,n)))}function A(){if(2===arguments.length){const e=arguments;return function(n){return P(n,e[0],e[1])}}return P(arguments[0],arguments[1],arguments[2])}function N(e,n,t){return k(e,n,t(e[n]))}function B(){if(2===arguments.length){const e=arguments;return function(n){return N(n,e[0],e[1])}}return N(arguments[0],arguments[1],arguments[2])}function M(e,n){var t=w({},e);return o(t,n),t}function R(){if(1===arguments.length){const e=arguments;return function(n){return M(n,e[0])}}return M(arguments[0],arguments[1])}function T(e,n){var t=w({},e);return function(e){for(var n=0,r=e.length;n<r;++n)i=e[n],o(t,i);var i}(n),t}function x(){if(1===arguments.length){const e=arguments;return function(n){return T(n,e[0])}}return T(arguments[0],arguments[1])}function j(e,n){return i(s(Object.keys(e),(function(t){return[t,n(e[t])]})))}function L(){if(1===arguments.length){const e=arguments;return function(n){return j(n,e[0])}}return j(arguments[0],arguments[1])}function D(e,n){return i(s(Object.keys(e),(function(t){return[t,n(t,e[t])]})))}function I(){if(1===arguments.length){const e=arguments;return function(n){return D(n,e[0])}}return D(arguments[0],arguments[1])}function G(e,n){return i(a(Object.keys(e),[],(function(t,r){var o=e[r];return n(o)?f(t,[r,o]):t})))}function q(){if(1===arguments.length){const e=arguments;return function(n){return G(n,e[0])}}return G(arguments[0],arguments[1])}function V(e,n){return i(a(Object.keys(e),[],(function(t,r){var o=e[r];return n(r,o)?f(t,[r,o]):t})))}function W(){if(1===arguments.length){const e=arguments;return function(n){return V(n,e[0])}}return V(arguments[0],arguments[1])}function H(e,n){return q(e,(function(e){return!n(e)}))}function $(){if(1===arguments.length){const e=arguments;return function(n){return H(n,e[0])}}return H(arguments[0],arguments[1])}function U(e,n){return W(e,(function(e,t){return!n(e,t)}))}function X(){if(1===arguments.length){const e=arguments;return function(n){return U(n,e[0])}}return U(arguments[0],arguments[1])}function z(e,n){return W(e,(function(e,t){return n.includes(e)}))}function K(){if(1===arguments.length){const e=arguments;return function(n){return z(n,e[0])}}return z(arguments[0],arguments[1])}function J(e){return c(e,{})}function Q(e){return!c(e,{})}},1654:(e,n)=>{var t=Object.defineProperty;function r(e){return void 0===e?{BS_PRIVATE_NESTED_SOME_NONE:0}:null!==e&&void 0!==e.BS_PRIVATE_NESTED_SOME_NONE?{BS_PRIVATE_NESTED_SOME_NONE:e.BS_PRIVATE_NESTED_SOME_NONE+1|0}:e}function o(e){if(null===e||void 0===e.BS_PRIVATE_NESTED_SOME_NONE)return e;var n=e.BS_PRIVATE_NESTED_SOME_NONE;return 0===n?void 0:{BS_PRIVATE_NESTED_SOME_NONE:n-1|0}}((e,n)=>{for(var r in(e=>{t(e,"__esModule",{value:!0})})(e),n)t(e,r,{get:n[r],enumerable:!0})})(n,{after:()=>ne,allPass:()=>L,always:()=>O,anyPass:()=>I,before:()=>Z,both:()=>C,coerce:()=>ce,debounce:()=>K,defaultTo:()=>F,either:()=>S,equals:()=>E,falsy:()=>k,identity:()=>m,ifElse:()=>N,ignore:()=>B,makeControlledDebounce:()=>X,makeControlledThrottle:()=>W,memoize:()=>re,memoizeWithKey:()=>ie,once:()=>te,placeholder:()=>p,tap:()=>q,throttle:()=>$,toMutable:()=>ue,truthy:()=>P,tryCatch:()=>Q,unless:()=>R,when:()=>x});var i={contents:0};function u(e){return i.contents=i.contents+1|0,e+"/"+i.contents}var c=u("Caml_js_exceptions.Error"),s=c,a=function(e,n){for(var t in e)n(t)};function l(e,n,t){return void 0!==e?t(o(e)):n}function f(e){return void 0!==e?e.h:0}function d(e,n,t,r){var o=f(e),i=f(r);return{k:n,v:t,h:o>=i?o+1|0:i+1|0,l:e,r}}function g(e,n,t,r){var o=void 0!==e?e.h:0,i=void 0!==r?r.h:0;if(o>(i+2|0)){var u=e.l,c=e.r;return f(u)>=f(c)?d(u,e.k,e.v,d(c,n,t,r)):d(d(u,e.k,e.v,c.l),c.k,c.v,d(c.r,n,t,r))}if(i<=(o+2|0))return{k:n,v:t,h:o>=i?o+1|0:i+1|0,l:e,r};var s=r.l,a=r.r;return f(a)>=f(s)?d(d(e,n,t,s),r.k,r.v,a):d(d(e,n,t,s.l),s.k,s.v,d(s.r,r.k,r.v,a))}function v(e,n,t){if(void 0===e)return{k:n,v:t,h:1,l:void 0,r:void 0};var r,o,i=e.k;if(n===i)return o=t,(r=e).v===o?r:{k:r.k,v:o,h:r.h,l:r.l,r:r.r};var u=e.v;return n<i?g(v(e.l,n,t),i,u,e.r):g(e.l,i,u,v(e.r,n,t))}var h=function(e,n){for(;;){var t=e;if(void 0===t)return;var o=t.k;if(n===o)return r(t.v);e=n<o?t.l:t.r}};function p(e){}function m(e){return e}var _=function e(n,t){if(n===t)return!0;var r=typeof n;if("string"===r||"number"===r||"boolean"===r||"undefined"===r||null===n)return!1;var o=typeof t;if("function"===r||"function"===o)throw{RE_EXN_ID:"Invalid_argument",_1:"equal: functional value",Error:new Error};if("number"===o||"undefined"===o||null===t)return!1;var i=0|n.TAG,u=0|t.TAG;if(248===i)return n[1]===t[1];if(251===i)throw{RE_EXN_ID:"Invalid_argument",_1:"equal: abstract value",Error:new Error};if(i!==u)return!1;var c=0|n.length;if(c!==(0|t.length))return!1;if(!Array.isArray(n)){if(n instanceof Date&&t instanceof Date)return!(n>t||n<t);var s={contents:!0};return a(n,(function(e){Object.prototype.hasOwnProperty.call(t,e)||(s.contents=!1)})),s.contents&&a(t,(function(r){Object.prototype.hasOwnProperty.call(n,r)&&e(t[r],n[r])||(s.contents=!1)})),s.contents}for(var l=0;;){var f=l;if(f===c)return!0;if(!e(n[f],t[f]))return!1;l=f+1|0}};function E(){if(1===arguments.length){const e=arguments;return function(n){return _(n,e[0])}}return _(arguments[0],arguments[1])}function y(e,n,t){return!!n(e)&&t(e)}function C(){if(2===arguments.length){const e=arguments;return function(n){return y(n,e[0],e[1])}}return y(arguments[0],arguments[1],arguments[2])}function b(e,n,t){return!!n(e)||t(e)}function S(){if(2===arguments.length){const e=arguments;return function(n){return b(n,e[0],e[1])}}return b(arguments[0],arguments[1],arguments[2])}function O(e){return function(){return e}}function w(e,n){return null==e?n:e}function F(){if(1===arguments.length){const e=arguments;return function(n){return w(n,e[0])}}return w(arguments[0],arguments[1])}function k(){return!1}function P(){return!0}function A(e,n,t,r){return n(e)?t(e):r(e)}function N(){if(3===arguments.length){const e=arguments;return function(n){return A(n,e[0],e[1],e[2])}}return A(arguments[0],arguments[1],arguments[2],arguments[3])}function B(e){}function M(e,n,t){return n(e)?e:t(e)}function R(){if(2===arguments.length){const e=arguments;return function(n){return M(n,e[0],e[1])}}return M(arguments[0],arguments[1],arguments[2])}function T(e,n,t){return n(e)?t(e):e}function x(){if(2===arguments.length){const e=arguments;return function(n){return T(n,e[0],e[1])}}return T(arguments[0],arguments[1],arguments[2])}function j(e,n){return function(n){for(var t=n.length,r=0;;){var o=r;if(o===t)return!0;if(!(0,n[o])(e))return!1;r=o+1|0}}(n)}function L(){if(1===arguments.length){const e=arguments;return function(n){return j(n,e[0])}}return j(arguments[0],arguments[1])}function D(e,n){return function(n){for(var t=n.length,r=0;;){var o=r;if(o===t)return!1;if((0,n[o])(e))return!0;r=o+1|0}}(n)}function I(){if(1===arguments.length){const e=arguments;return function(n){return D(n,e[0])}}return D(arguments[0],arguments[1])}function G(e,n){return n(e),e}function q(){if(1===arguments.length){const e=arguments;return function(n){return G(n,e[0])}}return G(arguments[0],arguments[1])}function V(e,n){var t={contents:!1},o={contents:void 0},i=function(e){l(o.contents,void 0,(function(e){clearTimeout(e)})),o.contents=void 0},u={contents:n.leading};return{cancel:i,invoke:function(...n){return i(),e(...n)},isScheduled:function(e){return t.contents},schedule:function(...c){if(u.contents)return u.contents=!1,e(...c);if(!t.contents){i(),t.contents=!0,e(...c);var s=setTimeout((function(e){t.contents=!1,o.contents=void 0}),n.delay);o.contents=r(s)}}}}function W(){if(1===arguments.length){const e=arguments;return function(n){return V(n,e[0])}}return V(arguments[0],arguments[1])}function H(e,n){return W(e,{delay:n,leading:!1}).schedule}function $(){if(1===arguments.length){const e=arguments;return function(n){return H(n,e[0])}}return H(arguments[0],arguments[1])}function U(e,n){var t={contents:void 0},o=function(e){l(t.contents,void 0,(function(e){clearTimeout(e)})),t.contents=void 0},i={contents:n.leading};return{cancel:o,invoke:function(...n){return o(),e(...n)},isScheduled:function(e){return function(e){return void 0!==e}(t.contents)},schedule:function(...u){if(i.contents)return i.contents=!1,e(...u);o();var c=setTimeout((function(n){e(...u),t.contents=void 0}),n.delay);t.contents=r(c)}}}function X(){if(1===arguments.length){const e=arguments;return function(n){return U(n,e[0])}}return U(arguments[0],arguments[1])}function z(e,n){return X(e,{delay:n,leading:!1}).schedule}function K(){if(1===arguments.length){const e=arguments;return function(n){return z(n,e[0])}}return z(arguments[0],arguments[1])}function J(e,n){try{return{TAG:0,_0:n(e)}}catch(e){var t=function(e){return null!=e&&"string"==typeof e.RE_EXN_ID}(o=e)?o:{RE_EXN_ID:c,_1:o};if(t.RE_EXN_ID===s){var r=t._1.message;return void 0!==r?{TAG:1,_0:r}:{TAG:1,_0:"F.tryCatch: unknown error"}}throw t}var o}function Q(){if(1===arguments.length){const e=arguments;return function(n){return J(n,e[0])}}return J(arguments[0],arguments[1])}function Y(e,n){var t={contents:0},i={contents:void 0};return function(...u){var c=i.contents;if(void 0!==c){if(t.contents>=e)return o(c);var s=n(...u);return i.contents=r(s),t.contents=t.contents+1|0,s}var a=n(...u);return i.contents=r(a),t.contents=t.contents+1|0,a}}function Z(){if(1===arguments.length){const e=arguments;return function(n){return Y(n,e[0])}}return Y(arguments[0],arguments[1])}function ee(e,n){var t={contents:0};return function(...o){return t.contents<e?void(t.contents=t.contents+1|0):r(n(...o))}}function ne(){if(1===arguments.length){const e=arguments;return function(n){return ee(n,e[0])}}return ee(arguments[0],arguments[1])}function te(e){var n={contents:void 0};return function(...t){var i=n.contents;if(void 0!==i)return o(i);var u=e(...t);return n.contents=r(u),u}}var re=te;function oe(e,n){var t={contents:void 0};return function(...r){var i=e(...r),u=h(t.contents,i);if(void 0!==u)return o(u);var c=n(...r);return t.contents=v(t.contents,i,c),c}}function ie(){if(1===arguments.length){const e=arguments;return function(n){return oe(n,e[0])}}return oe(arguments[0],arguments[1])}function ue(e){return e}function ce(e){return e}},1564:(e,n)=>{var t=Object.defineProperty;function r(e,n){return typeof e===n}function o(){if(1===arguments.length){const e=arguments;return function(n){return r(n,e[0])}}return r(arguments[0],arguments[1])}function i(e){return"string"==typeof e}function u(e){return"number"==typeof e&&!Number.isNaN(e)}function c(e){return"boolean"==typeof e}((e,n)=>{for(var r in(e=>{t(e,"__esModule",{value:!0})})(e),n)t(e,r,{get:n[r],enumerable:!0})})(n,{is:()=>o,isArray:()=>a,isBoolean:()=>c,isDate:()=>g,isError:()=>d,isFunction:()=>f,isNot:()=>E,isNotNullable:()=>h,isNull:()=>p,isNullable:()=>v,isNumber:()=>u,isObject:()=>l,isPromise:()=>s,isString:()=>i,isUndefined:()=>m});var s=e=>e instanceof Promise;function a(e){return Array.isArray(e)}function l(e){return!(!e||Array.isArray(e))&&"object"==typeof e}function f(e){return"function"==typeof e}var d=e=>e instanceof Error,g=e=>e instanceof Date;function v(e){return null==e}function h(e){return!(null==e)}var p=e=>null===e,m=e=>void 0===e;function _(e,n){return!n(e)}function E(){if(1===arguments.length){const e=arguments;return function(n){return _(n,e[0])}}return _(arguments[0],arguments[1])}},3327:(e,n)=>{var t=Object.defineProperty;function r(e,n){if(0===n)throw{RE_EXN_ID:"Division_by_zero",Error:new Error};return e%n}function o(e){}function i(e){return e-1|0}function u(e){return e+1|0}function c(e,n){return e+n}function s(){if(1===arguments.length){const e=arguments;return function(n){return c(n,e[0])}}return c(arguments[0],arguments[1])}function a(e,n){return e-n}function l(){if(1===arguments.length){const e=arguments;return function(n){return a(n,e[0])}}return a(arguments[0],arguments[1])}function f(e,n){return e*n}function d(){if(1===arguments.length){const e=arguments;return function(n){return f(n,e[0])}}return f(arguments[0],arguments[1])}function g(e,n){return e/n}function v(){if(1===arguments.length){const e=arguments;return function(n){return g(n,e[0])}}return g(arguments[0],arguments[1])}((e,n)=>{for(var r in(e=>{t(e,"__esModule",{value:!0})})(e),n)t(e,r,{get:n[r],enumerable:!0})})(n,{add:()=>s,clamp:()=>P,divide:()=>v,divideWithModulo:()=>_,gt:()=>y,gte:()=>b,lt:()=>O,lte:()=>F,modulo:()=>p,multiply:()=>d,placeholder:()=>o,pred:()=>i,subtract:()=>l,succ:()=>u});var h=r;function p(){if(1===arguments.length){const e=arguments;return function(n){return h(n,e[0])}}return h(arguments[0],arguments[1])}function m(e,n){return[e/n,r(0|e,0|n)]}function _(){if(1===arguments.length){const e=arguments;return function(n){return m(n,e[0])}}return m(arguments[0],arguments[1])}function E(e,n){return e>n}function y(){if(1===arguments.length){const e=arguments;return function(n){return E(n,e[0])}}return E(arguments[0],arguments[1])}function C(e,n){return e>=n}function b(){if(1===arguments.length){const e=arguments;return function(n){return C(n,e[0])}}return C(arguments[0],arguments[1])}function S(e,n){return e<n}function O(){if(1===arguments.length){const e=arguments;return function(n){return S(n,e[0])}}return S(arguments[0],arguments[1])}function w(e,n){return e<=n}function F(){if(1===arguments.length){const e=arguments;return function(n){return w(n,e[0])}}return w(arguments[0],arguments[1])}function k(e,n,t){return Math.min(Math.max(e,n),t)}function P(){if(2===arguments.length){const e=arguments;return function(n){return k(n,e[0],e[1])}}return k(arguments[0],arguments[1],arguments[2])}},5287:(e,n)=>{var t=Object.defineProperty;function r(e,n,t){for(var r=new Array(t),o=0,i=n;o<t;)r[o]=e[i],o=o+1|0,i=i+1|0;return r}function o(e,n){for(;;){var t=n,i=e,u=i.length,c=0===u?1:u,s=c-t.length|0;if(0===s)return i.apply(null,t);if(s>=0)return function(e,n){return function(t){return o(e,n.concat([t]))}}(i,t);n=r(t,c,0|-s),e=i.apply(null,r(t,0,c))}}function i(e,n){var t=e.length;if(1===t)return e(n);switch(t){case 1:return e(n);case 2:return function(t){return e(n,t)};case 3:return function(t,r){return e(n,t,r)};case 4:return function(t,r,o){return e(n,t,r,o)};case 5:return function(t,r,o,i){return e(n,t,r,o,i)};case 6:return function(t,r,o,i,u){return e(n,t,r,o,i,u)};case 7:return function(t,r,o,i,u,c){return e(n,t,r,o,i,u,c)};default:return o(e,[n])}}((e,n)=>{for(var r in(e=>{t(e,"__esModule",{value:!0})})(e),n)t(e,r,{get:n[r],enumerable:!0})})(n,{None:()=>J,Some:()=>K,contains:()=>H,filter:()=>A,flatMap:()=>S,fromExecution:()=>_,fromFalsy:()=>h,fromNullable:()=>v,fromPredicate:()=>m,fromPromise:()=>E,getExn:()=>M,getWithDefault:()=>B,isNone:()=>I,isSome:()=>G,map:()=>C,mapNullable:()=>k,mapWithDefault:()=>w,match:()=>D,placeholder:()=>g,tap:()=>V,toNullable:()=>R,toResult:()=>j,toUndefined:()=>T,zip:()=>U,zipWith:()=>z});var u=function(e,n){for(var t in e)n(t)};function c(e,n){if(e===n)return!0;var t=typeof e;if("string"===t||"number"===t||"boolean"===t||"undefined"===t||null===e)return!1;var r=typeof n;if("function"===t||"function"===r)throw{RE_EXN_ID:"Invalid_argument",_1:"equal: functional value",Error:new Error};if("number"===r||"undefined"===r||null===n)return!1;var o=0|e.TAG,i=0|n.TAG;if(248===o)return e[1]===n[1];if(251===o)throw{RE_EXN_ID:"Invalid_argument",_1:"equal: abstract value",Error:new Error};if(o!==i)return!1;var s=0|e.length;if(s!==(0|n.length))return!1;if(!Array.isArray(e)){if(e instanceof Date&&n instanceof Date)return!(e>n||e<n);var a={contents:!0};return u(e,(function(e){Object.prototype.hasOwnProperty.call(n,e)||(a.contents=!1)})),a.contents&&u(n,(function(t){Object.prototype.hasOwnProperty.call(e,t)&&c(n[t],e[t])||(a.contents=!1)})),a.contents}for(var l=0;;){var f=l;if(f===s)return!0;if(!c(e[f],n[f]))return!1;l=f+1|0}}function s(e){return void 0===e?{BS_PRIVATE_NESTED_SOME_NONE:0}:null!==e&&void 0!==e.BS_PRIVATE_NESTED_SOME_NONE?{BS_PRIVATE_NESTED_SOME_NONE:e.BS_PRIVATE_NESTED_SOME_NONE+1|0}:e}function a(e){if(null===e||void 0===e.BS_PRIVATE_NESTED_SOME_NONE)return e;var n=e.BS_PRIVATE_NESTED_SOME_NONE;return 0===n?void 0:{BS_PRIVATE_NESTED_SOME_NONE:n-1|0}}function l(e,n,t){return void 0!==e?t(a(e)):n}function f(e,n){if(void 0!==e)return n(a(e))}function d(e,n){return void 0!==e?a(e):n}function g(e){}function v(e){return null==e?void 0:s(e)}function h(e){if(e)return e}function p(e,n){return f(null==e?void 0:s(e),1===(t=function(e){if(n(e))return s(e)}).length?t:function(e){return i(t,e)});var t}function m(){if(1===arguments.length){const e=arguments;return function(n){return p(n,e[0])}}return p(arguments[0],arguments[1])}function _(e){try{return s(e(void 0))}catch(e){return}}function E(e){return e.then((function(e){return Promise.resolve(s(e))})).catch((function(e){return Promise.resolve(void 0)}))}var y=function(e,n){if(void 0!==e)return s(n(a(e)))};function C(){if(1===arguments.length){const e=arguments;return function(n){return y(n,e[0])}}return y(arguments[0],arguments[1])}var b=f;function S(){if(1===arguments.length){const e=arguments;return function(n){return b(n,e[0])}}return b(arguments[0],arguments[1])}var O=l;function w(){if(2===arguments.length){const e=arguments;return function(n){return O(n,e[0],e[1])}}return O(arguments[0],arguments[1],arguments[2])}function F(e,n){if(void 0!==e)return null==(t=n(a(e)))?void 0:s(t);var t}function k(){if(1===arguments.length){const e=arguments;return function(n){return F(n,e[0])}}return F(arguments[0],arguments[1])}function P(e,n){return f(e,(function(e){if(n(e))return s(e)}))}function A(){if(1===arguments.length){const e=arguments;return function(n){return P(n,e[0])}}return P(arguments[0],arguments[1])}var N=d;function B(){if(1===arguments.length){const e=arguments;return function(n){return N(n,e[0])}}return N(arguments[0],arguments[1])}var M=function(e){if(void 0!==e)return a(e);throw{RE_EXN_ID:"Not_found",Error:new Error}};function R(e){return d(e,null)}function T(e){return d(e,void 0)}function x(e,n){return void 0!==e?{TAG:0,_0:a(e)}:{TAG:1,_0:n}}function j(){if(1===arguments.length){const e=arguments;return function(n){return x(n,e[0])}}return x(arguments[0],arguments[1])}function L(e,n,t){return void 0!==e?n(a(e)):t(void 0)}function D(){if(2===arguments.length){const e=arguments;return function(n){return L(n,e[0],e[1])}}return L(arguments[0],arguments[1],arguments[2])}var I=function(e){return void 0===e},G=function(e){return void 0!==e};function q(e,n){return void 0!==e?(n(a(e)),e):e}function V(){if(1===arguments.length){const e=arguments;return function(n){return q(n,e[0])}}return q(arguments[0],arguments[1])}function W(e,n){return l(e,!1,(function(e){return c(e,n)}))}function H(){if(1===arguments.length){const e=arguments;return function(n){return W(n,e[0])}}return W(arguments[0],arguments[1])}function $(e,n){if(void 0!==e&&void 0!==n)return[a(e),a(n)]}function U(){if(1===arguments.length){const e=arguments;return function(n){return $(n,e[0])}}return $(arguments[0],arguments[1])}function X(e,n,t){if(void 0!==e&&void 0!==n)return s(t(a(e),a(n)))}function z(){if(2===arguments.length){const e=arguments;return function(n){return X(n,e[0],e[1])}}return X(arguments[0],arguments[1],arguments[2])}var K=e=>e,J=void 0},1619:(e,n)=>{var t=Object.defineProperty;function r(e,n,t){for(var r=new Array(t),o=0,i=n;o<t;)r[o]=e[i],o=o+1|0,i=i+1|0;return r}function o(e,n){for(;;){var t=n,i=e,u=i.length,c=0===u?1:u,s=c-t.length|0;if(0===s)return i.apply(null,t);if(s>=0)return function(e,n){return function(t){return o(e,n.concat([t]))}}(i,t);n=r(t,c,0|-s),e=i.apply(null,r(t,0,c))}}function i(e,n){var t=e.length;if(1===t)return e(n);switch(t){case 1:return e(n);case 2:return function(t){return e(n,t)};case 3:return function(t,r){return e(n,t,r)};case 4:return function(t,r,o){return e(n,t,r,o)};case 5:return function(t,r,o,i){return e(n,t,r,o,i)};case 6:return function(t,r,o,i,u){return e(n,t,r,o,i,u)};case 7:return function(t,r,o,i,u,c){return e(n,t,r,o,i,u,c)};default:return o(e,[n])}}((e,n)=>{for(var r in(e=>{t(e,"__esModule",{value:!0})})(e),n)t(e,r,{get:n[r],enumerable:!0})})(n,{Error:()=>Q,Ok:()=>J,catchError:()=>U,flatMap:()=>F,flip:()=>K,fromExecution:()=>E,fromFalsy:()=>p,fromNullable:()=>v,fromPredicate:()=>_,fromPromise:()=>y,getExn:()=>k,getWithDefault:()=>A,handleError:()=>V,isError:()=>x,isOk:()=>j,map:()=>b,mapError:()=>H,mapWithDefault:()=>O,match:()=>T,placeholder:()=>d,recover:()=>z,tap:()=>D,tapError:()=>G,toNullable:()=>B,toOption:()=>M,toUndefined:()=>N});var u={contents:0};function c(e){return u.contents=u.contents+1|0,e+"/"+u.contents}var s=c("Caml_js_exceptions.Error"),a=s;function l(e,n){return 0===e.TAG?n(e._0):{TAG:1,_0:e._0}}function f(e,n){return 0===e.TAG?e._0:n}function d(e){}function g(e,n){return null==e?{TAG:1,_0:n}:{TAG:0,_0:e}}function v(){if(1===arguments.length){const e=arguments;return function(n){return g(n,e[0])}}return g(arguments[0],arguments[1])}function h(e,n){return e?{TAG:0,_0:e}:{TAG:1,_0:n}}function p(){if(1===arguments.length){const e=arguments;return function(n){return h(n,e[0])}}return h(arguments[0],arguments[1])}function m(e,n,t){return l(v(e,t),1===(r=function(e){return n(e)?{TAG:0,_0:e}:{TAG:1,_0:t}}).length?r:function(e){return i(r,e)});var r}function _(){if(2===arguments.length){const e=arguments;return function(n){return m(n,e[0],e[1])}}return m(arguments[0],arguments[1],arguments[2])}function E(e){try{return{TAG:0,_0:e(void 0)}}catch(e){var n=function(e){return null!=e&&"string"==typeof e.RE_EXN_ID}(t=e)?t:{RE_EXN_ID:s,_1:t};if(n.RE_EXN_ID===a)return{TAG:1,_0:n._1};throw n}var t}function y(e){return e.then((function(e){return Promise.resolve({TAG:0,_0:e})})).catch((function(e){return Promise.resolve({TAG:1,_0:e})}))}var C=function(e,n){return 0===e.TAG?{TAG:0,_0:n(e._0)}:{TAG:1,_0:e._0}};function b(){if(1===arguments.length){const e=arguments;return function(n){return C(n,e[0])}}return C(arguments[0],arguments[1])}var S=function(e,n,t){return 0===e.TAG?t(e._0):n};function O(){if(2===arguments.length){const e=arguments;return function(n){return S(n,e[0],e[1])}}return S(arguments[0],arguments[1],arguments[2])}var w=l;function F(){if(1===arguments.length){const e=arguments;return function(n){return w(n,e[0])}}return w(arguments[0],arguments[1])}var k=function(e){if(0===e.TAG)return e._0;throw{RE_EXN_ID:"Not_found",Error:new Error}},P=f;function A(){if(1===arguments.length){const e=arguments;return function(n){return P(n,e[0])}}return P(arguments[0],arguments[1])}function N(e){return f(e,void 0)}function B(e){return f(e,null)}function M(e){if(0===e.TAG)return void 0===(n=e._0)?{BS_PRIVATE_NESTED_SOME_NONE:0}:null!==n&&void 0!==n.BS_PRIVATE_NESTED_SOME_NONE?{BS_PRIVATE_NESTED_SOME_NONE:n.BS_PRIVATE_NESTED_SOME_NONE+1|0}:n;var n}function R(e,n,t){return 0===e.TAG?n(e._0):t(e._0)}function T(){if(2===arguments.length){const e=arguments;return function(n){return R(n,e[0],e[1])}}return R(arguments[0],arguments[1],arguments[2])}var x=function(e){return 0!==e.TAG},j=function(e){return 0===e.TAG};function L(e,n){return 0!==e.TAG||n(e._0),e}function D(){if(1===arguments.length){const e=arguments;return function(n){return L(n,e[0])}}return L(arguments[0],arguments[1])}function I(e,n){return 0===e.TAG||n(e._0),e}function G(){if(1===arguments.length){const e=arguments;return function(n){return I(n,e[0])}}return I(arguments[0],arguments[1])}function q(e,n){return 0===e.TAG?e:{TAG:0,_0:n(e._0)}}function V(){if(1===arguments.length){const e=arguments;return function(n){return q(n,e[0])}}return q(arguments[0],arguments[1])}function W(e,n){return 0===e.TAG?e:{TAG:1,_0:n(e._0)}}function H(){if(1===arguments.length){const e=arguments;return function(n){return W(n,e[0])}}return W(arguments[0],arguments[1])}function $(e,n){return 0===e.TAG?e:n(e._0)}function U(){if(1===arguments.length){const e=arguments;return function(n){return $(n,e[0])}}return $(arguments[0],arguments[1])}function X(e,n){return U(e,(function(e){return{TAG:0,_0:n}}))}function z(){if(1===arguments.length){const e=arguments;return function(n){return X(n,e[0])}}return X(arguments[0],arguments[1])}function K(e){return 0===e.TAG?{TAG:1,_0:e._0}:{TAG:0,_0:e._0}}var J=e=>({TAG:0,_0:e}),Q=e=>({TAG:1,_0:e})},3059:(e,n)=>{var t=Object.defineProperty;function r(e){return void 0===e?{BS_PRIVATE_NESTED_SOME_NONE:0}:null!==e&&void 0!==e.BS_PRIVATE_NESTED_SOME_NONE?{BS_PRIVATE_NESTED_SOME_NONE:e.BS_PRIVATE_NESTED_SOME_NONE+1|0}:e}function o(e){return void 0===e?void 0:r(e)}function i(e){}function u(e){return String(e)}function c(e){return e.length}function s(e,n){return e.concat(n)}function a(){if(1===arguments.length){const e=arguments;return function(n){return s(n,e[0])}}return s(arguments[0],arguments[1])}function l(e,n){return e.concat(n)}function f(){if(1===arguments.length){const e=arguments;return function(n){return l(n,e[0])}}return l(arguments[0],arguments[1])}function d(e,n){return n.concat(e)}function g(){if(1===arguments.length){const e=arguments;return function(n){return d(n,e[0])}}return d(arguments[0],arguments[1])}function v(e,n,t){return e.slice(n,t)}function h(){if(2===arguments.length){const e=arguments;return function(n){return v(n,e[0],e[1])}}return v(arguments[0],arguments[1],arguments[2])}function p(e,n){return e.slice(n)}function m(){if(1===arguments.length){const e=arguments;return function(n){return p(n,e[0])}}return p(arguments[0],arguments[1])}function _(e){return e.toLowerCase()}function E(e){return e.toUpperCase()}function y(e){return e.trim()}function C(e){return e.trimStart()}function b(e){return e.trimEnd()}function S(e){return 0===e.length}function O(e){return e.length>0}function w(e,n){return e.split(n)}function F(){if(1===arguments.length){const e=arguments;return function(n){return w(n,e[0])}}return w(arguments[0],arguments[1])}function k(e,n){return e.split(n)}function P(){if(1===arguments.length){const e=arguments;return function(n){return k(n,e[0])}}return k(arguments[0],arguments[1])}function A(e,n){return[e.slice(0,n),e.slice(n)]}function N(){if(1===arguments.length){const e=arguments;return function(n){return A(n,e[0])}}return A(arguments[0],arguments[1])}function B(e,n){return e.includes(n)}function M(){if(1===arguments.length){const e=arguments;return function(n){return B(n,e[0])}}return B(arguments[0],arguments[1])}function R(e,n,t){return e.replace(n,t)}function T(){if(2===arguments.length){const e=arguments;return function(n){return R(n,e[0],e[1])}}return R(arguments[0],arguments[1],arguments[2])}function x(e,n,t){var r=e.split(n);return function(e){for(var n="",o=0,i=e.length;o<i;++o)u=n,c=e[o],s=void 0,s=o<(r.length-1|0)?c.concat(t):c,n=u.concat(s);var u,c,s;return n}(r)}function j(){if(2===arguments.length){const e=arguments;return function(n){return x(n,e[0],e[1])}}return x(arguments[0],arguments[1],arguments[2])}function L(e,n,t){return e.replace(n,t)}function D(){if(2===arguments.length){const e=arguments;return function(n){return L(n,e[0],e[1])}}return L(arguments[0],arguments[1],arguments[2])}function I(e,n){return e.replace(n,"")}function G(){if(1===arguments.length){const e=arguments;return function(n){return I(n,e[0])}}return I(arguments[0],arguments[1])}function q(e,n){return j(e,n,"")}function V(){if(1===arguments.length){const e=arguments;return function(n){return q(n,e[0])}}return q(arguments[0],arguments[1])}function W(e,n){var t=e.search(n);return t<0?void 0:t}function H(){if(1===arguments.length){const e=arguments;return function(n){return W(n,e[0])}}return W(arguments[0],arguments[1])}function $(e,n){return null===(t=e.match(n))?void 0:r(t);var t}function U(){if(1===arguments.length){const e=arguments;return function(n){return $(n,e[0])}}return $(arguments[0],arguments[1])}function X(e,n){return e.repeat(n)}function z(){if(1===arguments.length){const e=arguments;return function(n){return X(n,e[0])}}return X(arguments[0],arguments[1])}function K(e,n){var t=e.indexOf(n);return t<0?void 0:t}function J(){if(1===arguments.length){const e=arguments;return function(n){return K(n,e[0])}}return K(arguments[0],arguments[1])}function Q(e,n){var t=e.lastIndexOf(n);return t<0?void 0:t}function Y(){if(1===arguments.length){const e=arguments;return function(n){return Q(n,e[0])}}return Q(arguments[0],arguments[1])}function Z(e,n){return e.endsWith(n)}function ee(){if(1===arguments.length){const e=arguments;return function(n){return Z(n,e[0])}}return Z(arguments[0],arguments[1])}function ne(e,n){return e.startsWith(n)}function te(){if(1===arguments.length){const e=arguments;return function(n){return ne(n,e[0])}}return ne(arguments[0],arguments[1])}function re(e,n){return e[n]}function oe(){if(1===arguments.length){const e=arguments;return function(n){return re(n,e[0])}}return re(arguments[0],arguments[1])}function ie(e,n){return o(e[n])}function ue(){if(1===arguments.length){const e=arguments;return function(n){return ie(n,e[0])}}return ie(arguments[0],arguments[1])}function ce(e){return function(n){if(n<=0)return[];for(var t=new Array(n),r=0;r<n;++r)t[r]=e[r];return t}(e.length)}function se(e){return o(e[0])}function ae(e){return o(e[e.length-1|0])}((e,n)=>{for(var r in(e=>{t(e,"__esModule",{value:!0})})(e),n)t(e,r,{get:n[r],enumerable:!0})})(n,{append:()=>f,concat:()=>a,endsWith:()=>ee,get:()=>ue,getUnsafe:()=>oe,head:()=>se,includes:()=>M,indexOf:()=>J,isEmpty:()=>S,isNotEmpty:()=>O,last:()=>ae,lastIndexOf:()=>Y,length:()=>c,make:()=>u,match:()=>U,placeholder:()=>i,prepend:()=>g,remove:()=>G,removeAll:()=>V,repeat:()=>z,replace:()=>T,replaceAll:()=>j,replaceByRe:()=>D,search:()=>H,slice:()=>h,sliceToEnd:()=>m,split:()=>F,splitAt:()=>N,splitByRe:()=>P,startsWith:()=>te,toArray:()=>ce,toLowerCase:()=>_,toUpperCase:()=>E,trim:()=>y,trimEnd:()=>b,trimStart:()=>C})},2661:(e,n)=>{var t=Object.defineProperty;function r(){let e=arguments;return function(){let n=e[0].apply(null,arguments);for(let t=1,r=e.length;t<r;t++)n=e[t](n);return n}}((e,n)=>{for(var r in(e=>{t(e,"__esModule",{value:!0})})(e),n)t(e,r,{get:n[r],enumerable:!0})})(n,{flow:()=>r})},93:(e,n,t)=>{var r=Object.create,o=Object.defineProperty,i=Object.getOwnPropertyDescriptor,u=Object.getOwnPropertyNames,c=Object.getPrototypeOf,s=Object.prototype.hasOwnProperty,a=e=>o(e,"__esModule",{value:!0}),l=e=>((e,n,t)=>{if(n&&"object"==typeof n||"function"==typeof n)for(let r of u(n))s.call(e,r)||"default"===r||o(e,r,{get:()=>n[r],enumerable:!(t=i(n,r))||t.enumerable});return e})(a(o(null!=e?r(c(e)):{},"default",e&&e.__esModule&&"default"in e?{get:()=>e.default,enumerable:!0}:{value:e,enumerable:!0})),e);((e,n)=>{for(var t in a(e),n)o(e,t,{get:n[t],enumerable:!0})})(n,{A:()=>v,B:()=>y,D:()=>E,F:()=>g,G:()=>p,N:()=>C,O:()=>m,R:()=>h,S:()=>_,flow:()=>d.flow,pipe:()=>f.pipe});var f=l(t(3789)),d=l(t(2661)),g=l(t(1654)),v=l(t(4523)),h=l(t(1619)),p=l(t(1564)),m=l(t(5287)),_=l(t(3059)),E=l(t(6198)),y=l(t(1982)),C=l(t(3327))},3789:(e,n)=>{var t=Object.defineProperty;function r(){let e=arguments[0];for(let n=1,t=arguments.length;n<t;n++)e=arguments[n](e);return e}((e,n)=>{for(var r in(e=>{t(e,"__esModule",{value:!0})})(e),n)t(e,r,{get:n[r],enumerable:!0})})(n,{pipe:()=>r})},2334:(e,n)=>{"use strict";var t,r;Object.defineProperty(n,"__esModule",{value:!0}),n.PingResponse=n.PingRequest=void 0,function(e){e.of=function(){return{kind:"ping"}}}(t||(n.PingRequest=t={})),function(e){e.of=function(){return{kind:"ping"}}}(r||(n.PingResponse=r={}))},8672:(e,n)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.CmExtension=void 0;class t{static create(e){return new t(e)}constructor(e){!function({config:e,editor:n}){const t=n.getWrapperElement();t.dataset.cbCornerStyle=e.cornerStyle,t.dataset.cbRendering=e.rendering,t.dataset.cbRenderLayout=e.renderLayout}(e),function({completeHandler:e,config:n,editor:t,renderHandler:r,selectHandler:o}){"enabled"===n.rendering&&(r.renderNow(t),t.on("change",((e,n)=>r.renderOnChange(e,n)))),"enabled"===n.completion&&t.on("keydown",((n,t)=>e.completeOnEnter(n,t))),"enabled"===n.selectAllCapturing&&t.on("beforeSelectionChange",((e,n)=>o.selectOnSelectAll(e.getDoc(),n)))}(e),function({codeMirror:e,requestHandler:n}){e.defineExtension(t.extensionName,(e=>n.handle(e)))}(e)}}n.CmExtension=t,t.extensionName="BetterCodeBlocks"},1095:(e,n,t)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.CmExtensions=void 0;const r=t(8672),o=t(9230),i=t(9746),u=t(6644),c=t(4215),s=t(5174),a=t(3701),l=t(6871),f=t(886),d=t(8815),g=t(6342),v=t(687),h=t(752),p=t(6044),m=t(2328),_=t(7163),E=t(8263),y=t(4086),C=t(7196),b=t(6254),S=t(4014),O=t(4327),{clipboard:w}=navigator;var F;!function(e){e.createDefault=function({codeMirror:e,editor:n,config:t}){const F=y.Parser.create();return r.CmExtension.create({codeMirror:e,editor:n,completeHandler:l.CompleteHandler.create({completer:o.Completer.create({completionGenerator:i.CompletionGenerator.create({parser:F})})}),config:t,renderHandler:f.RenderHandler.create({renderer:b.Renderer.create({renderParser:S.RenderParser.create({config:t,parser:F}),renderPerformer:O.RenderPerformer.create({formatter:s.Formatter.combine(u.BetweenSpacer.create(),c.EdgeSpacer.create(),a.Opener.create()),marker:p.Marker.combine(_.Widgeter.create({widgetGenerator:E.WidgetGenerator.create({copyButtonGenerator:m.CopyButtonGenerator.create({clipboard:w,config:t,window})})}),h.LineStyler.create({lineStyleGenerator:v.LineStyleGenerator.create()}))})})}),requestHandler:d.RequestHandler.create(),selectHandler:g.SelectHandler.create({rangeFinder:C.RangeFinder.create({parser:F})})})}}(F||(n.CmExtensions=F={}))},9211:(e,n,t)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.Matcher=void 0;const r=t(7205);var o;!function(e){e.matchStart=function(e){const n=/^(?<indent> {0,3})(?<tag>~~~+|```+)[ \t]*(?<lang>[\w/+#-]*)[^\n`]*$/.exec(e);if(!(0,r.nil)(n))return{indent:n.groups.indent,tag:n.groups.tag,lang:n.groups.lang,sequence:n[0]}},e.matchesEnd=function({tag:e,text:n}){return function(e){return new RegExp(`^ {0,3}${e}+ *$`)}(e).test(n)}}(o||(n.Matcher=o={}))},4086:(e,n,t)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.Parser=void 0;const r=t(3106),o=t(1016),i=t(3964),u=t(7205),c=t(9211),s=t(9933);class a{static create(){return new a}constructor(){}parse(e){return this.parseInternal(e,o.Docs.eachLine)}parseBy(e,n){return this.parseInternal(e,n)}parseInternal(e,n){const t=[];let r;return n(e,(({line:e,text:n,endIteration:o})=>{if((0,u.nil)(r))return void(r=this.parseStartSequence(n,e));const c=this.parseEndSequence(n,e,r);(0,u.nil)(c)?(0,u.def)(this.parseStartSequence(n,e))&&o():(t.push(s.CodeBlock.of({lang:r.lang,start:i.LineSegment.of({line:r.line,from:0,to:r.length}),end:i.LineSegment.of({line:c.line,from:0,to:c.length}),openingFence:r.sequence,closingFence:n})),r=void 0)})),(0,u.def)(r)?[]:t}parseStartSequence(e,n){const t=c.Matcher.matchStart(e);if((0,u.nil)(t))return;const{tag:o,sequence:i}=t,s=r.Strings.isEmpty(t.lang)?void 0:t.lang;return{line:n,length:e.length,tag:o,lang:s,sequence:i}}parseEndSequence(e,n,{tag:t}){if(c.Matcher.matchesEnd({tag:t,text:e}))return{line:n,length:e.length}}}n.Parser=a},7196:(e,n,t)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.RangeFinder=void 0;const r=t(7205),o=t(6989);class i{static create(e){return new i(e)}constructor(e){this.parser=e.parser}findActiveCodeRange(e){const n=this.parser.parse(e),t=o.CodeDocs.getActiveCodeBlock(e,n);if(!(0,r.nil)(t))return o.CodeDocs.getCodeRange(e,t)}}n.RangeFinder=i},9230:(e,n,t)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.Completer=void 0;const r=t(7205);class o{static create(e){return new o(e)}constructor(e){this.completionGenerator=e.completionGenerator}complete(e,n){const t=this.completionGenerator.generate(e);return!(0,r.nil)(t)&&(this.performCompletion(e,t,n),!0)}performCompletion(e,n,t){const r=e.getCursor(),o=e.lineSeparator();e.replaceRange(`${o}${o}${n}`,r,void 0,t),e.setCursor(r.line+1,void 0,{origin:t})}}n.Completer=o},9746:(e,n,t)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.CompletionGenerator=void 0;const r=t(3106),o=t(1016),i=t(7205),u=t(9211);class c{static create(e){return new c(e)}constructor(e){this.parser=e.parser}generate(e){const n=e.getCursor(),t=e.getLine(n.line);if(n.ch<t.length)return;const r=u.Matcher.matchStart(t);if((0,i.nil)(r)||!this.isCompletionPossible(e))return;const o=`${r.indent}${r.tag}`;return this.isCompletionValid(e,o,n)?o:void 0}isCompletionPossible(e){return r.Arrays.isEmpty(this.parser.parse(e))}isCompletionValid(e,n,t){return this.parseCodeBlocksWithCompletion(e,n,t).some((e=>e.startsAt(t.line)&&e.endsAt(t.line+1)))}parseCodeBlocksWithCompletion(e,n,t){return this.parser.parseBy(e,this.eachLineWithCompletion(n,t))}eachLineWithCompletion(e,n){return(t,r)=>{o.Docs.eachLineInserting(t,{line:n.line+1,text:e},r)}}}n.CompletionGenerator=c},6644:(e,n,t)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.BetweenSpacer=void 0;const r=t(1016),o=t(7205);class i{static create(){return new i}constructor(){}format(e,n,t){return this.space(e,n,t)}space(e,n,t){let i,u=0;return n.map((n=>((0,o.def)(i)&&n.startsAt(i+1)&&(r.Docs.insertNewLine(e,{line:i+1+u,ch:0},t),u++),i=n.endLine,n.shiftDownBy(u))))}}n.BetweenSpacer=i},4215:(e,n,t)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.EdgeSpacer=void 0;const r=t(3106),o=t(1016),i=t(7205);class u{static create(){return new u}constructor(){}format(e,n,t){return this.space(e,n,t)}space(e,n,t){const r=this.spaceStart(e,n,t);return this.spaceEnd(e,r,t)}spaceStart(e,n,t){const u=r.Arrays.head(n);if((0,i.nil)(u))return n;const c=e.firstLine();return u.startsAt(c)?(o.Docs.insertNewLine(e,{line:0,ch:0},t),n.map((e=>e.shiftDownBy(1)))):n}spaceEnd(e,n,t){const u=r.Arrays.last(n);if((0,i.nil)(u))return n;const c=e.lastLine();if(!u.endsAt(c))return n;const s=e.getCursor();return o.Docs.insertNewLine(e,{line:c+1,ch:0},t),e.setCursor(s),n}}n.EdgeSpacer=u},5174:(e,n)=>{"use strict";var t;Object.defineProperty(n,"__esModule",{value:!0}),n.Formatter=void 0,function(e){e.combine=function(...e){return{format(n,t,r){let o=t;return e.forEach((e=>{o=e.format(n,o,r)})),o}}}}(t||(n.Formatter=t={}))},3701:(e,n,t)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.Opener=void 0;const r=t(1016),o=t(9933);class i{static create(){return new i}constructor(){}format(e,n,t){return this.open(e,n,t)}open(e,n,t){let i=0;return n.map((n=>n.isEmpty()?(r.Docs.insertNewLine(e,{line:n.endLine+i,ch:0},t),i++,o.CodeBlock.of({start:n.start.plusLines(i-1),end:n.end.plusLines(i),lang:n.lang,openingFence:n.openingFence,closingFence:n.closingFence})):n.shiftDownBy(i)))}}n.Opener=i},6871:(e,n,t)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.CompleteHandler=void 0;const r=t(1849);class o{static create(e){return new o(e)}constructor(e){this.completer=e.completer}completeOnEnter(e,n){"Enter"===n.code&&e.operation((()=>this.completer.complete(e.getDoc(),r.Origin.CompleteHandler)))&&n.preventDefault()}}n.CompleteHandler=o},886:(e,n,t)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.RenderHandler=void 0;const r=t(1849);class o{static create(e){return new o(e)}constructor(e){this.renderer=e.renderer}renderOnChange(e,{origin:n}){n!==r.Origin.RenderHandler&&this.renderInternal(e)}renderNow(e){this.renderInternal(e)}renderInternal(e){e.operation((()=>this.renderer.render(e.getDoc(),r.Origin.RenderHandler)))}}n.RenderHandler=o},8815:(e,n,t)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.RequestHandler=void 0;const r=t(2334);class o{static create(){return new o}constructor(){this.handle=e=>this[e.kind](e)}async ping(e){return Promise.resolve(r.PingResponse.of())}}n.RequestHandler=o},6342:(e,n,t)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.SelectHandler=void 0;const r=t(1016),o=t(5978),i=t(7205);class u{static create(e){return new u(e)}constructor(e){this.rangeFinder=e.rangeFinder}selectOnSelectAll(e,n){if(!o.Events.isSelectAll(e,n))return;const t=this.rangeFinder.findActiveCodeRange(e);(0,i.nil)(t)||r.Docs.isSelected(e,t)||n.update([{anchor:t.from,head:t.to}])}}n.SelectHandler=u},6044:(e,n)=>{"use strict";var t;Object.defineProperty(n,"__esModule",{value:!0}),n.Marker=void 0,function(e){e.combine=function(...e){return{mark(n,t){e.forEach((e=>e.mark(n,t)))}}}}(t||(n.Marker=t={}))},687:(e,n,t)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.LineStyleGenerator=void 0;const r=t(6215),o=t(6674),i=t(6589);class u{static create(){return new u}constructor(){}generate(e){return e.flatMap((e=>[{line:e.startLine,background:[i.CodeBlockClass.startBackground],text:[i.CodeBlockClass.startText],wrap:[i.CodeBlockClass.startLine]},...this.generateStyledCodeLines(e),{line:e.endLine,background:[i.CodeBlockClass.endBackground],text:[i.CodeBlockClass.endText],wrap:[i.CodeBlockClass.endLine]}]))}generateStyledCodeLines(e){return o.Repeat.times(e.size,(n=>{const t=e.startLine+1+n,r=0===n,o=n===e.size-1;return this.generateStyledCodeLine({line:t,isFirstLine:r,isLastLine:o})}))}generateStyledCodeLine({line:e,isFirstLine:n,isLastLine:t}){const o=n?i.CodeBlockClass.first:void 0,u=t?i.CodeBlockClass.last:void 0;return{line:e,background:r.Arrays.compact([i.CodeBlockClass.codeBackground,o,u]),text:r.Arrays.compact([i.CodeBlockClass.codeText,o,u]),wrap:r.Arrays.compact([i.CodeBlockClass.codeLine,o,u])}}}n.LineStyleGenerator=u},752:(e,n,t)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.LineStyler=void 0;const r=t(1016);class o{static create(e){return new o(e)}constructor(e){this.lineStyles=[],this.lineStyleGenerator=e.lineStyleGenerator}mark(e,n){this.updateLineStyles(e,n)}updateLineStyles(e,n){const t=this.lineStyleGenerator.generate(n);this.lineStyles.forEach((e=>e.remove())),this.lineStyles=t.map((n=>{const t=r.Docs.addLineClasses(e,n.line,n);return{...n,remove:()=>r.Docs.removeLineClasses(e,t,n)}}))}}n.LineStyler=o},2328:(e,n,t)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.CopyButtonGenerator=void 0;const r=t(9234),o=t(6989),i=t(6589),u=t(7335),{button:c,i:s}=r.Html;class a{static create(e){return new a(e)}constructor(e){this.clipboard=e.clipboard,this.config=e.config,this.window=e.window}generate(e,n){const t=s({className:`${u.FontAwesomeClass.solid} ${u.FontAwesomeClass.clipboard}`});return c({children:[t],className:i.CodeBlockClass.copyBtn,onClick:this.generateCopyButtonOnClick(e,n,t),title:"Copy Code"})}generateCopyButtonOnClick(e,n,t){const r="fencedCode"===this.config.copyFormat,i=u=>{const c=u.currentTarget,s=o.CodeDocs.getCode(e,n,{includeFences:r});this.handleCopyButtonClicked({code:s,copyButton:c,icon:t,onClick:i})};return i}async handleCopyButtonClicked({code:e,copyButton:n,icon:t,onClick:o}){r.Html.removeOnClick(n,o),await this.clipboard.writeText(e),r.Html.addClass(n,i.CodeBlockClass.copied),r.Html.swapClass(t,{from:u.FontAwesomeClass.clipboard,to:u.FontAwesomeClass.clipboardCheck}),this.window.setTimeout((()=>{r.Html.addOnClick(n,o),r.Html.removeClass(n,i.CodeBlockClass.copied),r.Html.swapClass(t,{from:u.FontAwesomeClass.clipboardCheck,to:u.FontAwesomeClass.clipboard})}),3e3)}}n.CopyButtonGenerator=a},8263:(e,n,t)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.WidgetGenerator=void 0;const r=t(9234),o=t(4293),i=t(6589),{span:u}=r.Html,{monospace:c}=o.BuiltInClass;class s{static create(e){return new s(e)}constructor(e){this.copyButtonGenerator=e.copyButtonGenerator}generate(e,n){return n.flatMap((n=>{const{start:t,end:r,lang:o,openingFence:s,closingFence:a}=n;return[{element:u({className:i.CodeBlockClass.startWidget,children:[u({className:`${i.CodeBlockClass.openingFence} ${c}`,textContent:s}),this.copyButtonGenerator.generate(e,n)]}),range:t},{element:u({className:i.CodeBlockClass.endWidget,children:[u({className:`${i.CodeBlockClass.closingFence} ${c}`,textContent:a}),u({className:`${i.CodeBlockClass.lang} ${c}`,textContent:o})]}),range:r}]}))}}n.WidgetGenerator=s},7163:(e,n)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.Widgeter=void 0;class t{static create(e){return new t(e)}constructor(e){this.widgets=[],this.widgetGenerator=e.widgetGenerator}mark(e,n){this.updateWidgets(e,n)}updateWidgets(e,n){const t=this.widgetGenerator.generate(e,n);this.widgets.forEach((e=>e.clear())),this.widgets=t.map((n=>{const{range:t,element:r}=n,o=e.markText({line:t.line,ch:t.from},{line:t.line,ch:t.to},{atomic:!0,collapsed:!0,inclusiveLeft:!1,inclusiveRight:!1,replacedWith:r,selectLeft:!1,selectRight:!1});return{...n,clear:()=>o.clear()}}))}}n.Widgeter=t},9933:(e,n,t)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.CodeBlock=void 0;const r=t(6162);class o{static of(e){return new o(e)}constructor({start:e,end:n,lang:t,openingFence:r,closingFence:o}){this.start=e,this.end=n,this.lang=t,this.openingFence=r,this.closingFence=o}get startLine(){return this.start.line}get endLine(){return this.end.line}shiftDownBy(e){return r.Require.integer(e),o.of({start:this.start.plusLines(e),end:this.end.plusLines(e),lang:this.lang,openingFence:this.openingFence,closingFence:this.closingFence})}isBefore(e){return r.Require.nonNegativeInteger(e),this.endLine<e}isAfter(e){return r.Require.nonNegativeInteger(e),this.startLine>e}startsAt(e){return r.Require.nonNegativeInteger(e),this.startLine===e}endsAt(e){return r.Require.nonNegativeInteger(e),this.endLine===e}containsLine(e,n){var t;return r.Require.nonNegativeInteger(e),null!==(t=null==n?void 0:n.includeFences)&&void 0!==t&&t?e>=this.startLine&&e<=this.endLine:e>this.startLine&&e<this.endLine}get size(){return this.endLine-this.startLine-1}isEmpty(){return 0===this.size}equals(e){return this.start.equals(e.start)&&this.end.equals(e.end)&&this.lang===e.lang&&this.openingFence===e.openingFence&&this.closingFence===e.closingFence}}n.CodeBlock=o},7284:(e,n,t)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.CodeBlocks=void 0;const r=t(3106);var o;!function(e){e.areEqual=function(e,n){return r.Arrays.eq(e,n,((e,n)=>e.equals(n)))}}(o||(n.CodeBlocks=o={}))},6989:(e,n,t)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.CodeDocs=void 0;const r=t(1016),o=t(9802),i=t(7205);var u;!function(e){function n(e,n){const t={line:n.startLine+1,ch:0},r=e.getLine(n.endLine-1).length,i={line:n.endLine-1,ch:r};return o.Range.of({from:t,to:i})}function t(e,n){const{line:t}=e.getCursor();return n.find((e=>e.containsLine(t,{includeFences:!0})))}e.getCode=function(e,t,r){var o;if(null!==(o=null==r?void 0:r.includeFences)&&void 0!==o&&o)return e.getRange(t.start.fromPosition,t.end.toPosition);{const{from:r,to:o}=n(e,t);return e.getRange(r,o)}},e.getCodeRange=n,e.getActiveCodeBlock=t,e.clampCursorOfActiveCodeBlockToCode=function(e,o,u){const c=t(e,o);if((0,i.nil)(c))return;const s=n(e,c);r.Docs.clampCursorToRange(e,s,u)}}(u||(n.CodeDocs=u={}))},1849:(e,n,t)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.Origin=void 0;const r=t(8672);n.Origin={RenderHandler:`${r.CmExtension.extensionName}.RenderHandler`,CompleteHandler:`${r.CmExtension.extensionName}.CompleteHandler`}},4014:(e,n,t)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.RenderParser=void 0;const r=t(7205);class o{static create(e){return new o(e)}constructor(e){this.config=e.config,this.parser=e.parser}parse(e){return this.parser.parse(e).filter((e=>!this.isExcluded(e)))}isExcluded({lang:e}){return!(0,r.nil)(e)&&this.config.excludedLanguages.includes(e)}}n.RenderParser=o},4327:(e,n,t)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.RenderPerformer=void 0;const r=t(6989);class o{static create(e){return new o(e)}constructor(e){this.formatter=e.formatter,this.marker=e.marker}perform(e,n,t){return this.renderCodeBlocks(e,n,t)}renderCodeBlocks(e,n,t){const o=this.formatter.format(e,n,t);return r.CodeDocs.clampCursorOfActiveCodeBlockToCode(e,o,t),this.marker.mark(e,o),o}}n.RenderPerformer=o},6254:(e,n,t)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.Renderer=void 0;const r=t(7284);class o{static create(e){return new o(e)}constructor(e){this.codeBlocks=[],this.renderParser=e.renderParser,this.renderPerformer=e.renderPerformer}render(e,n){const t=this.renderParser.parse(e);r.CodeBlocks.areEqual(this.codeBlocks,t)||(this.codeBlocks=this.renderPerformer.perform(e,t,n))}}n.Renderer=o},4293:(e,n)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.BuiltInClass=void 0,n.BuiltInClass={monospace:"cm-jn-monospace"}},6589:(e,n)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.CodeBlockClass=void 0,n.CodeBlockClass={startBackground:"cb-start-background",startLine:"cb-start-line",startText:"cb-start-text",codeBackground:"cb-code-background",codeLine:"cb-code-line",codeText:"cb-code-text",endBackground:"cb-end-background",endLine:"cb-end-line",endText:"cb-end-text",startWidget:"cb-start-widget",endWidget:"cb-end-widget",copied:"cb-copied",copyBtn:"cb-copy-btn",first:"cb-first",last:"cb-last",openingFence:"cb-opening-fence",closingFence:"cb-closing-fence",lang:"cb-lang"}},7335:(e,n)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.FontAwesomeClass=void 0,n.FontAwesomeClass={solid:"fa-solid",clipboard:"fa-clipboard",clipboardCheck:"fa-clipboard-check"}},3607:(e,n,t)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.BetterCodeBlocks=void 0;const r=t(2508),o=t(9208);var i;!function(e){const n=()=>null,t=[];function i(e,n,t){return t[e[n]]}e.extension=function(e){return[o.Extensions.codeBlocksStateField,o.Extensions.fenceLineBreakDeleteFilter,o.Extensions.lastCodeLineSelectionFilter,o.Extensions.openingFenceCursorFilter,o.Extensions.closingFenceCursorFilter,i(e,"selectAllCapturing",{enabled:o.Extensions.allCodeSelectionFilter,disabled:t}),o.Extensions.codeFenceAtomicRanges,o.Extensions.emptyCodeBlockUpdater,o.Extensions.theme,i(e,"rendering",{enabled:o.Extensions.viewPlugin,disabled:t}),o.Extensions.configEditorAttributes,o.Extensions.configFacetOf(e)]},e.completionSource=function(e){return i(e,"completion",{enabled:(0,r.CompletionSource)(e),disabled:n})}}(i||(n.BetterCodeBlocks=i={}))},9208:(e,n,t)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.Extensions=void 0;const r=t(5100),o=t(9232),i=t(8074),u=t(4785),c=t(7491),s=t(3045),a=t(4607),l=t(9444),f=t(725),d=t(7353),g=t(3758),v=t(4644),h=t(1408),p=t(9743),m=t(3251);var _;!function(e){e.codeBlocksStateField=r.StateField.define(h.CodeBlocksStateField),e.fenceLineBreakDeleteFilter=r.EditorState.transactionFilter.of(s.FenceLineBreakDeleteFilter),e.lastCodeLineSelectionFilter=r.EditorState.transactionFilter.of(f.LastCodeLineSelectionFilter),e.openingFenceCursorFilter=r.EditorState.transactionFilter.of(c.OpeningFenceCursorFilter),e.closingFenceCursorFilter=r.EditorState.transactionFilter.of(i.ClosingFenceCursorFilter),e.allCodeSelectionFilter=r.EditorState.transactionFilter.of(l.AllCodeSelectionFilter),e.codeFenceAtomicRanges=o.EditorView.atomicRanges.of(u.CodeFenceAtomicRanges),e.emptyCodeBlockUpdater=o.EditorView.updateListener.of(a.EmptyCodeBlockUpdater),e.theme=o.EditorView.baseTheme(m.Theme),e.viewPlugin=o.ViewPlugin.define((e=>v.ViewPluginValue.create({view:e})),g.ViewPluginSpec),e.configEditorAttributes=o.EditorView.editorAttributes.compute([p.ConfigFacet],d.ConfigEditorAttributes),e.configFacetOf=function(e){return p.ConfigFacet.of(e)}}(_||(n.Extensions=_={}))},2508:(e,n,t)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.CompletionSource=function({completedLanguages:e}){return({state:n,pos:t})=>{const r=c.FenceCompletionParser.parseOpeningFenceAt(n,t);if((0,u.nil)(r))return null;const{codeInfoPrefix:i}=r,a=e.filter((e=>e.startsWith(i))),l=a.map((e=>s({openingFence:r,pos:t,lineBreak:n.lineBreak,desiredLang:e}))),f=o.Arrays.includes(a,i);return{from:t,options:f?[...l]:[...l,s({openingFence:r,pos:t,lineBreak:n.lineBreak,lowerRank:o.Strings.isNotEmpty(i)})]}}};const r=t(1235),o=t(3106),i=t(4137),u=t(7205),c=t(5103);function s({pos:e,lineBreak:n,openingFence:{indent:t,codeMark:o,codeInfoPrefix:c},lowerRank:s=!1,desiredLang:a}){const l=(0,u.def)(a)?a.length-c.length:0,f=(0,u.def)(a)?a.slice(c.length):"";return{label:`${o}${(0,u.def)(a)?a:c}`,type:i.CompletionType.type,apply:(i,u)=>{i.dispatch({annotations:r.pickedCompletion.of(u),changes:{from:e,insert:`${f}${n}${n}${t}${o}`},selection:{anchor:e+l+1}})},boost:s?-1:void 0}}},7432:(e,n,t)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.CodeBlock=void 0;const r=t(7446);class o{static of(e){return new o(e)}constructor({lang:e,openingFence:n,closingFence:t}){this.openingFence=n,this.closingFence=t,this.lang=e}get start(){return this.openingFence.from}get end(){return this.closingFence.to}get openingFenceStart(){return this.openingFence.from}get openingFenceEnd(){return this.openingFence.to}get openingFenceText(){return this.openingFence.text}get closingFenceStart(){return this.closingFence.from}get closingFenceEnd(){return this.closingFence.to}get closingFenceText(){return this.closingFence.text}get firstLine(){return this.openingFence.number}get lastLine(){return this.closingFence.number}get codeStart(){return this.hasCode()?this.openingFenceEnd+1:void 0}get codeEnd(){return this.hasCode()?this.closingFenceStart-1:void 0}get firstCodeLine(){return this.hasCode()?this.openingFence.number+1:void 0}get lastCodeLine(){return this.hasCode()?this.closingFence.number-1:void 0}get codeLineCount(){return this.closingFence.number-this.openingFence.number-1}get codeLines(){return this.hasCode()?r.Iterables.range({start:this.firstCodeLine,endExclusive:this.lastCodeLine+1}):r.Iterables.empty()}hasCode(){return this.codeLineCount>=1}includesRange({from:e,to:n},{includeFences:t}){return t?e>=this.start&&n<=this.end:!!this.hasCode()&&e>=this.codeStart&&n<=this.codeEnd}isExactRange({from:e,to:n},{includeFences:t}){return t?e===this.start&&n===this.end:!!this.hasCode()&&e===this.codeStart&&n===this.codeEnd}touchesRange({from:e,to:n},{includeFences:t}){return t?!(n<this.start||e>this.end):!!this.hasCode()&&!(n<this.codeStart||e>this.codeEnd)}}n.CodeBlock=o},3182:(e,n,t)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.CodeDocs=void 0;const r=t(7446);var o;!function(e){e.getCode=function(e,n,t){return t.includeFences?e.sliceString(n.start,n.end):n.hasCode()?e.sliceString(n.codeStart,n.codeEnd):""},e.iterCodeLines=function(e,n,t){return t.includeFences?e.iterLines(n.firstLine,n.lastLine+1):n.hasCode()?e.iterLines(n.firstCodeLine,n.lastCodeLine+1):r.Iterables.empty()}}(o||(n.CodeDocs=o={}))},3586:(e,n,t)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.CodeEditorStates=void 0;const r=t(9208),o=t(9743);var i;!function(e){e.getConfig=function(e){return e.facet(o.ConfigFacet)},e.getCodeBlocks=function(e){return e.field(r.Extensions.codeBlocksStateField)}}(i||(n.CodeEditorStates=i={}))},6146:(e,n)=>{"use strict";var t;Object.defineProperty(n,"__esModule",{value:!0}),n.Config=void 0,function(e){e.createDefault=function(){return{completedLanguages:["bash","c","c#","c++","clojure","clojurescript","coffeescript","cpp","crystal","cs","csharp","css","d","dart","diff","dockerfile","ecmascript","elm","erlang","f#","forth","fortran","fsharp","gherkin","go","groovy","haskell","haxe","html","http","ini","java","javascript","jinja2","js","json","jsx","julia","kotlin","latex","less","lisp","lua","markdown","mathematica","mysql","node","objc","objc++","objective-c","objective-c++","ocaml","pascal","perl","php","plsql","postgresql","powershell","properties","protobuf","python","r","rake","rb","rscript","rss","ruby","rust","sass","scala","scheme","scss","sh","shell","smalltalk","sql","sqlite","swift","tcl","toml","ts","tsx","typescript","vb.net","vbscript","verilog","xhtml","xml","yaml","yml","zsh"],completion:"enabled",rendering:"enabled",selectAllCapturing:"enabled",renderLayout:"minimal",cornerStyle:"square",excludedLanguages:[],copyFormat:"code"}}}(t||(n.Config=t={}))},8074:(e,n,t)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.ClosingFenceCursorFilter=void 0;const r=t(1939),o=t(7205),i=t(3586);n.ClosingFenceCursorFilter=(0,r.CursorMovementFilter)((e=>{const{startState:n,pos:t,previousPos:r,isVertical:u}=e,c=i.CodeEditorStates.getCodeBlocks(n).find((e=>t===e.closingFenceStart));if((0,o.nil)(c))return;const{codeEnd:s,openingFenceStart:a,closingFenceEnd:l}=c;return u||t>=r?l:null!=s?s:a}))},4785:(e,n,t)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.CodeFenceAtomicRanges=void 0;const r=t(5100),o=t(3586),i=new class extends r.RangeValue{};n.CodeFenceAtomicRanges=({state:e})=>{const n=new r.RangeSetBuilder;for(const t of o.CodeEditorStates.getCodeBlocks(e))n.add(t.openingFenceStart,t.openingFenceEnd,i),n.add(t.closingFenceStart,t.closingFenceEnd,i);return n.finish()}},7491:(e,n,t)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.OpeningFenceCursorFilter=void 0;const r=t(1939),o=t(7205),i=t(3586);n.OpeningFenceCursorFilter=(0,r.CursorMovementFilter)((e=>{const{startState:n,pos:t,previousPos:r,isVertical:u}=e,c=i.CodeEditorStates.getCodeBlocks(n).find((e=>t===e.openingFenceEnd));if((0,o.nil)(c))return;const{openingFenceStart:s,codeStart:a,closingFenceEnd:l}=c;return u||t<=r?s:null!=a?a:l}))},3045:(e,n,t)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.FenceLineBreakDeleteFilter=void 0;const r=t(391),o=t(3586);n.FenceLineBreakDeleteFilter=e=>{if(!r.Transactions.isDeleteDirection(e))return[e];const{startState:n,changes:t}=e,i=o.CodeEditorStates.getCodeBlocks(n).some((e=>e.hasCode()&&function(e,{codeStart:n,codeEnd:t,openingFenceEnd:r,closingFenceStart:o}){return"cover"===e.touchesRange(n,r)||"cover"===e.touchesRange(o,t)}(t,e)));return i?[{}]:[e]}},4607:(e,n,t)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.EmptyCodeBlockUpdater=void 0;const r=t(3106),o=t(3586);n.EmptyCodeBlockUpdater=({docChanged:e,state:n,view:t})=>{if(!e)return;const i=o.CodeEditorStates.getCodeBlocks(n).filter((e=>!e.hasCode())).map((e=>({from:e.openingFenceEnd,insert:n.lineBreak})));r.Arrays.isNotEmpty(i)&&t.dispatch({changes:i})}},9444:(e,n,t)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.AllCodeSelectionFilter=void 0;const r=t(8788),o=t(391),i=t(7205),u=t(3586);n.AllCodeSelectionFilter=e=>{if(!o.Transactions.isSelectAll(e))return[e];const{startState:n}=e;if(r.EditorSelections.isMultiple(n.selection))return[e];const t=n.selection.main,c=u.CodeEditorStates.getCodeBlocks(n).find((e=>e.includesRange(t,{includeFences:!1})));return(0,i.nil)(c)||c.isExactRange(t,{includeFences:!1})?[e]:[e,{selection:{anchor:c.codeStart,head:c.codeEnd}}]}},725:(e,n,t)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.LastCodeLineSelectionFilter=void 0;const r=t(8788),o=t(7205),i=t(3586);n.LastCodeLineSelectionFilter=e=>{const{selection:n,docChanged:t,startState:u}=e;if(t||(0,o.nil)(n))return[e];if(r.EditorSelections.isMultiple(n)||n.main.empty)return[e];const c=n.main,s=i.CodeEditorStates.getCodeBlocks(u).find((e=>e.hasCode()&&e.includesRange(c,{includeFences:!0})));if((0,o.nil)(s))return[e];const a=u.doc.line(s.lastCodeLine);return c.from!==a.from||c.to!==a.to+1?[e]:[e,{selection:{anchor:a.from,head:a.to}}]}},3579:(e,n,t)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.CodeBlocksParser=void 0;const r=t(3329),o=t(3106),i=t(7944),u=t(7205),c=t(7432),s=t(3182),a=t(8206);var l;!function(e){function n(e,n){const r=function(e){return e.length<2?void 0:i.Nodes.isCodeMark(o.Arrays.last(e))?{openingCodeMark:o.Arrays.head(e),closingCodeMark:o.Arrays.last(e),codeInfo:i.Nodes.isCodeInfo(e[1])?e[1]:void 0}:void 0}(n);if((0,u.nil)(r))return;const l=function(e,{openingCodeMark:n,codeInfo:r,closingCodeMark:o}){return c.CodeBlock.of({openingFence:t(e.lineAt(n.from)),closingFence:t(e.lineAt(o.from)),lang:(0,u.def)(r)?e.sliceString(r.from,r.to):void 0})}(e,r);return function(e,n){for(const t of s.CodeDocs.iterCodeLines(e,n,{includeFences:!1}))if(a.FenceMatcher.matchesOpeningFence(t))return!0;return!1}(e,l)?void 0:l}function t({from:e,to:n,number:t,text:r}){return{from:e,to:n,number:t,text:r}}e.parse=function(e){const t=(0,r.syntaxTree)(e).cursor(),o=[];for(let r=t.next(!0);r;r=t.next(!1)){if(!i.Nodes.isFencedCode(t))continue;t.next();const r=[];do{r.push({name:t.name,from:t.from,to:t.to})}while(t.nextSibling());const c=n(e.doc,r);if((0,u.nil)(c))return[];o.push(c)}return o}}(l||(n.CodeBlocksParser=l={}))},5103:(e,n,t)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.FenceCompletionParser=void 0;const r=t(3329),o=t(7944),i=t(7205);var u;!function(e){e.parseOpeningFenceAt=function(e,n){if(n<3)return;const t=function(e){if(!o.Nodes.isCodeMark(e))return!1;const n=e.parent;return e.from===n.from}(u=(0,r.syntaxTree)(e).resolveInner(n,-1))?{codeMark:u,codeInfo:void 0}:o.Nodes.isCodeInfo(u)?{codeMark:u.prevSibling,codeInfo:u}:void 0;var u;if((0,i.nil)(t))return;const{codeMark:c,codeInfo:s}=t,{doc:a}=e;return{indent:a.sliceString(a.lineAt(n).from,c.from),codeMark:a.sliceString(c.from,(0,i.def)(s)?s.from:n),codeInfoPrefix:(0,i.def)(s)?a.sliceString(s.from,n):""}}}(u||(n.FenceCompletionParser=u={}))},8206:(e,n)=>{"use strict";var t;Object.defineProperty(n,"__esModule",{value:!0}),n.FenceMatcher=void 0,function(e){e.matchesOpeningFence=function(e){return/^ {0,3}(?:```|~~~)/.test(e)}}(t||(n.FenceMatcher=t={}))},7353:(e,n,t)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.ConfigEditorAttributes=function(e){const{cornerStyle:n,rendering:t,renderLayout:o}=r.CodeEditorStates.getConfig(e);return{"data-cb-corner-style":n,"data-cb-rendering":t,"data-cb-render-layout":o}};const r=t(3586)},3758:(e,n)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.ViewPluginSpec=void 0,n.ViewPluginSpec={decorations:e=>e.decorations}},4644:(e,n,t)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.ViewPluginValue=void 0;const r=t(1127);class o{static create(e){return new o(e)}constructor({view:e}){this.decorations=r.DecorationSets.create(e)}update({docChanged:e,viewportChanged:n,view:t}){(e||n)&&(this.decorations=r.DecorationSets.create(t))}}n.ViewPluginValue=o},1127:(e,n,t)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.DecorationSets=void 0;const r=t(5100),o=t(7205),i=t(3586),u=t(2919),c=t(5115);var s;!function(e){e.create=function(e){const n=new r.RangeSetBuilder;for(const t of function({state:e,visibleRanges:n}){const{excludedLanguages:t}=i.CodeEditorStates.getConfig(e);return i.CodeEditorStates.getCodeBlocks(e).filter((e=>!function(e,n){return!(0,o.nil)(e.lang)&&n.includes(e.lang)}(e,t))).filter((e=>function(e,n){return n.some((n=>e.touchesRange(n,{includeFences:!0})))}(e,n)))}(e)){const{openingFenceStart:r,openingFenceEnd:o}=t;n.add(r,r,u.LineDecorations.openingFence),n.add(r,o,c.ReplaceDecorations.openingFence(t));const{firstCodeLine:i,lastCodeLine:s,codeLines:a}=t;for(const t of a){const{from:r}=e.state.doc.line(t),o=t===i,c=t===s;n.add(r,r,u.LineDecorations.code({isFirst:o,isLast:c}))}const{closingFenceStart:l,closingFenceEnd:f}=t;n.add(l,l,u.LineDecorations.closingFence),n.add(l,f,c.ReplaceDecorations.closingFence(t))}return n.finish()}}(s||(n.DecorationSets=s={}))},2919:(e,n,t)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.LineDecorations=void 0;const r=t(9232),o=t(6215),i=t(1464);var u;!function(e){function n({isFirst:e,isLast:n}){return{attributes:{class:o.Arrays.compact([i.CodeBlockClass.codeLine,e?i.CodeBlockClass.first:void 0,n?i.CodeBlockClass.last:void 0]).join(" ")}}}e.openingFenceSpec={attributes:{class:i.CodeBlockClass.startLine}},e.openingFence=r.Decoration.line(e.openingFenceSpec),e.closingFenceSpec={attributes:{class:i.CodeBlockClass.endLine}},e.closingFence=r.Decoration.line(e.closingFenceSpec),e.codeSpec=n,e.code=function(e){return r.Decoration.line(n(e))}}(u||(n.LineDecorations=u={}))},5115:(e,n,t)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.ReplaceDecorations=void 0;const r=t(9232),o=t(419);var i;!function(e){function n(e){return{widget:o.Widgets.OpeningFence.create({codeBlock:e})}}function t(e){return{widget:o.Widgets.ClosingFence.create({codeBlock:e})}}e.openingFenceSpec=n,e.openingFence=function(e){return r.Decoration.replace(n(e))},e.closingFenceSpec=t,e.closingFence=function(e){return r.Decoration.replace(t(e))}}(i||(n.ReplaceDecorations=i={}))},419:(e,n,t)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.Widgets=void 0;const r=t(9232),o=t(9234),i=t(3182),u=t(3586),c=t(1464),s=t(3474),{span:a,i:l,button:f}=o.Html;var d;!function(e){class n extends r.WidgetType{static create(e){return new n(e)}constructor({codeBlock:e}){super(),this.codeBlock=e}toDOM(e){const n=l({className:`${s.FontAwesomeClass.solid} ${s.FontAwesomeClass.clipboard}`}),t=f({children:[n],className:c.CodeBlockClass.copyBtn,onClick:this.generateCopyButtonOnClick(e.state,n),title:"Copy Code"});return a({className:c.CodeBlockClass.startWidget,children:[a({className:`${c.CodeBlockClass.openingFence}`,textContent:this.codeBlock.openingFenceText}),t]})}generateCopyButtonOnClick(e,n){const t="fencedCode"===u.CodeEditorStates.getConfig(e).copyFormat,r=o=>{const u=o.currentTarget,c=i.CodeDocs.getCode(e.doc,this.codeBlock,{includeFences:t});this.handleCopyButtonClicked({code:c,copyButton:u,icon:n,onClick:r})};return r}async handleCopyButtonClicked({code:e,copyButton:t,icon:r,onClick:i}){o.Html.removeOnClick(t,i),await navigator.clipboard.writeText(e),o.Html.addClass(t,c.CodeBlockClass.copied),o.Html.swapClass(r,{from:s.FontAwesomeClass.clipboard,to:s.FontAwesomeClass.clipboardCheck}),window.setTimeout((()=>{o.Html.addOnClick(t,i),o.Html.removeClass(t,c.CodeBlockClass.copied),o.Html.swapClass(r,{from:s.FontAwesomeClass.clipboardCheck,to:s.FontAwesomeClass.clipboard})}),n.copyButtonResetMillis)}}n.copyButtonResetMillis=3e3,e.OpeningFence=n;class t extends r.WidgetType{static create(e){return new t(e)}constructor({codeBlock:e}){super(),this.codeBlock=e}toDOM(e){return a({className:c.CodeBlockClass.endWidget,children:[a({className:`${c.CodeBlockClass.closingFence}`,textContent:this.codeBlock.closingFenceText}),a({className:`${c.CodeBlockClass.lang}`,textContent:this.codeBlock.lang})]})}}e.ClosingFence=t}(d||(n.Widgets=d={}))},1408:(e,n,t)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.CodeBlocksStateField=void 0;const r=t(3579);n.CodeBlocksStateField={create:e=>r.CodeBlocksParser.parse(e),update:(e,{docChanged:n,state:t})=>n?r.CodeBlocksParser.parse(t):e}},9743:(e,n,t)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.ConfigFacet=n.ConfigFacetSpec=void 0;const r=t(638),o=t(6146);n.ConfigFacetSpec={defaultValue:o.Config.createDefault},n.ConfigFacet=r.Facets.defineSingular(n.ConfigFacetSpec)},6792:(e,n)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.MinimalMixin=void 0,n.MinimalMixin={"&[data-cb-render-layout='minimal'][data-cb-corner-style='round'] .cb-code-line.cb-first":{"border-top-left-radius":"3px","border-top-right-radius":"3px"},"&[data-cb-render-layout='minimal'][data-cb-corner-style='round'] .cb-code-line.cb-last":{"border-bottom-right-radius":"3px","border-bottom-left-radius":"3px"},"&[data-cb-render-layout='minimal'] .cb-start-line, &[data-cb-render-layout='minimal'] .cb-end-line":{position:"relative",background:"none"},"&[data-cb-render-layout='minimal'] .cb-opening-fence, &[data-cb-render-layout='minimal'] .cb-closing-fence":{visibility:"hidden"},"&[data-cb-render-layout='minimal'] .cb-copy-btn":{position:"absolute",top:"100%",right:0,height:"100%",padding:"0 0.25ch"}}},4416:(e,n)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.StandardMixin=void 0,n.StandardMixin={"&[data-cb-render-layout='standard'][data-cb-corner-style='square'] .cb-start-line":{"border-top-left-radius":0,"border-top-right-radius":0},"&[data-cb-render-layout='standard'][data-cb-corner-style='square'] .cb-end-line":{"border-bottom-right-radius":0,"border-bottom-left-radius":0},"&[data-cb-render-layout='standard'] .cb-copy-btn":{padding:"0 0.25ch"},"&[data-cb-render-layout='standard'] .cb-lang":{visibility:"hidden"}}},3251:(e,n,t)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.Theme=void 0;const r=t(6792),o=t(4416);n.Theme={...r.MinimalMixin,...o.StandardMixin,"&.cm-editor .cm-codeBlock":{"border-width":0,"padding-right":0},"& .cb-start-widget, & .cb-end-widget":{display:"inline-flex","align-items":"center","justify-content":"space-between",width:"100%"},"& .cb-opening-fence, & .cb-closing-fence":{color:"var(--joplin-color-faded)","-webkitFontSmoothing":"antialiased","user-select":"none"},"& .cb-copy-btn":{cursor:"pointer",background:"none",border:"none",opacity:0,transition:"opacity 0.5s ease-in-out 0s"},"& .cb-copy-btn:hover":{opacity:1},"& .cb-copy-btn.cb-copied":{opacity:1},"& .cb-copy-btn .fa-solid":{"font-family":'"Font Awesome 5 Free"',"font-size":"2.5ch","font-style":"normal","font-weight":900,"line-height":1,color:"var(--joplin-color-faded)","-webkitFontSmoothing":"antialiased"},"& .cb-lang":{color:"var(--joplin-color-faded)","-webkitFontSmoothing":"antialiased","user-select":"none"}}},1464:(e,n)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.CodeBlockClass=void 0,n.CodeBlockClass={startLine:"cb-start-line",codeLine:"cb-code-line",endLine:"cb-end-line",startWidget:"cb-start-widget",endWidget:"cb-end-widget",copied:"cb-copied",copyBtn:"cb-copy-btn",first:"cb-first",last:"cb-last",openingFence:"cb-opening-fence",closingFence:"cb-closing-fence",lang:"cb-lang"}},3474:(e,n)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.FontAwesomeClass=void 0,n.FontAwesomeClass={solid:"fa-solid",clipboard:"fa-clipboard",clipboardCheck:"fa-clipboard-check"}},7616:(e,n,t)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.ContentScript=void 0;const r=t(2065),o=`enable${t(8672).CmExtension.extensionName}`;class i{static create(e){return new i(e)}constructor(e){this.codeMirrorResources=[],this.codeMirrorOptions={[o]:!0},this.assets=()=>this.styles.map((e=>({name:e}))),this.createCm6Extension=e.createCm6Extension,this.createCm5Extension=e.createCm5Extension,this.styles=e.styles}plugin(e){r.JoplinPlugins.isCodeMirror6(e)?this.initializeCm6Extension(e):this.initializeCm5Extension(e)}initializeCm6Extension(e){this.createCm6Extension(e)}initializeCm5Extension(e){e.defineOption(o,!1,((n,t)=>{t&&this.createCm5Extension(e,n)}))}}n.ContentScript=i},1016:(e,n,t)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.Docs=void 0;const r=t(2927),o=t(9802),i=t(6162);var u;!function(e){function n(e,n){let t=!0;const r=()=>t=!1;let o=0;e.eachLine((e=>{t&&n({line:o,text:e.text,endIteration:r}),o++}))}e.mapLines=function(e,t){const r=[];let o=0;return n(e,(e=>{r.push(t({line:o,text:e.text})),o++})),r},e.eachLine=n,e.eachLineInserting=function(e,{line:n,text:t},r){i.Require.nonNegativeInteger(n);let o=!0;const u=()=>o=!1;let c=0;e.eachLine((e=>{o&&c===n&&(r({line:c,text:t,endIteration:u}),c++),o&&r({line:c,text:e.text,endIteration:u}),c++})),o&&c===n&&r({line:c,text:t,endIteration:u})},e.insertNewLine=function(e,n,t){i.Require.nonNegativeInteger(n.line),i.Require.nonNegativeInteger(n.ch),e.replaceRange(e.lineSeparator(),n,void 0,t)},e.addLineClasses=function(e,n,t){var r,o,u,c;let s;return i.Require.nonNegativeInteger(n),null===(r=t.background)||void 0===r||r.forEach((t=>{s=e.addLineClass(n,"background",t)})),null===(o=t.text)||void 0===o||o.forEach((t=>{s=e.addLineClass(n,"text",t)})),null===(u=t.wrap)||void 0===u||u.forEach((t=>{s=e.addLineClass(n,"wrap",t)})),null===(c=t.gutter)||void 0===c||c.forEach((t=>{s=e.addLineClass(n,"gutter",t)})),null!=s?s:e.getLineHandle(n)},e.removeLineClasses=function(e,n,t){var r,o,i,u;null===(r=t.background)||void 0===r||r.forEach((t=>{e.removeLineClass(n,"background",t)})),null===(o=t.text)||void 0===o||o.forEach((t=>{e.removeLineClass(n,"text",t)})),null===(i=t.wrap)||void 0===i||i.forEach((t=>{e.removeLineClass(n,"wrap",t)})),null===(u=t.gutter)||void 0===u||u.forEach((t=>{e.removeLineClass(n,"gutter",t)}))},e.isSelected=function(e,n){return e.listSelections().some((({head:e,anchor:t})=>{const r=o.Range.of({from:e,to:t}),i=o.Range.of({from:t,to:e});return r.equals(n)||i.equals(n)}))},e.clampCursorToRange=function(e,n,t){const o=e.getCursor();r.Positions.areStrictlyOrdered({before:o,after:n.from})?e.setCursor(n.from,void 0,{origin:t}):r.Positions.areStrictlyOrdered({before:n.to,after:o})&&e.setCursor(n.to,void 0,{origin:t})}}(u||(n.Docs=u={}))},5978:(e,n)=>{"use strict";var t;Object.defineProperty(n,"__esModule",{value:!0}),n.Events=void 0,function(e){e.isSelectAll=function(e,n){var t;if(1!==(null===(t=n.ranges)||void 0===t?void 0:t.length))return!1;const[{anchor:r,head:o}]=n.ranges;if(r.line>0||r.ch>0)return!1;const i=e.lastLine();return o.line===i&&o.ch===e.getLine(i).length}}(t||(n.Events=t={}))},3964:(e,n,t)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.LineSegment=void 0;const r=t(6162);class o{static of(e){return new o(e)}constructor({line:e,from:n,to:t}){r.Require.nonNegativeInteger(e),r.Require.nonNegativeInteger(n),r.Require.nonNegativeInteger(t),r.Require.validRange({from:n,to:t}),this.line=e,this.from=n,this.to=t}get fromPosition(){return{line:this.line,ch:this.from}}get toPosition(){return{line:this.line,ch:this.to}}plusLines(e){return r.Require.integer(e),o.of({line:this.line+e,from:this.from,to:this.to})}equals(e){return this.line===e.line&&this.from===e.from&&this.to===e.to}}n.LineSegment=o},2927:(e,n)=>{"use strict";var t;Object.defineProperty(n,"__esModule",{value:!0}),n.Positions=void 0,function(e){e.areEqual=function(e,n){return e.line===n.line&&e.ch===n.ch},e.areStrictlyOrdered=function({before:e,after:n}){return e.line<n.line||!(e.line>n.line)&&e.ch<n.ch}}(t||(n.Positions=t={}))},9802:(e,n,t)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.Range=void 0;const r=t(2927);class o{static of(e){return new o(e)}constructor({from:e,to:n}){this.from=e,this.to=n}equals(e){return r.Positions.areEqual(this.from,e.from)&&r.Positions.areEqual(this.to,e.to)}}n.Range=o},4137:(e,n)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.CompletionType=void 0,n.CompletionType={type:"type"}},1939:(e,n,t)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.CursorMovementFilter=void 0;const r=t(8788),o=t(7205);n.CursorMovementFilter=e=>n=>{const{selection:t,startState:i,docChanged:u}=n;if((0,o.nil)(t)||u)return[n];if(!r.EditorSelections.isSingleCursor(t))return[n];const c=t.main,s=e({startState:i,pos:c.head,previousPos:i.selection.main.head,isVertical:(0,o.def)(c.goalColumn)});return(0,o.nil)(s)?[n]:[n,{selection:r.EditorSelections.singleCursor({pos:s,assoc:c.assoc,bidiLevel:c.bidiLevel,goalColumn:c.goalColumn})}]}},8788:(e,n,t)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.EditorSelections=void 0;const r=t(5100);var o;!function(e){function n(e){return 1===e.ranges.length}e.isSingle=n,e.isMultiple=function(e){return e.ranges.length>1},e.isSingleCursor=function(e){return n(e)&&e.main.empty},e.singleCursor=function({pos:e,assoc:n,bidiLevel:t,goalColumn:o}){return r.EditorSelection.create([r.EditorSelection.cursor(e,n,t,o)])}}(o||(n.EditorSelections=o={}))},638:(e,n,t)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.Facets=void 0;const r=t(5100),o=t(3106);var i;!function(e){e.defineSingular=function(e){return r.Facet.define({...o.Dicts.deleteKey(e,"defaultValue"),combine:n=>o.Arrays.isEmpty(n)?e.defaultValue():o.Arrays.head(n)})}}(i||(n.Facets=i={}))},391:(e,n,t)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.Transactions=void 0;const r=t(8788),o=t(9469),i=t(7205);var u;!function(e){function n(e){return e.isUserEvent(o.UserEvent.deleteBackward)}function t(e){return e.isUserEvent(o.UserEvent.deleteForward)}e.isDeleteBackward=n,e.isDeleteForward=t,e.isDeleteDirection=function(e){return n(e)||t(e)},e.isSelectAll=function(e){const{selection:n,docChanged:t,startState:o}=e;if(t)return!1;if((0,i.nil)(n)||r.EditorSelections.isMultiple(n))return!1;const{main:u}=n;return 0===u.from&&u.to===o.doc.length}}(u||(n.Transactions=u={}))},9469:(e,n)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.UserEvent=void 0,n.UserEvent={deleteBackward:"delete.backward",deleteForward:"delete.forward"}},9234:(e,n,t)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.Html=void 0;const r=t(7205);var o;!function(e){function n(e,n){e.addEventListener("click",n)}e.button=function(e){return t({tagName:"button",...e})},e.span=function(e){return t({tagName:"span",...e})},e.i=function({className:e}){const n=document.createElement("i");return n.className=e,n},e.addClass=function({classList:e},n){e.add(n)},e.removeClass=function({classList:e},n){e.remove(n)},e.swapClass=function({classList:e},{from:n,to:t}){e.contains(n)&&(e.remove(n),e.add(t))},e.addOnClick=n,e.removeOnClick=function(e,n){e.removeEventListener("click",n)};const t=({tagName:e,className:t,title:o,textContent:i,onClick:u,children:c})=>{const s=document.createElement(e);return s.className=t,(0,r.def)(o)&&(s.title=o),(0,r.def)(i)&&(s.textContent=i),(0,r.def)(u)&&n(s,u),(0,r.def)(c)&&c.forEach((e=>s.appendChild(e))),s}}(o||(n.Html=o={}))},2065:(e,n)=>{"use strict";var t;Object.defineProperty(n,"__esModule",{value:!0}),n.JoplinPlugins=void 0,function(e){e.isCodeMirror6=function(e){return"cm6"in e}}(t||(n.JoplinPlugins=t={}))},7944:(e,n)=>{"use strict";var t;Object.defineProperty(n,"__esModule",{value:!0}),n.Nodes=void 0,function(e){e.isFencedCode=function(e){return"FencedCode"===e.name},e.isCodeMark=function(e){return"CodeMark"===e.name},e.isCodeInfo=function(e){return"CodeInfo"===e.name}}(t||(n.Nodes=t={}))},6215:(e,n,t)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.Arrays=void 0;const r=t(7205),o=t(6162);var i;!function(e){e.compact=function(e){return e.filter((e=>(0,r.def)(e)))},e.onlyElement=function(e){return o.Require.hasSingleElement(e),e[0]}}(i||(n.Arrays=i={}))},7446:(e,n,t)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.Iterables=void 0;const r=t(6162);var o;!function(e){e.range=function({start:e,endExclusive:n}){return r.Require.integer(e),r.Require.integer(n),r.Require.validRange({from:e,to:n}),{*[Symbol.iterator](){for(let t=e;t<n;t++)yield t}}},e.empty=function(){return{*[Symbol.iterator](){}}}}(o||(n.Iterables=o={}))},6674:(e,n,t)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.Repeat=void 0;const r=t(6162);var o;!function(e){e.times=function(e,n){r.Require.nonNegativeInteger(e);const t=new Array(e);for(let r=0;r<e;r++)t[r]=n(r);return t}}(o||(n.Repeat=o={}))},6162:(e,n)=>{"use strict";var t;Object.defineProperty(n,"__esModule",{value:!0}),n.Require=void 0,function(e){e.integer=function(e){if(!Number.isInteger(e))throw new Error(`${e} is not an integer`)},e.nonNegativeInteger=function(e){if(!Number.isInteger(e))throw new Error(`${e} is not an integer`);if(e<0)throw new Error(`${e} is negative`)},e.validRange=function({from:e,to:n}){if(e>n)throw new Error(`[${e},${n}] is an invalid range`)},e.hasSingleElement=function(e){if(0===e.length)throw new Error(`${e.toString()} is empty`);if(e.length>1)throw new Error(`${e.toString()} has ${e.length} elements`)}}(t||(n.Require=t={}))},9056:(e,n,t)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.Retrier=void 0;const r=t(6162);class o{static create(e){return new o(e)}constructor(e){this.window=e.window}async retry({fn:e,isSuccess:n,startDelayMillis:t}){r.Require.nonNegativeInteger(t);let o=t,i=await e();for(;!n(i);)await this.delay(o),o*=2,i=await e();return i}async delay(e){return await new Promise((n=>this.window.setTimeout(n,e)))}}n.Retrier=o},7205:(e,n,t)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.nil=n.def=void 0;const r=t(3106);n.def=r.Guards.isNotNullable,n.nil=r.Guards.isNullable},3106:(e,n,t)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.Strings=n.Results=n.Options=n.Numbers=n.Guards=n.Functions=n.Dicts=n.Bools=n.Arrays=void 0;var r=t(93);Object.defineProperty(n,"Arrays",{enumerable:!0,get:function(){return r.A}}),Object.defineProperty(n,"Bools",{enumerable:!0,get:function(){return r.B}}),Object.defineProperty(n,"Dicts",{enumerable:!0,get:function(){return r.D}}),Object.defineProperty(n,"Functions",{enumerable:!0,get:function(){return r.F}}),Object.defineProperty(n,"Guards",{enumerable:!0,get:function(){return r.G}}),Object.defineProperty(n,"Numbers",{enumerable:!0,get:function(){return r.N}}),Object.defineProperty(n,"Options",{enumerable:!0,get:function(){return r.O}}),Object.defineProperty(n,"Results",{enumerable:!0,get:function(){return r.R}}),Object.defineProperty(n,"Strings",{enumerable:!0,get:function(){return r.S}})},8714:(e,n,t)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.JoplinPluginClient=void 0;const r=t(7205),o=t(9004);class i{static create(e){return new i(e)}constructor(e){this.call=function(e){const{call:n}=e,t=async function({call:e,retrier:n}){await n.retry({fn:async()=>await e(o.PingRequest.of()),isSuccess:r.def,startDelayMillis:50})}(e);return e=>t.then((async()=>n(e)))}(e)}async getSettings(){return(await this.call(o.GetSettingsRequest.of())).settings}async ping(){await this.call(o.PingRequest.of())}}n.JoplinPluginClient=i},9004:(e,n)=>{"use strict";var t,r,o,i;Object.defineProperty(n,"__esModule",{value:!0}),n.PingResponse=n.PingRequest=n.GetSettingsResponse=n.GetSettingsRequest=void 0,function(e){e.of=function(){return{kind:"getSettings"}}}(t||(n.GetSettingsRequest=t={})),function(e){e.of=function(e){return{kind:"getSettings",settings:e}}}(r||(n.GetSettingsResponse=r={})),function(e){e.of=function(){return{kind:"ping"}}}(o||(n.PingRequest=o={})),function(e){e.of=function(){return{kind:"ping"}}}(i||(n.PingResponse=i={}))},1235:e=>{"use strict";e.exports=require("@codemirror/autocomplete")},3329:e=>{"use strict";e.exports=require("@codemirror/language")},5100:e=>{"use strict";e.exports=require("@codemirror/state")},9232:e=>{"use strict";e.exports=require("@codemirror/view")}},n={};function t(r){var o=n[r];if(void 0!==o)return o.exports;var i=n[r]={exports:{}};return e[r](i,i.exports,t),i.exports}var r={};(()=>{"use strict";var e=r;const n=t(9056),o=t(8714),i=t(7616),u=t(1095);async function c(e){const t=n.Retrier.create({window}),r=o.JoplinPluginClient.create({call:e,retrier:t});return await r.getSettings()}e.default=e=>i.ContentScript.create({createCm6Extension:async n=>{const r=await c(e.postMessage),{BetterCodeBlocks:o}=await Promise.resolve().then((()=>t(3607)));n.addExtension([o.extension(r),n.joplinExtensions.completionSource(o.completionSource(r))])},createCm5Extension:async(n,t)=>{const r=await c(e.postMessage);return u.CmExtensions.createDefault({codeMirror:n,editor:t,config:r})},styles:["./cm-extension/cm5/assets/cm-extension.css","./cm-extension/cm5/assets/opt/render-layout/minimal.css","./cm-extension/cm5/assets/opt/render-layout/standard.css"]})})(),exports.default=r.default})();