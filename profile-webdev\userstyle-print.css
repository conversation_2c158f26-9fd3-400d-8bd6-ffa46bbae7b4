/* PRINT ONLY STYLES /////////////////////////////////////////////////////////////////////////////*/
/*////////////////////////////////////////////////////////////////////////////////////////////////*/
/*////////////////////////////////////////////////////////////////////////////////////////////////*/

html {
    div.exported-note {
        width: var(--default-body-max-width-print) !important;
        max-width: var(--default-body-max-width-print) !important;
        padding: 0 !important;
        margin: 0 !important;
        box-sizing: border-box !important;

        #rendered-md {
            .inline-code {
                &:hover {
                    border: var(--inlinecode-border-hover) !important;
                    background-color: var(--inlinecode-background-color-hover) !important;
                }
            }
        }
    }

    div.exported-note-title {
        padding: 0 !important;
        margin: 0 !important;
        visibility: hidden !important;
        display: none !important;
    }

    /* AVOID PAGE BREAKS ON IMPORTANT ELEMENTS ///////////////////////////////////////////////////*/
    div:not(.exported-note-title),
    pre, img, h1, h2, h3, h4, h5, h6 {
        page-break-inside: avoid !important;
    }
    table {
        page-break-inside: auto;
    }
}