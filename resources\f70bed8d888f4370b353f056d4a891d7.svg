<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M0 13H16V6H2C0.9 6 0 6.9 0 8V13Z" fill="#CCCCCC"/>
<path d="M32 6H16V13H32V6Z" fill="#999999"/>
<path d="M48 13H32V6H46C47.1 6 48 6.9 48 8V13Z" fill="#666666"/>
<path d="M46 42H2C0.9 42 0 41.1 0 40V12H48V40C48 41.1 47.1 42 46 42Z" fill="url(#paint0_linear)"/>
<g filter="url(#filter0_dd)">
<path d="M15.2 24.3L6.39999 33.1C5.89999 33.6 5.89999 34.3 6.39999 34.7L8.19999 36.5C8.69999 37 9.4 37 9.8 36.5L18.6 27.7C19.1 27.2 19.1 26.5 18.6 26.1L16.8 24.3C16.4 23.9 15.6 23.9 15.2 24.3Z" fill="url(#paint1_linear)"/>
<mask id="mask0" mask-type="alpha" maskUnits="userSpaceOnUse" x="6" y="24" width="13" height="13">
<path d="M15.2 24.3L6.39999 33.1C5.89999 33.6 5.89999 34.3 6.39999 34.7L8.19999 36.5C8.69999 37 9.4 37 9.8 36.5L18.6 27.7C19.1 27.2 19.1 26.5 18.6 26.1L16.8 24.3C16.4 23.9 15.6 23.9 15.2 24.3Z" fill="url(#paint2_linear)"/>
</mask>
<g mask="url(#mask0)">
<g filter="url(#filter1_dd)">
<path d="M9.8 17.3L18.6 26.1C19.1 26.6 19.1 27.3 18.6 27.7L16.8 29.5C16.3 30 15.6 30 15.2 29.5L6.39999 20.7C5.89999 20.2 5.89999 19.5 6.39999 19.1L8.19999 17.3C8.59999 16.9 9.4 16.9 9.8 17.3Z" fill="url(#paint3_linear)"/>
</g>
</g>
<path d="M9.8 17.3L18.6 26.1C19.1 26.6 19.1 27.3 18.6 27.7L16.8 29.5C16.3 30 15.6 30 15.2 29.5L6.39999 20.7C5.89999 20.2 5.89999 19.5 6.39999 19.1L8.19999 17.3C8.59999 16.9 9.4 16.9 9.8 17.3Z" fill="url(#paint4_linear)"/>
</g>
<g filter="url(#filter2_dd)">
<path d="M40 32H24C23.4 32 23 32.4 23 33V36C23 36.6 23.4 37 24 37H40C40.6 37 41 36.6 41 36V33C41 32.4 40.6 32 40 32Z" fill="url(#paint5_linear)"/>
</g>
<defs>
<filter id="filter0_dd" x="3.02499" y="15" width="18.95" height="25.875" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="0.5"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="1.5"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.2 0"/>
<feBlend mode="normal" in2="effect1_dropShadow" result="effect2_dropShadow"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow" result="shape"/>
</filter>
<filter id="filter1_dd" x="3.02499" y="15" width="18.95" height="18.875" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="0.5"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="1.5"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.2 0"/>
<feBlend mode="normal" in2="effect1_dropShadow" result="effect2_dropShadow"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow" result="shape"/>
</filter>
<filter id="filter2_dd" x="20" y="30" width="24" height="11" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="0.5"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="1.5"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.2 0"/>
<feBlend mode="normal" in2="effect1_dropShadow" result="effect2_dropShadow"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow" result="shape"/>
</filter>
<linearGradient id="paint0_linear" x1="36.4462" y1="47.8257" x2="11.8217" y2="5.1748" gradientUnits="userSpaceOnUse">
<stop stop-color="#333333"/>
<stop offset="1" stop-color="#4D4D4D"/>
</linearGradient>
<linearGradient id="paint1_linear" x1="14.5276" y1="33.9959" x2="10.4841" y2="26.9924" gradientUnits="userSpaceOnUse">
<stop stop-color="#999999"/>
<stop offset="1" stop-color="#B3B3B3"/>
</linearGradient>
<linearGradient id="paint2_linear" x1="14.5276" y1="33.9959" x2="10.4841" y2="26.9924" gradientUnits="userSpaceOnUse">
<stop stop-color="#999999"/>
<stop offset="1" stop-color="#B3B3B3"/>
</linearGradient>
<linearGradient id="paint3_linear" x1="16.2747" y1="30.0336" x2="8.73699" y2="16.9781" gradientUnits="userSpaceOnUse">
<stop stop-color="#CCCCCC"/>
<stop offset="1" stop-color="#E6E6E6"/>
</linearGradient>
<linearGradient id="paint4_linear" x1="16.2747" y1="30.0336" x2="8.73699" y2="16.9781" gradientUnits="userSpaceOnUse">
<stop stop-color="#CCCCCC"/>
<stop offset="1" stop-color="#E6E6E6"/>
</linearGradient>
<linearGradient id="paint5_linear" x1="35.1496" y1="39.9553" x2="28.8504" y2="29.0447" gradientUnits="userSpaceOnUse">
<stop stop-color="#CCCCCC"/>
<stop offset="1" stop-color="#E6E6E6"/>
</linearGradient>
</defs>
</svg>
