:root {

   /* FONT STACKS ////////////////////////////////////////////////////////////////*/
   /*/////////////////////////////////////////////////////////////////////////////*/

   --font-stack-helvnow: "Helvetica Now Text", Helvetica, "Helvetica LT Pro", "SF Pro Text", Arial, sans-serif;
   --font-stack-helvnowmed: "Helvetica Now Text Medium", "Helvetica Now Text", Helvetica, "Helvetica LT Pro", "SF Pro Text", Arial, sans-serif;
   --font-stack-inter: Inter, Roboto, "Helvetica Now Text", Helvetica, Arial, sans-serif;
   --font-stack-roboto: Roboto, "Roboto Flex", "Helvetica LT Pro", Helvetica, "SF Pro Text", Arial, sans-serif;
   --font-stack-helv: "Helvetica LT Pro", "Helvetica", "SF Pro Text", "Arial", sans-serif;
   --font-stack-sfpro: "SF Pro Display", "Helvetica Now Text", "Helvetica LT Pro", "Arial", sans-serif;
   --font-stack-robotomono: "Roboto Mono", "SF Mono", "Input Mono", Input, monospace;
   --font-stack-inputmono: "Input Mono", Input, "Roboto Mono", "SF Mono", monospace;
   --font-stack-inputmonomedium: "Input Mono Medium", "Input Mono", Input, "Roboto Mono", "SF Mono", monospace;
   --font-stack-sfmono: "SF Mono", "Input Mono", Input, "Roboto Mono", "SF Mono", monospace;

   --codemirror-font: var(--font-stack-robotomono);
   --codemirror-font-headers: var(--font-stack-helvnow);

   --ui-primary-text-color: #0E1013;



   --toptoolbar-background: #FFFFFF;
   --toptoolbar-icon-color: #70767a;
   --toptoolbar-icon-disabled-opacity: 0.6;
   --toptoolbar-mdtoggle-br: 4px;

   --mdtoolbar-toggle-border-l: var(--toptoolbar-mdtoggle-br) 0px 0px var(--toptoolbar-mdtoggle-br);
   --mdtoolbar-toggle-border-r: 0px var(--toptoolbar-mdtoggle-br) var(--toptoolbar-mdtoggle-br) 0px;

   --top-right-icon-color: #3C4043;
   --top-right-icon-disabled-opacity: 0.35;


   --toolbar-default-border: 1px solid transparent;



   --dark-toolbar-background-color: #1a1d1f;
   --dark-toolbar-button-icon-color: #FFFFFF;
   --dark-toolbar-button-icon-color-hover: #d1d1d1;
   --dark-toolbar-button-border-hover-width: 2px;
   --dark-toolbar-button-border-radius: 3px;
   --dark-toolbar-button-border-color: transparent;
   --dark-toolbar-button-border-hover-color: #727880;
   --dark-toolbar-button-border-hover: var(--dark-toolbar-button-border-hover-width) solid var(--dark-toolbar-button-border-hover-color);
   --dark-toolbar-button-cursor-hover: pointer;
   --dark-toolbar-button-background-color-focus: #868c93;
   --dark-toolbar-buttonsplit-border-color: #494d52;
   --dark-toolbar-buttonsplit-border-color-hover: #727880;
   --dark-toolbar-buttonsplit-border: var(--dark-toolbar-button-border-hover-width) solid var(--dark-toolbar-buttonsplit-border-color);
   --dark-toolbar-buttonsplit-background-color: var(--dark-toolbar-background-color);
   --dark-toolbar-buttonsplit-background-color-hover: var(--dark-toolbar-background-color);
   --dark-toolbar-buttonsplit-padding: 0px;
   --dark-toolbar-buttonsplit-margin: 0px 4px 0px 4px;
   --dark-toolbar-buttonsplit-width-default: 26px;
   --dark-toolbar-buttonsplit-colorchange-width: 28px;
   --dark-toolbar-buttonsplit-chevron-width: 22px;
   --dark-toolbar-buttonsplit-height: 26px;
   --dark-toolbar-buttonsplit-colorchange-icon-margin-bottom-adjustment: 4.3px;

   --dark-toolbar-button-group-padding: 8px;
   --dark-toolbar-button-disabled-opacity: .43;
   --dark-toolbar-separator-color: #4f4e4e;





}

html body.mce-content-body.jop-tinymce {
   div#rendered-md {
      margin-left: 5px !important;
      padding-left: 5px !important;
   }
}

/* MARKDOWN EDITOR STYLES /////////////////////////////////////////////////////*/
/*/////////////////////////////////////////////////////////////////////////////*/
/*/////////////////////////////////////////////////////////////////////////////*/

.cm-header-1 {
   margin-bottom: 15px !important;
   margin-top: 10px !important;
}

.cm-comment cm-jn-monospace {
   font-family: var(--font-stack-inputmono) !important;
}

div.CodeMirror div.cm-jn-code-block-background {
   background-color: rgb(247 247 247);
}

cm-comment cm-jn-monospace {
   color: rgb(52 52 52);
   padding-left: 8px !important;
}

cm-jn-inline-code {
   color: rgb(52 52 52);
   padding-left: 0px !important;
}

div.CodeMirror span.cm-comment {
   color: rgb(52 52 52);
}

div.CodeMirror span.cm-comment.cm-jn-inline-code:not(.cm-search-marker):not(.cm-fat-cursor-mark):not(.cm-search-marker-selected):not(.CodeMirror-selectedtext) {
   border: 1px solid rgb(220, 220, 220) !important;
   background-color: rgb(243, 243, 243) !important;
   margin-left: 0px !important;
   margin-right: 0px !important;
   border-radius: 0.25em !important;
}

div.CodeMirror span.cm-comment.cm-jn-inline-code:not(.cm-search-marker):not(.cm-fat-cursor-mark):not(.cm-search-marker-selected):not(.CodeMirror-selectedtext) {
   font-size: 13px !important;
   font-family: var(--codemirror-font) !important;
   padding-bottom: 1px !important;
   color: #343434 !important;
}

.cm-s-default .cm-link {
   color: #3462e3;
}

span .cm-jn-monospace {
   font-family: var(--codemirror-font) !important;
   font-size: 14px !important;
}

.codeMirrorEditor {
   text-align: left !important;
}

.CodeMirror-scroll .CodeMirror-sizer {
   margin-left: 0 !important;
}

.CodeMirror-gutters {
   border-right: 1px solid #efefef;
   background-color: #fbfbfb;
   white-space: nowrap;
   padding-right: 10px;
   left: 0px !important;
   color: #fff;
}

.resizableLayoutItem rli-noteList {
   position: relative;
   user-select: auto;
   display: flex;
   flex-direction: column;
   width: 250px;
   height: 1357px;
   min-width: 40px;
   min-height: 40px;
   box-sizing: border-box;
   flex-shrink: 0;
}

.CodeMirror-gutter-wrapper>.CodeMirror-linenumber {
   background-color: #FBFBFB !important;
   color: #a5a1a1 !important;
   font-family: "Input Mono" !important;
   font-size: 14px !important;
}



pre.CodeMirror-line span .cm-jn-monospace {
   font-family: Input Mono !important;
   font-size: 13px !important;
   color: #6d6d6d !important;
}

.CodeMirror {
   font-family: var(--codemirror-font) !important;
   font-size: 15px;
   height: 100% !important;
   width: 100% !important;
   color: inherit !important;
   background-color: inherit !important;
   position: absolute !important;
   box-shadow: none !important;
   -webkit-box-shadow: none !important;
   line-height: 1.6em !important;
   left: 0px !important;
   top: -10px !important;
}

.CodeMirror pre.CodeMirror-line,
.CodeMirror pre.CodeMirror-line-like {
   padding-left: 19px !important;
}


.note-editor-wrapper {

   .CodeMirror *,
   .cm-editor .cm-content {
      font-family: var(--font-stack-sfmono) !important;
      font-family: var(--font-stack-inputmono) !important;
      font-size: 13px !important;
      line-height: 23px !important;
      margin-left: 0 !important;
      /* letter-spacing: -.1px; */
   }

   div.CodeMirror {

      div.cm-scroller {

         /* CONTENT CONTAINER FOR ALL LINES ///////////////////////////////////////////////////*/
         div.cm-content {

            font-family: var(--font-stack-inputmono) !important;
            font-size: 13px !important;
            line-height: 23px !important;
            margin-left: 0 !important;

            /* Link //////////////////////////////////////////////////////////////////////////*/

            .cm-url, .tok-url {
               color: #426bdb !important;
               opacity: 1 !important;
            }

            .tok-link, .ͼ1b.ͼ1d.tok-link {
               &:not(.tok-url) {
                  color: #434852 !important;
                  opacity: 1 !important;
               }
            }

            .ͼ18 .tok-url.tok-link, .ͼ18 .tok-link.tok-meta, .ͼ18 .tok-link.tok-string {
               opacity: 1 !important;
            }

            div.cm-headerLine.cm-header {
               span {
                  font-size: 14px !important;
                  font-family: var(--font-stack-sfmono) !important;
                  font-weight: 600 !important;
               }
            }

            .cm-inlineCode {
               border: none !important;
               background-color: #F8F8F8 !important;
               padding: 2px !important;
            }

            div.cm-blockQuote {
               border: none !important;
               color: #6F7378 !important;
               opacity: 1 !important;
            }

            div.cm-codeBlock {
               font-family: var(--font-stack-inputmono) !important;
               padding-left: 10px !important;
               border: none !important;
               background-color: #f9f9f9 !important;

               &.cm-regionFirstLine {
                  padding-top: 6px !important;
               }

               &.cm-regionLastLine {
                  padding-bottom: 6px !important;
               }
            }
         }
      }
   }
}



.title-input {
   font-family: var(--font-stack-helv) !important;
   font-size: 19px !important;
}




::-webkit-scrollbar {
   width: 10px !important;
   height: 10px !important;
}





/* TOOLBAR TWEAKS /////////////////////////////////////////////////////////////*/
/*/////////////////////////////////////////////////////////////////////////////*/
/*/////////////////////////////////////////////////////////////////////////////*/


span.tox-icon.tox-tbtn__icon-wrap i.fa-calendar {
   color: #FFFFFF !important;
}

.tox-toolbar__group button.tox-tbtn .tox-icon i.fa-calendar {
   color: #FFFFFF !important;
}

.plugin-icon.fas.fa-calendar {
   color: #FFF !important;
}

button.button.toolbar-button[title^='Horizontal'] i {
   color: #FFF !important;
   scale: 80% !important;
}

.toolbar-button span.sc-eldPxv.eFjEVa.icon-add-date {
   scale: 125% !important;
}

.toolbar-button span.sc-eldPxv.eFjEVa.icon-attachment {
   scale: 116% !important;
}

.toolbar-button span.sc-eldPxv.eFjEVa.icon-link {
   scale: 120% !important;
}

.toolbar-button span.sc-eldPxv.eFjEVa.icon-bulleted-list {
   scale: 114% !important;
}

.toolbar-button span.sc-eldPxv.eFjEVa.icon-numbered-list {
   scale: 114% !important;
}

.toolbar-button span.sc-eldPxv.eFjEVa.icon-to-do-list {
   scale: 114% !important;
}

.toolbar-button span.sc-eldPxv.eFjEVa.icon-heading {
   scale: 108% !important;
   position: relative;
   top: .05rem;
}

.toolbar-button i.sc-eDPEul.eVkTyI.fas.fa-calendar {
   scale: 82% !important;
   color: #FFF !important;
}

.toolbar-button span.sc-eldPxv.eFjEVa.btnh4 {
   color: #FFF !important;
   opacity: 100% !important;
   visibility: visible;
   background-color: #FFF !important;
   cursor: grab !important;
   overflow: visible !important;
   pointer-events: all !important;
   margin: 11px !important;
}

.toolbar-button span.sc-eldPxv.eFjEVa.icon-back {
   color: #FFF !important;
   opacity: 100% !important;
}

.toolbar-button span.sc-eldPxv.eFjEVa.icon-share {
   scale: 120% !important;
}




.sc-fPXMVe div.sc-gFqAkR.diFMPN {
   margin-right: 80px !important;
}


.group button.toolbar-button[title^='Toggle editor layout'] {
   display: inline-flex;
   position: relative;
   /* left: -6px !important; */
   /* margin-left: 8px !important; */
}

.group button.toolbar-button[title^='Note properties'] {
   display: inline-flex;
   position: relative;
   /* left: -6px !important; */
   /* margin-left: 8px !important; */
   scale: 120% !important;
}

.sc-gFqAkR .editor-toolbar .group .toolbar-button:not(:disabled) span.sc-eldPxv.eFjEVa.icon-layout {
   scale: 140% !important;
   color: #646b82 !important;
}

.sc-gFqAkR .editor-toolbar .group .toolbar-button:not(:disabled) span.sc-eldPxv.eFjEVa.icon-info {
   scale: 140% !important;
   color: #646b82 !important;
}

.sc-gFqAkR .editor-toolbar .group .toolbar-button:not(:disabled) span.sc-eldPxv.eFjEVa.icon-alarm {
   scale: 140% !important;
   color: #738598 !important;
}

.sc-gFqAkR .editor-toolbar .group .toolbar-button:is(:disabled) span.sc-eldPxv.eFjEVa.icon-alarm {
   color: #646b82 !important;
   scale: 140% !important;
}

.sc-gFqAkR .editor-toolbar .group .toolbar-button:not(:disabled) i.fa-globe {
   color: #646b82 !important;
   scale: 84% !important;
}

.sc-gFqAkR .editor-toolbar .button:not(.disabled):hover {
   background-color: #CCC !important;
}

.sc-gFqAkR .editor-toolbar .button.toolbar-button:not(.disabled):hover {
   background-color: #eef0f4 !important;
   border: 1px solid #d5d5d5 !important;
}

.sc-gFqAkR .editor-toolbar .group button.toolbar-button {
   margin-right: 2px !important;
}

.editor-toolbar .group button:disabled {
   cursor: default !important
}

.sc-gFqAkR .editor-toolbar .group {
   min-width: unset !important;
}

.sc-fPXMVe .sc-gFqAkR .editor-toolbar {
   display: inline-flex;
   justify-content: flex-end;
   align-items: center;
   flex-direction: row;
}

.sc-gFqAkR .editor-toolbar .group {
   min-width: unset !important;
}

.rli-editor .sc-cwHptR .sc-fPXMVe div.sc-gFqAkR.diFMPN {
   margin-right: 0px !important;
}

.sc-fPXMVe .sc-gFqAkR .updated-time-label {
   margin-right: 77px !important;
}

/* END TOOLBAR TWEAKS /////////////////////////////////////////////////////////////*/
/*/////////////////////////////////////////////////////////////////////////////////*/
/*/////////////////////////////////////////////////////////////////////////////////*/



.CodeMirror5 {

   * {
      font-family: var(--font-stack-roboto) !important;
      font-size: 16px !important;
   }



   .CodeMirror {

      .CodeMirror-lines {

         div {

            & div.CodeMirror-code {

               .cb-start-line,
               .cb-code-line,
               .cb-end-line {
                  display: block;
                  margin-left: 19px !important;
                  background-color: #F3F3F3 !important;
               }

               & pre {
                  .CodeMirror-line {

                     margin-left: 19px !important;

                     span {

                        font-size: 12px !important;
                        font-family: "Input Mono" !important;
                        line-height: 12px !important;

                        .cm-header {
                           font-family: var(--codemirror-font-headers) !important;
                           color: var(--ui-primary-text-color) !important;
                        }

                        .cm-header-1 {
                           font-size: 24px !important;
                           letter-spacing: .5px !important;
                        }

                        .cm-header-2 {
                           font-size: 19px !important;
                           letter-spacing: .5px !important;
                        }

                        .cm-header-3 {
                           font-size: 16px !important;
                           letter-spacing: .2px;
                        }

                        .cm-header-4 {
                           font-size: 14.5px !important;
                           letter-spacing: .1px;
                        }

                        .cm-header-5 {
                           font-size: 13.5px !important;
                           letter-spacing: .1px !important;
                        }

                        .cm-header-6 {
                           font-size: 21px !important;
                        }


                     }
                  }
               }
            }
         }
      }
   }
}



.CodeMirror-code div pre.CodeMirror-line span .cm-comment.cm-jn-inline-code:not(.cm-search-marker):not(.cm-fat-cursor-mark):not(.cm-search-marker-selected):not(.CodeMirror-selectedtext) {
   font-size: 12px !important
}

.CodeMirror-code div pre.CodeMirror-line {
   line-height: 25px !important;
}

.CodeMirror-code div pre.cm-jn-code-block {
   line-height: 22px !important;
}

.codeMirrorEditor div.CodeMirror span.cm-comment.cm-jn-inline-code:not(.cm-search-marker):not(.cm-fat-cursor-mark):not(.cm-search-marker-selected):not(.CodeMirror-selectedtext) {
   vertical-align: middle !important;
}




html div.rli-editor {

   div.note-editor-wrapper {

      /* TOP BAR ///////////////////////////////////////////////////////////////////////////////////*/
      div.note-title-wrapper {

         div.note-title-info-group {

            position: relative !important;
            top: -2px !important;

            span.updated-time-label {
               margin-right: 12px !important;
               font-family: var(--font-stack-roboto) !important;
               font-size: 12px !important;
               line-height: 1.6em !important;
               color: rgb(98, 113, 132) !important;
               padding-left: 8px !important;
               white-space: nowrap !important;

            }

            div.editor-toolbar {

               flex: 1.5 1 0% !important;
               background-color: var(--toptoolbar-background) !important;
               font-size: 15px !important;
               color: #ffffff !important;
               margin-right: 0px !important;
               padding: 0px !important;
               font-family: var(--font-stack-roboto) !important;

               div.group {
                  display: inline-flex !important;
                  margin-top: 0;
                  margin-bottom: 0;
                  flex-direction: row;
                  box-sizing: border-box;
                  min-width: 0;

                  button.toolbar-button {

                     scale: 120% !important;
                     opacity: 1 !important;
                     padding: 0px 5px 0px 5px !important;
                     color: #6e7175 !important;
                     cursor: pointer !important;
                     border: 1px solid rgb(0 0 0 / 0%) !important;
                     background: rgb(0 0 0 / 0%) !important;
                     box-sizing: border-box !important;
                     width: 26px;
                     display: inline-flex;
                     align-items: center;
                     flex-direction: row;

                     i.toolbar-icon {
                        font-size: 16px !important;
                     }

                     &[title="Spell checker"] i.toolbar-icon {
                        font-size: 13px !important;
                        position: relative !important;
                        top: 1px !important;
                     }

                     &.disabled {
                        opacity: .4 !important;
                        cursor: default !important;

                        &:hover {
                           border: none !important;
                        }
                     }

                     &:not(.disabled) {
                        &:hover {
                           border-color: #d0d0d0 !important;
                        }
                     }
                  }

               }
            }
         }
      }

      /* BOTTOM BAR (EDIT MODE) ////////////////////////////////////////////////////////////////*/
      div.note-title-wrapper+div {
         div.editor-toolbar {
            font-family: var(--font-stack-roboto) !important;
            display: flex !important;
            flex-direction: row !important;
            box-sizing: border-box !important;
            background-color: var(--dark-toolbar-background-color) !important;
            font-size: 15px !important;
            color: rgb(255, 255, 255) !important;
            margin-right: 0px !important;
            flex: 1 0 0% !important;
            padding: 0px !important;

            div.group {

               button.toolbar-button {

                  background-color: transparent !important;
                  border: var(--toolbar-default-border) !important;
                  cursor: default;

                  span.toolbar-icon {
                     color: #ffffff !important;
                     font-size: 16px !important;
                  }

                  i.toolbar-icon {
                     color: #ffffff !important;
                     font-size: 16px !important;
                     scale: 80% !important;
                  }

                  &.disabled {
                     opacity: .47 !important;

                     &:hover {
                        background-color: transparent !important;
                        border: none !important;
                        cursor: default;
                     }

                     span.toolbar-icon {
                        &:hover {
                           border: none !important;
                        }
                     }

                     span[class*="btnh"] {
                        font-family: var(--font-stack-helvnow) !important;
                        position: relative !important;
                        font-weight: 500 !important;
                        font-size: 12px !important;
                     }

                     span.btnh4 {
                        &::before {
                           content: "H4";
                        }
                     }

                     span.btnh5 {
                        &::before {
                           content: "H5";
                        }
                     }

                     span.btnh6 {
                        &::before {
                           content: "H6";
                        }
                     }
                  }

                  &:not(.disabled) {
                     &:hover {
                        cursor: var(--dark-toolbar-button-cursor-hover) !important;
                        border: var(--dark-toolbar-button-border-hover) !important;
                     }
                  }
               }
            }
         }
      }

   }
}

/* TOGGLE EDITORS BUTTON /////////////////////////////////////////////////////////////////////*/

button[title="Toggle editors"]>div {
   width: 27px !important;
   height: 18px !important;
   border: 2px solid !important;
}

button[title="Toggle editors"].richText-active {

   >div {

      &:nth-child(1) {

         border-radius: var(--mdtoolbar-toggle-border-l) !important;
         background-color: #000000 !important;
         border-color: #696e72 !important;
         border-right: none !important;
         opacity: .8 !important;

         &:hover {
            border-color: #666b6f !important;
            opacity: 1 !important;

            .fas {
               color: #ced5db !important;
            }
         }
      }

      &:nth-child(2) {
         border-radius: var(--mdtoolbar-toggle-border-r) !important;
         background-color: #738598 !important;
         border-color: #738598 !important;
      }

      & i {

         position: relative !important;
         top: 2px !important;

         &.fab {
            font-size: 15px !important;
            color: #a6b0b9 !important;
         }

         &.fas {
            font-size: 13px !important;
            left: 1px;
            color: #f4f5f6 !important;
         }
      }
   }
}


button[title="Toggle editors"].markdown-active {

   /* i.fa-markdown {
        font-size: 15px;
        position: relative;
        top: 2px !important;
        color: rgb(244, 245, 246);
    } */

   >div {

      &:nth-child(1) {
         border-radius: var(--mdtoolbar-toggle-border-l) !important;
         background-color: #738598 !important;
         border-color: #738598 !important;
      }

      &:nth-child(2) {

         border-radius: var(--mdtoolbar-toggle-border-r) !important;
         background-color: #000000 !important;
         border-color: #696e72 !important;
         border-left: none !important;
         opacity: .8 !important;

         &:hover {
            border-color: #666b6f !important;
            opacity: 1 !important;

            .fas {
               color: #ced5db !important;
            }
         }
      }

      & i {

         position: relative !important;
         top: 2px !important;

         &.fab {
            font-size: 15px !important;
            color: #f4f5f6 !important;
         }

         &.fas {
            font-size: 13px !important;
            left: 1px;
            color: #a6b0b9 !important;
         }
      }
   }
}

/* BLACK TOOLBAR /////////////////////////////////////////////////////////////////////////////////*/


.joplin-tinymce {

   div:nth-child(1) button.toolbar-button {

      background-color: transparent !important;
      border: transparent !important;
      cursor: default;

      &.disabled {
         opacity: .43 !important;
         span.toolbar-icon {
            color: #ffffff !important;
            font-size: 16px !important;
            position: relative;
            top: .5px !important;
            &:hover {
               border: none !important;
            }
         }
         &:hover {
            background-color: transparent !important;
            border: none !important;
            cursor: default;
         }
      }
      &:not(.disabled) {
         span.toolbar-icon {
            color: #ffffff !important;
            font-size: 17px !important;
         }
         &:hover {
            cursor: var(--dark-toolbar-button-cursor-hover) !important;
            border: var(--dark-toolbar-button-border-hover) !important;
         }
      }
   }


   .tox-editor-container .tox-editor-header .tox-toolbar-overlord {
      .tox-toolbar__primary {
         .tox-toolbar__group button[title="Insert"] {
            span.tox-icon {
               position: relative;
               top: 1px;
            }
         }
         div.tox-toolbar__group button[title="Quick HTML tag"] {
            color: #FFF !important;
            span.tox-icon {
               scale: .77;
               position: relative;
               top: .5px;
               right: -.4px !important;
            }
         }
         div.tox-toolbar__group button[title="Creates a button."] {
            color: #FFF !important;
            span.tox-icon {
               scale: .77;
               position: relative;
               top: .5px;
               right: -.4px !important;
            }
         }
      }
   }

   .tox-toolbar__primary {

      .tox-toolbar__group {

         button.tox-tbtn {
            padding-bottom: 0 !important;
            width: 26px;
            height: 26px;
            min-height: 26px;
            margin: 0;
            box-shadow: none !important;
            box-sizing: border-box !important;
            align-items: center;
            background: 0 0;
            border: 0;
            border-radius: 3px;
            display: flex;
            flex: 0 0 auto;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            justify-content: center;
            outline: 0;
            overflow: hidden;
            padding: 0;
            text-transform: none;
            margin-right: 2px !important;

            &:hover {
               cursor: var(--dark-toolbar-button-cursor-hover) !important;
               background-color: transparent !important;
               border: var(--dark-toolbar-button-border-hover) !important;
               span svg {
                  color: var(--dark-toolbar-button-icon-color-hover) !important;
                  fill: var(--dark-toolbar-button-icon-color-hover) !important;
               }
            }

            span svg {
               color: var(--dark-toolbar-button-icon-color) !important;
               fill: var(--dark-toolbar-button-icon-color) !important;
            }

            div.tox-tbtn__select-chevron {
               svg {
                  color: var(--dark-toolbar-button-icon-color) !important;
                  fill: var(--dark-toolbar-button-icon-color) !important;
               }
            }
         }

         div.tox-split-button {

            background-color: var(--dark-toolbar-buttonsplit-background-color) !important;
            box-sizing: border-box;
            height: var(--dark-toolbar-buttonsplit-height);
            border: none !important;
            margin: var(--dark-toolbar-buttonsplit-margin) !important;

            span.tox-icon.tox-tbtn__icon-wrap {
               margin-bottom: var(--dark-toolbar-buttonsplit-colorchange-icon-margin-bottom-adjustment);
            }

            span.tox-tbtn {
               padding-bottom: 0px !important;
               width: var(--dark-toolbar-buttonsplit-width-default) !important;
               height: var(--dark-toolbar-buttonsplit-height) !important;
               min-height: var(--dark-toolbar-buttonsplit-height) !important;
               box-shadow: none !important;
               box-sizing: border-box !important;
            }

            span.tox-tbtn:not(.tox-split-button__chevron) {
               border-left: var(--dark-toolbar-buttonsplit-border) !important;
               border-top: var(--dark-toolbar-buttonsplit-border) !important;
               border-top-left-radius: var(--dark-toolbar-button-border-radius) !important;
               border-bottom-left-radius: var(--dark-toolbar-button-border-radius) !important;
               border-bottom: var(--dark-toolbar-buttonsplit-border) !important;
               border-right: 1px solid var(--dark-toolbar-buttonsplit-border-color) !important;
               width: var(--dark-toolbar-buttonsplit-colorchange-width) !important;

               svg {
                  color: var(--dark-toolbar-button-icon-color) !important;
                  fill: var(--dark-toolbar-button-icon-color) !important;
               }

               &:hover {
                  background-color: var(--dark-toolbar-buttonsplit-background-color-hover) !important;
                  border-color: var(--dark-toolbar-button-border-hover-color) !important;
                  cursor: var(--dark-toolbar-button-cursor-hover) !important;
                  svg {
                     color: var(--dark-toolbar-button-icon-color-hover) !important;
                     fill: var(--dark-toolbar-button-icon-color-hover) !important;
                  }
               }
            }

            span.tox-split-button__chevron {
               border-left: 1px solid var(--dark-toolbar-buttonsplit-border-color) !important;
               border-top: var(--dark-toolbar-buttonsplit-border) !important;
               border-right: var(--dark-toolbar-buttonsplit-border) !important;
               border-bottom: var(--dark-toolbar-buttonsplit-border) !important;
               border-top-right-radius: var(--dark-toolbar-button-border-radius) !important;
               border-bottom-right-radius: var(--dark-toolbar-button-border-radius) !important;
               width: var(--dark-toolbar-buttonsplit-chevron-width) !important;

               svg {
                  color: var(--dark-toolbar-button-icon-color) !important;
                  fill: var(--dark-toolbar-button-icon-color) !important;
               }

               &:hover {
                  background-color: var(--dark-toolbar-buttonsplit-background-color-hover) !important;
                  border-color: var(--dark-toolbar-buttonsplit-border-color-hover) !important;
                  cursor: var(--dark-toolbar-button-cursor-hover) !important;

                  svg {
                     color: var(--dark-toolbar-button-icon-color-hover) !important;
                     fill: var(--dark-toolbar-button-icon-color-hover) !important;
                  }

               }

            }
         }
      }
   }

   div.tox-toolbar__primary button[title="Heading 1"],
   div.tox-toolbar__primary button[title="Heading 2"],
   div.tox-toolbar__primary button[title="Heading 3"],
   div.tox-toolbar__primary button[title="Heading 4"],
   div.tox-toolbar__primary button[title="Heading 5"],
   div.tox-toolbar__primary button[title="Heading 6"] {
      width: 28px !important;
   }
   path.tox-icon-text-color__color {
      fill: #FFF !important;
   }

   /* div.tox-tinymce {
      div.tox-editor-container {
         div.tox-editor-header {
            div.tox-toolbar-overlord {
               div.tox-toolbar__primary {
                  div.tox-toolbar__group {

                     button.tox-tbtn {

                        padding-bottom: 1px !important;
                        width: 26px !important;
                        height: 26px;
                        min-height: 26px;
                        margin: 0;
                        box-shadow: none !important;
                        box-sizing: border-box !important;
                        align-items: center;
                        background: 0 0;
                        border: 0;
                        border-radius: 3px;
                        display: flex;
                        flex: 0 0 auto;
                        font-size: 14px;
                        font-style: normal;
                        font-weight: 400;
                        justify-content: center;
                        outline: 0;
                        overflow: hidden;
                        padding: 0;
                        text-transform: none;


                        &:hover {
                           cursor: var(--dark-toolbar-button-cursor-hover) !important;
                           background-color: transparent !important;
                           border: var(--dark-toolbar-button-border-hover) !important;
                           box-shadow: none !important;

                           span svg {
                              color: var(--dark-toolbar-button-icon-color-hover) !important;
                              fill: var(--dark-toolbar-button-icon-color-hover) !important;
                           }

                        }



                        span svg {
                           color: var(--dark-toolbar-button-icon-color) !important;
                           fill: var(--dark-toolbar-button-icon-color) !important;
                        }



                     }
                  }
               }
            }
         }
      }
   } */
}

.editor-toolbar {

   >div {

      margin-top: 6px;
      margin-bottom: 6px;

      &:first-child {
         margin-left: 5px;
      }

      &:last-child {
         margin-right: 9px;
      }

      &:nth-child(2) {
         & a[title="Italic"]:not(.disabled) span.icon-italic {
            margin-right: 1px !important;
         }

         a[title="Hyperlink"]:not(.disabled) span.icon-link {
            font-size: 19px;
            position: relative;
            top: 1px;
         }

         a[title="Bulleted List"]:not(.disabled),
         a[title="Numbered List"]:not(.disabled),
         a[title="Checkbox"]:not(.disabled) {

            span.icon-bulleted-list,
            span.icon-numbered-list,
            span.icon-to-do-list {
               font-size: 18.5px;
               position: relative;
               right: 1px;
            }
         }

         a[title="Heading 4"] span,
         a[title="Heading 5"] span,
         a[title="Heading 6"] span {
            font-family: 'icomoon';

            ::before {
               content: "\e903";
            }
         }
      }

   }
}


.sc-ikkxIA .editor-toolbar {
   & div {

      /* Fix top right button spacing */
      &:nth-child(2) {
         min-width: unset !important;
      }

      .button {

         &.disabled {
            opacity: var(--top-right-icon-disabled-opacity) !important;
         }

         &:not(.disabled):hover {
            background: none !important;
            border: 1px solid #CCC !important;
            border-radius: 3px !important;

            & span, i {
               color: #969696 !important;
            }
         }

      }
   }
}




/* RTE EDITOR TOOLBAR /////////////////////////////////////////////////////////////*/
/*/////////////////////////////////////////////////////////////////////////////////*/

.joplin-tinymce {

   & a {
      &.button:not(.disabled) {
         &:hover {
            background: #181818 !important;
            box-sizing: border-box !important;
            border: 1px solid #636970 !important;
            box-shadow: none !important;
         }
      }
   }

   >div {
      &:nth-child(1) {
         background-color: var(--dark-toolbar-background-color) !important;
         height: 27px !important;
      }

      &:nth-child(2) {
         box-sizing: content-box !important;
         background-color: rgb(244, 245, 246) !important;
         display: flex !important;
         flex-direction: row !important;
         position: absolute !important;
         height: 27px !important;
         z-index: 2 !important;
         top: 0px !important;
         padding: 6px 8px 6px 6px !important;
         align-items: center !important;
         justify-content: flex-end !important;
         width: 70px !important;
      }
   }
}

/* div.joplin-tinymce .tox .tox-toolbar__group {
   border: unset !important;
   border: none;
} */



/* .tox:not([dir=rtl]) .tox-toolbar__group:not(:last-of-type) {
   padding-right: 10px !important;
    padding-left: 10px !important;
    border-right: 1px solid var(--dark-toolbar-separator-color) !important;
} */

.tox .tox-toolbar__primary {
   /* Add separators between menu groups in the black toolbar. ///////////////////////////////////*/
   /*/////////////////////////////////////////////////////////////////////////////////////////////*/

   .tox-toolbar__group {

      padding-right: var(--dark-toolbar-button-group-padding) !important;
      padding-left: var(--dark-toolbar-button-group-padding) !important;
      border-right: 1px solid var(--dark-toolbar-separator-color) !important;

      &:first-of-type {
         border-left: 1px solid var(--dark-toolbar-separator-color) !important;
      }
      &:last-of-type {
         padding-right: 0 !important;
         border-right: 1px solid transparent !important;
      }

      .tox-tbtn {
         &:active {
            background: var(--dark-toolbar-button-background-color-focus) !important;
            /* border: 2px solid #c7c3c3 !important;
            color: #222f3e !important; */
         }

         &:focus {
            background: var(--dark-toolbar-button-background-color-focus) !important;
         }
      }

      button[title="Heading 1"],
      button[title="Heading 2"],
      button[title="Heading 3"],
      button[title="Heading 4"],
      button[title="Heading 5"],
      button[title="Heading 6"] {
         span, i {
            color: #ffffff !important;
            fill: #ffffff !important;
            font-family: "SF Pro", "Helvetica Now Text", "Roboto", "Inter", "Arial";
            position: relative;
            font-weight: 500;
            font-size: 15.5px;
            text-overflow: initial;
         }
      }
   }
}

div.resizableLayoutItem {
   div.note-editor-wrapper {

      /* Black toolbar in markdown editor mode ////////////////////////////////////////////////*/
      /*///////////////////////////////////////////////////////////////////////////////////////*/

      /* Remove H4 H5 and H6 //////////////////////////////////////////////////////////////////*/
      div#CodeMirrorToolbar {

         height: 40px !important;

         .group {

            button[Title="Heading 4"],
            button[Title="Heading 5"],
            button[Title="Heading 6"] {
               display: none !important;
            }

            span:not(.toolbar-icon) {
               flex-wrap: wrap !important;
               display: flex !important;
               margin-left: 7px !important;
               margin-right: 7px !important;
               flex-direction: row;
               align-content: center;
               justify-content: center !important;
               align-items: center !important;
            }

            button.toolbar-button {
               display: inline-flex;
               align-items: center;
               flex-direction: column;
               margin-left: 2.4px;
               margin-right: 2.4px;
               shape-rendering: crispedges;
               text-rendering: geometricPrecision;
               span.toolbar-icon {
                  scale: 1.15;
                  shape-rendering: crispedges;
                  text-rendering: geometricPrecision;

                  &.icon-bulleted-list,
                  &.icon-numbered-list,
                  &.icon-to-do-list {
                     scale: 1.19 !important;
                  }
               }
            }
         }




      }

   }
}

a.sc-fABZjn.cLQczt.button.disabled {
   opacity: 70% !important;
}

/* Top part above main toolbar */
.rli-editor>div>div>div>div>div>.editor-toolbar {
   background-color: #ffffff !important;
   font-size: 15px !important;
   color: #45505c !important;
   margin-right: 0px !important;
   /* pointer-events: none !important; */
}

.editor-toolbar>div:nth-child(1)>.jCwNNr.button {
   opacity: 0.5 !important;
   height: 26px !important;
   min-height: 26px !important;
   width: 26px !important;
   max-width: 26px !important;
   display: flex !important;
   align-items: center !important;
   justify-content: center !important;
   cursor: default !important;
   border-radius: 3px !important;
   box-sizing: border-box !important;
   color: #eff3f7 !important;
   font-size: 14.4px !important;
   padding-left: 5px !important;
   padding-right: 5px !important;
}

.editor-toolbar .gialvs {
   font-size: 18px !important;
   color: #fff !important;
   margin-right: 0px !important;
   pointer-events: none !important;
}

div.dEAQJl .editor-toolbar .bAtQDZ {
   font-size: 13px !important;
   color: #fff !important;
   margin-right: 0px !important;
   pointer-events: none !important;
}

.editor-toolbar>div:nth-child(1)>.jCwNNr.button.disabled {
   opacity: 0.5 !important;
   height: 26px !important;
   min-height: 26px !important;
   width: 26px !important;
   max-width: 26px !important;
   display: flex !important;
   align-items: center !important;
   justify-content: center !important;
   cursor: default !important;
   border-radius: 3px !important;
   box-sizing: border-box !important;
   color: #eff3f7 !important;
   font-size: 14.4px !important;
   padding-left: 5px !important;
   padding-right: 5px !important;
}



/* .editor-toolbar cYzoOR.button {
    opacity: 1 !important;
    height: 26px !important;
    min-height: 26px !important;
    width: 26px !important;
    max-width: 26px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    cursor: default !important;
    border-radius: 3px !important;
    box-sizing: border-box !important;
    color: #f1f3f5 !important;
    font-size: 14.4px !important;
    padding-left: 5px !important;
    padding-right: 5px !important;
} */


.joplin-tinymce .jCwNNr {
   opacity: .5 !important;
   height: 26px !important;
   min-height: 26px !important;
   width: 26px !important;
   max-width: 26px !important;
   display: flex !important;
   align-items: center !important;
   justify-content: center !important;
   cursor: default !important;
   border-radius: 3px !important;
   box-sizing: border-box !important;
   color: #738598 !important;
   font-size: 14.4px !important;
   padding-left: 5px !important;
   padding-right: 5px !important;
}

.joplin-tinymce .jCwNNr span.gialvs {
   font-size: 18px !important;
   color: #e8ecf0 !important;
   margin-right: 0px !important;
   pointer-events: none !important;
}

.joplin-tinymce .cYzoOR {
   opacity: 1 !important;
   height: 26px !important;
   min-height: 26px !important;
   width: 26px !important;
   max-width: 26px !important;
   display: flex !important;
   align-items: center !important;
   justify-content: center !important;
   cursor: default !important;
   border-radius: 3px !important;
   box-sizing: border-box !important;
   color: #ebedf0 !important;
   font-size: 14.4px !important;
   padding-left: 5px !important;
   padding-right: 5px !important;
}

.joplin-tinymce .cYzoOR span.gialvs {
   font-size: 18px !important;
   color: #fcfeff !important;
   margin-right: 0px !important;
   pointer-events: none !important;
}



.editor-toolbar i.bAtQDZ {
   font-size: 18px;
   color: #eef0f3;
   margin-right: 0px;
   pointer-events: none;
   font-size: 14px !important;
}

/* Top Toolbar Buttons */
div.dEAQJl .editor-toolbar .jCwNNr {
   opacity: .55 !important;
   height: 26px !important;
   min-height: 26px !important;
   width: 26px !important;
   max-width: 26px !important;
   display: flex !important;
   align-items: center !important;
   justify-content: center !important;
   cursor: default !important;
   border-radius: 3px !important;
   box-sizing: border-box !important;
   color: #738598 !important;
   font-size: 14.4px !important;
   padding-left: 5px !important;
   padding-right: 5px !important;
}

div.dEAQJl .editor-toolbar span.gialvs {
   color: var(--toptoolbar-icon-color) !important;
   font-size: 21px !important;
}

div.dEAQJl .editor-toolbar .cYzoOR.button .bAtQDZ {
   color: var(--toptoolbar-icon-color) !important;
   font-size: 16px !important;

}

div.dEAQJl .editor-toolbar .jCwNNr.button.disabled .gialvs {
   color: var(--toptoolbar-icon-color) !important;
   opacity: var(--toptoolbar-icon-disabled-opacity) !important;
   font-size: 21px !important;

}

div.sc-feUZmu.dEAQJl .editor-toolbar {
   display: flex !important;
   flex-direction: row !important;
   box-sizing: border-box !important;
   background-color: #fff !important;
   padding: 3px 12px 8px 6px !important;
   margin-bottom: 0px !important;
   border-bottom: none !important;
}

.joplin-tinymce .jCwNNr {
   opacity: 0.6 !important;

}

/* MARKDOWN EDITOR STYLING */

.joplin-tinymce .tox .tox-toolbar__primary {
   background: none;
   background-color: #161718 !important;
}

.joplin-tinymce .tox-toolbar__group {
   background-color: var(--dark-toolbar-background-color) !important;
   padding-top: 6px !important;
   padding-bottom: 6px !important;
   padding-left: 6px !important;
}


div.joplin-tinymce {
   div.tox-tinymce {
      div.tox-editor-container {
         div.tox-toolbar-overlord {
            div.tox-toolbar__primary {
               div.tox-toolbar__group {
                  button span.tox-tbtn__select-label {
                     margin: 0 !important;
                  }
               }
            }
         }
      }
   }
}

.tox .tox-tbtn--enabled,
.tox .tox-tbtn--enabled:hover {
   background-color: #757981;
}

div.tox-toolbar__group .tox-tbtn.tox-tbtn--select>.tox-tbtn__select-label {
   color: #ffffff !important;
   fill: #ffffff !important;
}

div.joplin-tinymce div a.button.disabled span.dXWRAJ {
   color: #ffffff !important;
   fill: #ffffff !important;
   opacity: 100% !important;
}

div.joplin-tinymce div a.button span.dXWRAJ {
   color: #ffffff !important;
   fill: #ffffff !important;
   opacity: 100% !important;
}

.sc-hUOJWJ.dXWRAJ.icon-share {
   margin-right: 0px !important;
}

div.tox-editor-header div.tox-toolbar-overlord div.tox-toolbar__primary {
   background-color: var(--dark-toolbar-background-color) !important;
}


div.joplin-tinymce>div:nth-child(2) {
   background-color: var(--dark-toolbar-background-color) !important;
}




.tox .tox-tbtn--enabled, .tox .tox-tbtn--enabled:hover {
   background-color: #757c85 !important;
   /* box-sizing: border-box !important;
    border: 2px solid #cfcfcf !important; */
}

.dySelf:hover {
   background: #181818 !important;
   box-sizing: border-box !important;
   border: 2px solid #7a7a7a !important;
   box-shadow: none !important;
   color: #FFFFFF !important;
}



.tox.tox-silver-sink.tox-tinymce-aux .tox-toolbar__overflow {
   position: absolute !important;
   left: 732px !important;
   top: -991px !important;
   max-height: 991px !important;
   overflow: hidden auto !important;
   max-width: 1300px !important;
   background-color: #000 !important;
   padding: 5px !important;
}


.tox.tox-silver-sink.tox-tinymce-aux .tox-toolbar__overflow .tox-toolbar__group {
   display: flex !important;
   flex-wrap: wrap !important;
   margin: 0 0 !important;
   padding: 0 4px 0 4px !important;
   align-items: center !important;
}

.tox.tox-silver-sink.tox-tinymce-aux .tox-toolbar__overflow .tox-toolbar__group button {
   width: 26px !important;
   height: 26px !important;
   min-width: 26px !important;
   min-height: 26px !important;
}


.tox-pop.tox-pop--bottom {
   position: absolute !important;
   left: 1025px !important;
   bottom: 894.312px !important;
   max-height: 89px !important;
   max-width: 1006px !important;
}

.tox-pop.tox-pop--bottom .tox-pop__dialog {
   background-color: #fff !important;
   border: 1px solid #ccc !important;
   border-radius: 3px !important;
   box-shadow: 0 1px 3px rgb(0 0 0 / 15%) !important;
   min-width: 0 !important;
   overflow: hidden !important;
   padding: 4px !important;
}


.tox-pop.tox-pop--bottom .tox-pop__dialog .tox-toolbar .tox-toolbar__group {
   align-items: center !important;
   display: flex !important;
   flex-wrap: wrap !important;
   margin: 0 0 !important;
   padding: 0 4px 0 4px !important;
}

div.tox-pop.tox-pop--bottom .tox-pop__dialog .tox-toolbar .tox-toolbar__group .tox-tbtn {
   width: 26px !important;
   height: 26px !important;
   min-width: 26px !important;
   min-height: 26px !important;
   margin: 0 !important;
}

div.tox-pop.tox-pop--bottom .tox-tbtn:hover {
   fill: #000000 !important;
}

.tox-pop__dialog .tox-toolbar .tox-toolbar__group .tox-tbtn>span.tox-icon.tox-tbtn__icon-wrap {
   transform: scale(1) !important;
}

.tox-pop.tox-pop--bottom .tox-pop__dialog .tox-toolbar .tox-toolbar__group .tox-tbtn>.tox-icon.tox-tbtn__icon-wrap svg {
   color: #a3a3a3 !important;
   fill: #a3a3a3 !important;
}


.sc-hUOJWJ.dXWRAJ.icon-share {
   margin-right: 0px !important;
}

.dySelf {
   opacity: 1 !important;
   height: 26px !important;
   min-height: 26px !important;
   width: 26px !important;
   max-width: 26px !important;
   display: flex !important;
   align-items: center !important;
   justify-content: center !important;
   cursor: default !important;
   border-radius: 3px !important;
   box-sizing: border-box !important;
   color: #738598 !important;
   padding-left: 5px !important;
   padding-right: 5px !important;
   margin-left: 0px !important;
   font-size: 0 !important;
}

.dXWRAJ {
   font-size: 16px !important;
   pointer-events: none !important;
}

.hBxXkP {
   opacity: 1 !important;
   height: 26px !important;
   min-height: 26px !important;
   width: 26px !important;
   max-width: 26px !important;
   display: flex !important;
   align-items: center !important;
   justify-content: center !important;
   cursor: default !important;
   border-radius: 3px !important;
   box-sizing: border-box !important;
   color: #738598 !important;
   padding-left: 5px !important;
   padding-right: 5px !important;
   margin-left: 0px !important;
   font-size: 0 !important;
}

.hBxXkP:hover {
   background-color: #181818;
}

.gppWZc {
   font-size: 18px !important;
   margin-right: 0px !important;
   color: #d2c6ff !important;
   pointer-events: none !important;
}

.tox-pop.tox-pop--bottom .tox-pop__dialog .tox-toolbar .tox-toolbar__group .tox-tbtn>.tox-icon.tox-tbtn__icon-wrap svg {
   color: #a2aab1 !important;
   fill: #a2aab1 !important;
}

.tox-pop__dialog div.tox-toolbar__group .tox-tbtn>.tox-icon.tox-tbtn__icon-wrap svg {
   color: #a2aab1 !important;
   fill: #a2aab1 !important;
}

.dzazep {
   color: #ffffff !important;
}

.bLUFtk:hover {
   background-color: #3a3c3e !important;
   border: 1px solid #7a8091 !important;
}

.eFjEVa {
   color: #ffffff !important;
}


div.editor-toolbar>div>span {
   padding: 0px !important;
   height: 26px !important;
   min-width: 1px !important;
   width: 1px !important;
   align-items: center !important;
   text-decoration: none !important;
   margin-left: 6px !important;
   margin-right: 6px !important;
   font-family: Roboto !important;
   font-size: 12px !important;
   box-sizing: border-box !important;
   cursor: default !important;
   justify-content: center !important;
   background: #5a616c !important;
   white-space: nowrap !important;
}


.editor-toolbar .button .eFjEVa {
   font-size: 14px !important;

}

.tox .tox-tbtn:hover {
   background: #181818 !important;
   box-sizing: border-box !important;
   border: 1px solid #636970 !important;
   box-shadow: none !important;
   color: #FFFFFF !important;
}


div.sc-ikkxIA div.editor-toolbar div .button .eFjEVa {
   color: var(--top-right-icon-color) !important;
   font-size: 16px !important;
}

div.sc-ikkxIA div.editor-toolbar div .button .dzazep {
   color: var(--top-right-icon-color) !important;
   font-size: 20px !important;
}



/* FIXING POPUP TOOLBAR                              */

div.tox-toolbar .tox-toolbar__group button.tox-tbtn {
   width: 24px !important;
   height: 24px !important;
   min-height: 20px;
   /* min-width: 26px; */
   /* margin: 0; */
   /* max-width: 24px !important; */
   /* max-height: 21px !important; */
   padding: 0 !important;
   padding-top: 0 !important;
   padding-bottom: 0 !important;
   margin-left: 2px !important;
   box-sizing: border-box !important;
   display: flex;
   align-items: center;
}

div.tox-pop .tox-pop__dialog .tox-toolbar {
   /* height: 28px !important; */
   padding-top: 5px;
   padding-bottom: 6px;
   padding-left: 0px;
   padding-right: 2px;
}

.tox-pop .tox-pop__dialog div.tox-toolbar__group .tox-tbtn>.tox-icon.tox-tbtn__icon-wrap svg, .tox-pop .tox-pop__dialog div.tox-toolbar__group .tox-tbtn>.tox-icon svg {
   fill: #a1acb1 !important;
   color: #808b91 !important;
}

div.tox-toolbar .tox-toolbar__group button.tox-tbtn:hover {
   background: #e6e8eb !important;
   box-sizing: border-box !important;
   border: 0px solid #636970 !important;
   box-shadow: none !important;
   color: #FFFFFF !important;
}

.tox-pop__dialog div.tox-toolbar .tox-toolbar__group {
   display: flex;
   flex-wrap: wrap;
   margin: 0 0;
   padding: 0 4px 0 4px;
   /* height: 26px !important; */
   align-items: center;
}