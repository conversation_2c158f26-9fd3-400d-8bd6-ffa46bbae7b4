(()=>{"use strict";var e={775:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.default=joplin},200:(e,t)=>{var o,n,i,r,a,l,u,c,s,d;Object.defineProperty(t,"__esModule",{value:!0}),t.ContentScriptType=t.SettingStorage=t.AppType=t.SettingItemSubType=t.SettingItemType=t.ToolbarButtonLocation=t.isContextMenuItemLocation=t.MenuItemLocation=t.ModelType=t.ImportModuleOutputFormat=t.FileSystemItem=void 0,(d=t.FileSystemItem||(t.FileSystemItem={})).File="file",d.Directory="directory",(s=t.ImportModuleOutputFormat||(t.ImportModuleOutputFormat={})).Markdown="md",s.Html="html",(c=t.ModelType||(t.ModelType={}))[c.Note=1]="Note",c[c.Folder=2]="Folder",c[c.Setting=3]="Setting",c[c.Resource=4]="Resource",c[c.Tag=5]="Tag",c[c.NoteTag=6]="NoteTag",c[c.Search=7]="Search",c[c.Alarm=8]="Alarm",c[c.MasterKey=9]="MasterKey",c[c.ItemChange=10]="ItemChange",c[c.NoteResource=11]="NoteResource",c[c.ResourceLocalState=12]="ResourceLocalState",c[c.Revision=13]="Revision",c[c.Migration=14]="Migration",c[c.SmartFilter=15]="SmartFilter",c[c.Command=16]="Command",function(e){e.File="file",e.Edit="edit",e.View="view",e.Note="note",e.Tools="tools",e.Help="help",e.Context="context",e.NoteListContextMenu="noteListContextMenu",e.EditorContextMenu="editorContextMenu",e.FolderContextMenu="folderContextMenu",e.TagContextMenu="tagContextMenu"}(o=t.MenuItemLocation||(t.MenuItemLocation={})),t.isContextMenuItemLocation=function(e){return[o.Context,o.NoteListContextMenu,o.EditorContextMenu,o.FolderContextMenu,o.TagContextMenu].includes(e)},(u=t.ToolbarButtonLocation||(t.ToolbarButtonLocation={})).NoteToolbar="noteToolbar",u.EditorToolbar="editorToolbar",(l=t.SettingItemType||(t.SettingItemType={}))[l.Int=1]="Int",l[l.String=2]="String",l[l.Bool=3]="Bool",l[l.Array=4]="Array",l[l.Object=5]="Object",l[l.Button=6]="Button",(a=t.SettingItemSubType||(t.SettingItemSubType={})).FilePathAndArgs="file_path_and_args",a.FilePath="file_path",a.DirectoryPath="directory_path",(r=t.AppType||(t.AppType={})).Desktop="desktop",r.Mobile="mobile",r.Cli="cli",(i=t.SettingStorage||(t.SettingStorage={}))[i.Database=1]="Database",i[i.File=2]="File",(n=t.ContentScriptType||(t.ContentScriptType={})).MarkdownItPlugin="markdownItPlugin",n.CodeMirrorPlugin="codeMirrorPlugin"},607:function(e,t,o){var n=this&&this.__awaiter||function(e,t,o,n){return new(o||(o=Promise))((function(i,r){function a(e){try{u(n.next(e))}catch(e){r(e)}}function l(e){try{u(n.throw(e))}catch(e){r(e)}}function u(e){var t;e.done?i(e.value):(t=e.value,t instanceof o?t:new o((function(e){e(t)}))).then(a,l)}u((n=n.apply(e,t||[])).next())}))};Object.defineProperty(t,"__esModule",{value:!0});const i=o(775),r=o(200);i.default.plugins.register({onStart:function(){return n(this,void 0,void 0,(function*(){console.info("To Google search plugin started!"),yield i.default.commands.register({name:"To Google search",label:"To Google search",execute:()=>n(this,void 0,void 0,(function*(){const e="https://www.google.com/search?q="+(yield i.default.commands.execute("selectedText"));yield i.default.commands.execute("openItem",e)}))}),yield i.default.views.menuItems.create("GoogleSearchItem","To Google search",r.MenuItemLocation.EditorContextMenu)}))}})}},t={};!function o(n){var i=t[n];if(void 0!==i)return i.exports;var r=t[n]={exports:{}};return e[n].call(r.exports,r,r.exports,o),r.exports}(607)})();