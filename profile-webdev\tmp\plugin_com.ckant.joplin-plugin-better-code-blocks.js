(()=>{var n={523:(n,t)=>{var r=Object.defineProperty;function e(n){return void 0===n?{BS_PRIVATE_NESTED_SOME_NONE:0}:null!==n&&void 0!==n.BS_PRIVATE_NESTED_SOME_NONE?{BS_PRIVATE_NESTED_SOME_NONE:n.BS_PRIVATE_NESTED_SOME_NONE+1|0}:n}function u(n){if(null===n||void 0===n.BS_PRIVATE_NESTED_SOME_NONE)return n;var t=n.BS_PRIVATE_NESTED_SOME_NONE;return 0===t?void 0:{BS_PRIVATE_NESTED_SOME_NONE:t-1|0}}function o(n,t){return n<t?-1:n===t?0:1}function i(n,t){return n===t?0:n<t?-1:1}function c(n,t){return n>t?n:t}((n,t)=>{for(var e in(n=>{r(n,"__esModule",{value:!0})})(n),t)r(n,e,{get:t[e],enumerable:!0})})(t,{all:()=>Sr,any:()=>Nr,append:()=>K,at:()=>en,concat:()=>it,concatMany:()=>ct,copy:()=>bt,deepFlat:()=>ir,difference:()=>Ar,drop:()=>On,dropExactly:()=>wn,dropWhile:()=>Rn,eq:()=>_t,every:()=>at,filter:()=>Bn,filterMap:()=>gr,filterWithIndex:()=>qn,find:()=>gn,flat:()=>er,flatten:()=>or,flip:()=>lr,forEach:()=>Lt,forEachWithIndex:()=>Xt,get:()=>tn,getBy:()=>ln,getIndexBy:()=>zt,getUndefined:()=>fn,getUnsafe:()=>on,groupBy:()=>rr,head:()=>vn,includes:()=>Ut,init:()=>_n,initOrEmpty:()=>En,insertAt:()=>It,intersection:()=>Mr,intersperse:()=>$,isEmpty:()=>W,isNotEmpty:()=>X,join:()=>Qt,keep:()=>Gn,keepMap:()=>dr,keepWithIndex:()=>Fn,last:()=>dn,length:()=>L,make:()=>V,makeEmpty:()=>C,makeWithIndex:()=>B,map:()=>jn,mapWithIndex:()=>Cn,partition:()=>ut,placeholder:()=>k,prepend:()=>H,prependToAll:()=>Y,range:()=>yt,rangeBy:()=>St,reduce:()=>Kn,reduceReverse:()=>Hn,reduceWithIndex:()=>Yn,reject:()=>Wn,rejectWithIndex:()=>Jn,removeAt:()=>Bt,removeFirst:()=>Er,removeFirstBy:()=>pr,repeat:()=>F,replaceAt:()=>Mt,reverse:()=>J,shuffle:()=>rt,slice:()=>vt,sliceToEnd:()=>ht,some:()=>st,sort:()=>Zt,sortBy:()=>nr,splitAt:()=>$n,splitEvery:()=>tt,swapAt:()=>Ct,tail:()=>hn,tailOrEmpty:()=>pn,take:()=>mn,takeExactly:()=>bn,takeWhile:()=>Tn,tap:()=>ar,toTuple:()=>cr,uncons:()=>In,union:()=>Pr,uniq:()=>qt,uniqBy:()=>Vt,unzip:()=>Pt,updateAt:()=>jt,zip:()=>Tt,zipWith:()=>Ot,zipWithIndex:()=>yr});var f=function(n,t){for(var r in n)t(r)};function a(n,t){if(n===t)return 0;var r,e=typeof n,u=typeof t;switch(e){case"boolean":if("boolean"===u)return r=t,n?r?0:1:r?-1:0;break;case"function":if("function"===u)throw{RE_EXN_ID:"Invalid_argument",_1:"compare: functional value",Error:new Error};break;case"number":if("number"===u)return o(n,t);break;case"string":return"string"===u?i(n,t):1;case"undefined":return-1}switch(u){case"string":return-1;case"undefined":return 1;default:if("boolean"===e)return 1;if("boolean"===u)return-1;if("function"===e)return 1;if("function"===u)return-1;if("number"===e)return null===t||void 0!==t.BS_PRIVATE_NESTED_SOME_NONE?1:-1;if("number"===u)return null===n||void 0!==n.BS_PRIVATE_NESTED_SOME_NONE?-1:1;if(null===n)return void 0!==t.BS_PRIVATE_NESTED_SOME_NONE?1:-1;if(null===t)return void 0!==n.BS_PRIVATE_NESTED_SOME_NONE?-1:1;if(void 0!==n.BS_PRIVATE_NESTED_SOME_NONE)return void 0!==t.BS_PRIVATE_NESTED_SOME_NONE?l(n,t):-1;var c=0|n.TAG,f=0|t.TAG;if(248===c)return o(n[1],t[1]);if(251===c)throw{RE_EXN_ID:"Invalid_argument",_1:"equal: abstract value",Error:new Error};if(c!==f)return c<f?-1:1;var s=0|n.length,g=0|t.length;if(s===g){if(!Array.isArray(n))return n instanceof Date&&t instanceof Date?n-t:l(n,t);for(var v=0;;){var d=v;if(d===s)return 0;var h=a(n[d],t[d]);if(0!==h)return h;v=d+1|0}}else if(s<g)for(var p=0;;){var _=p;if(_===s)return-1;var E=a(n[_],t[_]);if(0!==E)return E;p=_+1|0}else for(var y=0;;){var m=y;if(m===g)return 1;var S=a(n[m],t[m]);if(0!==S)return S;y=m+1|0}}}function l(n,t){var r={contents:void 0},e={contents:void 0},u=function(n,t){var r=n[2],e=n[1];if(!Object.prototype.hasOwnProperty.call(e,t)||a(n[0][t],e[t])>0){var u=r.contents;return void 0!==u&&t>=u?void 0:void(r.contents=t)}},o=[n,t,e],c=[t,n,r];f(n,(function(n){return u(o,n)})),f(t,(function(n){return u(c,n)}));var l=r.contents,s=e.contents;return void 0!==l?void 0!==s?i(l,s):-1:void 0!==s?1:0}function s(n,t){if(n===t)return!0;var r=typeof n;if("string"===r||"number"===r||"boolean"===r||"undefined"===r||null===n)return!1;var e=typeof t;if("function"===r||"function"===e)throw{RE_EXN_ID:"Invalid_argument",_1:"equal: functional value",Error:new Error};if("number"===e||"undefined"===e||null===t)return!1;var u=0|n.TAG,o=0|t.TAG;if(248===u)return n[1]===t[1];if(251===u)throw{RE_EXN_ID:"Invalid_argument",_1:"equal: abstract value",Error:new Error};if(u!==o)return!1;var i=0|n.length;if(i!==(0|t.length))return!1;if(!Array.isArray(n)){if(n instanceof Date&&t instanceof Date)return!(n>t||n<t);var c={contents:!0};return f(n,(function(n){Object.prototype.hasOwnProperty.call(t,n)||(c.contents=!1)})),c.contents&&f(t,(function(r){Object.prototype.hasOwnProperty.call(n,r)&&s(t[r],n[r])||(c.contents=!1)})),c.contents}for(var a=0;;){var l=a;if(l===i)return!0;if(!s(n[l],t[l]))return!1;a=l+1|0}}var g=2147483647,v=-2147483648;function d(n,t){if(t>=0&&t<n.length)return e(n[t])}function h(n,t){if(!(t>=0&&t<n.length))throw{RE_EXN_ID:"Assert_failure",_1:["belt_Array.ml",27,4],Error:new Error};return n[t]}function p(n,t,r){var e=n[t];n[t]=n[r],n[r]=e}function _(n,t){if(n<=0)return[];for(var r=new Array(n),e=0;e<n;++e)r[e]=t;return r}function E(n,t){for(var r=n.length,e=t.length,u=new Array(r+e|0),o=0;o<r;++o)u[o]=n[o];for(var i=0;i<e;++i)u[r+i|0]=t[i];return u}function y(n,t,r){if(r<=0)return[];var e=n.length,u=t<0?c(e+t|0,0):t,o=e-u|0,i=o<r?o:r;if(i<=0)return[];for(var f=new Array(i),a=0;a<i;++a)f[a]=n[u+a|0];return f}function m(n,t){for(var r=n.length,e=t<0?c(r+t|0,0):t,u=r-e|0,o=new Array(u),i=0;i<u;++i)o[i]=n[e+i|0];return o}function S(n,t,r,e,u){if(e<=t)for(var o=0;o<u;++o)r[o+e|0]=n[o+t|0];else for(var i=u-1|0;i>=0;--i)r[i+e|0]=n[i+t|0]}function b(n,t){for(var r=0,e=n.length;r<e;++r)t(n[r])}function N(n,t){for(var r,u=n.length,o=0;void 0===r&&o<u;){var i=n[o];t(i)&&(r=e(i)),o=o+1|0}return r}function T(n,t){for(var r=n.length,e=new Array(r),o=0,i=0;i<r;++i){var c=t(n[i]);void 0!==c&&(e[o]=u(c),o=o+1|0)}return e.length=o,e}function A(n,t){for(var r=n.length,e=new Array(r),u=0;u<r;++u)e[u]=t(u,n[u]);return e}function O(n,t,r){for(var e=t,u=0,o=n.length;u<o;++u)e=r(e,n[u]);return e}function P(n,t,r){for(var e=t,u=0,o=n.length;u<o;++u)e=r(e,n[u],u);return e}function w(n,t){for(var r=n.length,e=0;;){var u=e;if(u===r)return!0;if(!t(n[u]))return!1;e=u+1|0}}function M(n,t){for(var r=n.length,e=0;;){var u=e;if(u===r)return!1;if(t(n[u]))return!0;e=u+1|0}}function R(n,t,r,e,u,o,i,c,f){for(var a=t+r|0,l=u+o|0,s=t,g=n[t],v=u,d=e[u],h=c;;){var p=h,_=d,E=v,y=g,m=s;if(f(y,_)<=0){i[p]=y;var b=m+1|0;if(b>=a)return S(e,E,i,p+1|0,l-E|0);h=p+1|0,g=n[b],s=b}else{i[p]=_;var N=E+1|0;if(N>=l)return S(n,m,i,p+1|0,a-m|0);h=p+1|0,d=e[N],v=N}}}function I(n,t,r,e,u,o){for(var i=0;i<u;++i){for(var c=n[t+i|0],f=(e+i|0)-1|0;f>=e&&o(r[f],c)>0;)r[f+1|0]=r[f],f=f-1|0;r[f+1|0]=c}}function D(n,t,r,e,u,o){if(u<=5)return I(n,t,r,e,u,o);var i=u/2|0,c=u-i|0;return D(n,t+i|0,r,e+i|0,c,o),D(n,t,n,t+c|0,i,o),R(n,t+c|0,i,r,e+i|0,c,r,e,o)}function j(n,t){var r=n.slice(0);return function(n,t){var r=n.length;if(r<=5)return I(n,0,n,0,r,t);var e=r/2|0,u=r-e|0,o=new Array(u);D(n,e,o,0,u,t),D(n,0,n,u,e,t),R(n,u,e,o,0,u,n,0,t)}(r,t),r}function k(n){}function C(n){return[]}var x=function(n,t){if(n<=0)return[];for(var r=new Array(n),e=0;e<n;++e)r[e]=t(e);return r};function B(){if(1===arguments.length){const n=arguments;return function(t){return x(t,n[0])}}return x(arguments[0],arguments[1])}var G=_;function V(){if(1===arguments.length){const n=arguments;return function(t){return G(t,n[0])}}return G(arguments[0],arguments[1])}var q=_;function F(){if(1===arguments.length){const n=arguments;return function(t){return q(t,n[0])}}return q(arguments[0],arguments[1])}function L(n){return n.length}function W(n){return 0===n.length}function X(n){return 0!==n.length}var J=function(n){for(var t=n.length,r=new Array(t),e=0;e<t;++e)r[e]=n[(t-1|0)-e|0];return r};function z(n,t){return E(n,[t])}function K(){if(1===arguments.length){const n=arguments;return function(t){return z(t,n[0])}}return z(arguments[0],arguments[1])}function U(n,t){return E([t],n)}function H(){if(1===arguments.length){const n=arguments;return function(t){return U(t,n[0])}}return U(arguments[0],arguments[1])}function Q(n,t){return O(n,[],(function(n,r){return E(n,[t,r])}))}function Y(){if(1===arguments.length){const n=arguments;return function(t){return Q(t,n[0])}}return Q(arguments[0],arguments[1])}function Z(n,t){return P(n,[],(function(r,e,u){return(n.length-1|0)===u?r.push(e):r.push(e,t),r}))}function $(){if(1===arguments.length){const n=arguments;return function(t){return Z(t,n[0])}}return Z(arguments[0],arguments[1])}var nn=d;function tn(){if(1===arguments.length){const n=arguments;return function(t){return nn(t,n[0])}}return nn(arguments[0],arguments[1])}var rn=d;function en(){if(1===arguments.length){const n=arguments;return function(t){return rn(t,n[0])}}return rn(arguments[0],arguments[1])}function un(n,t){return n[t]}function on(){if(1===arguments.length){const n=arguments;return function(t){return un(t,n[0])}}return un(arguments[0],arguments[1])}function cn(n,t){return n[t]}function fn(){if(1===arguments.length){const n=arguments;return function(t){return cn(t,n[0])}}return cn(arguments[0],arguments[1])}var an=N;function ln(){if(1===arguments.length){const n=arguments;return function(t){return an(t,n[0])}}return an(arguments[0],arguments[1])}var sn=N;function gn(){if(1===arguments.length){const n=arguments;return function(t){return sn(t,n[0])}}return sn(arguments[0],arguments[1])}function vn(n){return d(n,0)}function dn(n){var t=n.length;return 0===t?void 0:d(n,t-1|0)}function hn(n){var t=n.length;if(1===t)return[];if(0!==t){var r=m(n,1);return 0!==r.length?r:void 0}}function pn(n){var t=hn(n);return void 0!==t?t:[]}function _n(n){var t=n.length;return 0===t?void 0:y(n,0,t-1|0)}function En(n){var t=_n(n);return void 0!==t?t:[]}function yn(n,t){var r=n.length;return y(n,0,t<0?0:r<t?r:t)}function mn(){if(1===arguments.length){const n=arguments;return function(t){return yn(t,n[0])}}return yn(arguments[0],arguments[1])}function Sn(n,t){return t<0||t>n.length?void 0:y(n,0,t)}function bn(){if(1===arguments.length){const n=arguments;return function(t){return Sn(t,n[0])}}return Sn(arguments[0],arguments[1])}function Nn(n,t){return O(n,[],(function(n,r){return t(r)&&n.push(r),n}))}function Tn(){if(1===arguments.length){const n=arguments;return function(t){return Nn(t,n[0])}}return Nn(arguments[0],arguments[1])}function An(n,t){var r=n.length;return m(n,t<0?0:r<t?r:t)}function On(){if(1===arguments.length){const n=arguments;return function(t){return An(t,n[0])}}return An(arguments[0],arguments[1])}function Pn(n,t){return t<0||t>n.length?void 0:m(n,t)}function wn(){if(1===arguments.length){const n=arguments;return function(t){return Pn(t,n[0])}}return Pn(arguments[0],arguments[1])}function Mn(n,t){return O(n,[],(function(n,r){return t(r)||n.push(r),n}))}function Rn(){if(1===arguments.length){const n=arguments;return function(t){return Mn(t,n[0])}}return Mn(arguments[0],arguments[1])}function In(n){if(0!==n.length)return[h(n,0),m(n,1)]}function Dn(n,t){return function(n,t){for(var r=n.length,e=new Array(r),u=0;u<r;++u)e[u]=t(n[u]);return e}(n,t)}function jn(){if(1===arguments.length){const n=arguments;return function(t){return Dn(t,n[0])}}return Dn(arguments[0],arguments[1])}var kn=A;function Cn(){if(1===arguments.length){const n=arguments;return function(t){return kn(t,n[0])}}return kn(arguments[0],arguments[1])}function xn(n,t){for(var r=0,e=[];r<n.length;){var u=n[r];t(u)&&e.push(u),r=r+1|0}return e}function Bn(){if(1===arguments.length){const n=arguments;return function(t){return xn(t,n[0])}}return xn(arguments[0],arguments[1])}var Gn=Bn;function Vn(n,t){for(var r=0,e=[];r<n.length;){var u=n[r];t(r,u)&&e.push(u),r=r+1|0}return e}function qn(){if(1===arguments.length){const n=arguments;return function(t){return Vn(t,n[0])}}return Vn(arguments[0],arguments[1])}var Fn=qn;function Ln(n,t){return Bn(n,(function(n){return!t(n)}))}function Wn(){if(1===arguments.length){const n=arguments;return function(t){return Ln(t,n[0])}}return Ln(arguments[0],arguments[1])}function Xn(n,t){return qn(n,(function(n,r){return!t(n,r)}))}function Jn(){if(1===arguments.length){const n=arguments;return function(t){return Xn(t,n[0])}}return Xn(arguments[0],arguments[1])}var zn=O;function Kn(){if(2===arguments.length){const n=arguments;return function(t){return zn(t,n[0],n[1])}}return zn(arguments[0],arguments[1],arguments[2])}var Un=function(n,t,r){for(var e=t,u=n.length-1|0;u>=0;--u)e=r(e,n[u]);return e};function Hn(){if(2===arguments.length){const n=arguments;return function(t){return Un(t,n[0],n[1])}}return Un(arguments[0],arguments[1],arguments[2])}var Qn=P;function Yn(){if(2===arguments.length){const n=arguments;return function(t){return Qn(t,n[0],n[1])}}return Qn(arguments[0],arguments[1],arguments[2])}function Zn(n,t){return t<0||t>n.length?void 0:[y(n,0,t),m(n,t)]}function $n(){if(1===arguments.length){const n=arguments;return function(t){return Zn(t,n[0])}}return Zn(arguments[0],arguments[1])}function nt(n,t){if(t<1||n.length<=t)return[n];for(var r=0,e=[];r<n.length;){var u=r+t|0;e.push(y(n,r,t)),r=u}return e}function tt(){if(1===arguments.length){const n=arguments;return function(t){return nt(t,n[0])}}return nt(arguments[0],arguments[1])}var rt=function(n){var t=n.slice(0);return function(n){for(var t=n.length,r=0;r<t;++r)p(n,r,(e=r,u=t,o=void 0,0|((o=Math.random()*(u-e|0))>g?g:o<v?v:Math.floor(o))+e));var e,u,o}(t),t},et=function(n,t){for(var r=n.length,e=0,u=0,o=new Array(r),i=new Array(r),c=0;c<r;++c){var f=n[c];t(f)?(o[e]=f,e=e+1|0):(i[u]=f,u=u+1|0)}return o.length=e,i.length=u,[o,i]};function ut(){if(1===arguments.length){const n=arguments;return function(t){return et(t,n[0])}}return et(arguments[0],arguments[1])}var ot=E;function it(){if(1===arguments.length){const n=arguments;return function(t){return ot(t,n[0])}}return ot(arguments[0],arguments[1])}var ct=function(n){for(var t=n.length,r=0,e=0;e<t;++e)r=r+n[e].length|0;var u=new Array(r);r=0;for(var o=0;o<t;++o)for(var i=n[o],c=0,f=i.length;c<f;++c)u[r]=i[c],r=r+1|0;return u},ft=w;function at(){if(1===arguments.length){const n=arguments;return function(t){return ft(t,n[0])}}return ft(arguments[0],arguments[1])}var lt=M;function st(){if(1===arguments.length){const n=arguments;return function(t){return lt(t,n[0])}}return lt(arguments[0],arguments[1])}var gt=y;function vt(){if(2===arguments.length){const n=arguments;return function(t){return gt(t,n[0],n[1])}}return gt(arguments[0],arguments[1],arguments[2])}var dt=m;function ht(){if(1===arguments.length){const n=arguments;return function(t){return dt(t,n[0])}}return dt(arguments[0],arguments[1])}var pt=function(n,t,r){var e=n.length;return e===t.length&&function(n,t,r,e,u){for(;;){var o=r;if(o===u)return!0;if(!e(n[o],t[o]))return!1;r=o+1|0}}(n,t,0,r,e)};function _t(){if(2===arguments.length){const n=arguments;return function(t){return pt(t,n[0],n[1])}}return pt(arguments[0],arguments[1],arguments[2])}var Et=function(n,t){var r=t-n|0;if(r<0)return[];for(var e=new Array(r+1|0),u=0;u<=r;++u)e[u]=n+u|0;return e};function yt(){if(1===arguments.length){const n=arguments;return function(t){return Et(t,n[0])}}return Et(arguments[0],arguments[1])}var mt=function(n,t,r){var e=t-n|0;if(e<0||r<=0)return[];for(var u=1+(e/r|0)|0,o=new Array(u),i=n,c=0;c<u;++c)o[c]=i,i=i+r|0;return o};function St(){if(2===arguments.length){const n=arguments;return function(t){return mt(t,n[0],n[1])}}return mt(arguments[0],arguments[1],arguments[2])}function bt(n){return n.slice(0)}var Nt=function(n,t){for(var r=n.length,e=t.length,u=r<e?r:e,o=new Array(u),i=0;i<u;++i)o[i]=[n[i],t[i]];return o};function Tt(){if(1===arguments.length){const n=arguments;return function(t){return Nt(t,n[0])}}return Nt(arguments[0],arguments[1])}var At=function(n,t,r){for(var e=n.length,u=t.length,o=e<u?e:u,i=new Array(o),c=0;c<o;++c)i[c]=r(n[c],t[c]);return i};function Ot(){if(2===arguments.length){const n=arguments;return function(t){return At(t,n[0],n[1])}}return At(arguments[0],arguments[1],arguments[2])}var Pt=function(n){for(var t=n.length,r=new Array(t),e=new Array(t),u=0;u<t;++u){var o=n[u];r[u]=o[0],e[u]=o[1]}return[r,e]};function wt(n,t,r){return A(n,(function(n,e){return n===t?r:e}))}function Mt(){if(2===arguments.length){const n=arguments;return function(t){return wt(t,n[0],n[1])}}return wt(arguments[0],arguments[1],arguments[2])}function Rt(n,t,r){var e=$n(n,t);return void 0!==e?E(e[0],E([r],e[1])):n}function It(){if(2===arguments.length){const n=arguments;return function(t){return Rt(t,n[0],n[1])}}return Rt(arguments[0],arguments[1],arguments[2])}function Dt(n,t,r){return A(n,(function(n,e){return n===t?r(e):e}))}function jt(){if(2===arguments.length){const n=arguments;return function(t){return Dt(t,n[0],n[1])}}return Dt(arguments[0],arguments[1],arguments[2])}function kt(n,t,r){var e=d(n,t),o=d(n,r);if(void 0===e)return n;if(void 0===o)return n;var i=u(o),c=u(e);return A(n,(function(n,e){return t===n?i:r===n?c:e}))}function Ct(){if(2===arguments.length){const n=arguments;return function(t){return kt(t,n[0],n[1])}}return kt(arguments[0],arguments[1],arguments[2])}function xt(n,t){return qn(n,(function(n,r){return n!==t}))}function Bt(){if(1===arguments.length){const n=arguments;return function(t){return xt(t,n[0])}}return xt(arguments[0],arguments[1])}function Gt(n,t){for(var r=0,e=[];r<n.length;){var u=n[r];M(e,function(n){return function(r){return s(t(r),t(n))}}(u))||e.push(u),r=r+1|0}return e}function Vt(){if(1===arguments.length){const n=arguments;return function(t){return Gt(t,n[0])}}return Gt(arguments[0],arguments[1])}function qt(n){return Vt(n,(function(n){return n}))}var Ft=b;function Lt(){if(1===arguments.length){const n=arguments;return function(t){return Ft(t,n[0])}}return Ft(arguments[0],arguments[1])}var Wt=function(n,t){for(var r=0,e=n.length;r<e;++r)t(r,n[r])};function Xt(){if(1===arguments.length){const n=arguments;return function(t){return Wt(t,n[0])}}return Wt(arguments[0],arguments[1])}var Jt=function(n,t){for(var r,e=n.length,u=0;void 0===r&&u<e;)t(n[u])&&(r=u),u=u+1|0;return r};function zt(){if(1===arguments.length){const n=arguments;return function(t){return Jt(t,n[0])}}return Jt(arguments[0],arguments[1])}function Kt(n,t){return M(n,(function(n){return s(n,t)}))}function Ut(){if(1===arguments.length){const n=arguments;return function(t){return Kt(t,n[0])}}return Kt(arguments[0],arguments[1])}function Ht(n,t){return n.join(t)}function Qt(){if(1===arguments.length){const n=arguments;return function(t){return Ht(t,n[0])}}return Ht(arguments[0],arguments[1])}var Yt=j;function Zt(){if(1===arguments.length){const n=arguments;return function(t){return Yt(t,n[0])}}return Yt(arguments[0],arguments[1])}function $t(n,t){return j(n,(function(n,r){var e=t(n),u=t(r);return e===u?0:function(n,t){return a(n,t)<0}(e,u)?-1:1}))}function nr(){if(1===arguments.length){const n=arguments;return function(t){return $t(t,n[0])}}return $t(arguments[0],arguments[1])}function tr(n,t){return O(n,{},(function(n,r){var u=t(r),o=function(n,t){if(t in n)return e(n[t])}(n,u);return void 0!==o?o.push(r):n[u]=[r],n}))}function rr(){if(1===arguments.length){const n=arguments;return function(t){return tr(t,n[0])}}return tr(arguments[0],arguments[1])}function er(n){return O(n,[],(function(n,t){return Array.isArray(t)?b(t,(function(t){n.push(t)})):n.push(t),n}))}function ur(n,t){for(var r=0;r<n.length;){var e=n[r];Array.isArray(e)?or(e,t):t.push(e),r=r+1|0}return t}function or(){if(1===arguments.length){const n=arguments;return function(t){return ur(t,n[0])}}return ur(arguments[0],arguments[1])}function ir(n){return or(n,[])}function cr(n){return n}function fr(n,t){return b(n,t),n}function ar(){if(1===arguments.length){const n=arguments;return function(t){return fr(t,n[0])}}return fr(arguments[0],arguments[1])}function lr(n){return[n[1],n[0]]}var sr=T;function gr(){if(1===arguments.length){const n=arguments;return function(t){return sr(t,n[0])}}return sr(arguments[0],arguments[1])}var vr=T;function dr(){if(1===arguments.length){const n=arguments;return function(t){return vr(t,n[0])}}return vr(arguments[0],arguments[1])}function hr(n,t,r){return O(n,[!1,[]],(function(n,e){var u=n[1];return n[0]?(u.push(e),[!0,u]):r(e,t)?[!0,u]:(u.push(e),[!1,u])}))[1]}function pr(){if(2===arguments.length){const n=arguments;return function(t){return hr(t,n[0],n[1])}}return hr(arguments[0],arguments[1],arguments[2])}function _r(n,t){return pr(n,t,s)}function Er(){if(1===arguments.length){const n=arguments;return function(t){return _r(t,n[0])}}return _r(arguments[0],arguments[1])}function yr(n){return P(n,[],(function(n,t,r){return n.push([t,r]),n}))}function mr(n,t){return w(n,t)}function Sr(){if(1===arguments.length){const n=arguments;return function(t){return mr(t,n[0])}}return mr(arguments[0],arguments[1])}function br(n,t){return M(n,t)}function Nr(){if(1===arguments.length){const n=arguments;return function(t){return br(t,n[0])}}return br(arguments[0],arguments[1])}function Tr(n,t){return Wn(Vt(n,(function(n){return n})),(function(n){return Ut(t,n)}))}function Ar(){if(1===arguments.length){const n=arguments;return function(t){return Tr(t,n[0])}}return Tr(arguments[0],arguments[1])}function Or(n,t){return Vt(E(n,t),(function(n){return n}))}function Pr(){if(1===arguments.length){const n=arguments;return function(t){return Or(t,n[0])}}return Or(arguments[0],arguments[1])}function wr(n,t){var r=n.length>t.length?[n,t]:[t,n],e=r[1];return Vt(Bn(r[0],(function(n){return Ut(e,n)})),(function(n){return n}))}function Mr(){if(1===arguments.length){const n=arguments;return function(t){return wr(t,n[0])}}return wr(arguments[0],arguments[1])}},982:(n,t)=>{var r=Object.defineProperty;function e(n){}function u(n,t,r){return n?t(void 0):r(void 0)}function o(){if(2===arguments.length){const n=arguments;return function(t){return u(t,n[0],n[1])}}return u(arguments[0],arguments[1],arguments[2])}function i(n){return!n}function c(n){return!n}function f(n,t){return!!n&&t}function a(){if(1===arguments.length){const n=arguments;return function(t){return f(t,n[0])}}return f(arguments[0],arguments[1])}function l(n,t){return!!n||t}function s(){if(1===arguments.length){const n=arguments;return function(t){return l(t,n[0])}}return l(arguments[0],arguments[1])}function g(n,t){return!(n&&t)}function v(){if(1===arguments.length){const n=arguments;return function(t){return g(t,n[0])}}return g(arguments[0],arguments[1])}function d(n,t){return!(n||t)}function h(){if(1===arguments.length){const n=arguments;return function(t){return d(t,n[0])}}return d(arguments[0],arguments[1])}function p(n,t){return!(n||!t)||!!n&&!t}function _(){if(1===arguments.length){const n=arguments;return function(t){return p(t,n[0])}}return p(arguments[0],arguments[1])}function E(n,t){return!_(n,t)}function y(){if(1===arguments.length){const n=arguments;return function(t){return E(t,n[0])}}return E(arguments[0],arguments[1])}function m(n,t){return!n||t}function S(){if(1===arguments.length){const n=arguments;return function(t){return m(t,n[0])}}return m(arguments[0],arguments[1])}((n,t)=>{for(var e in(n=>{r(n,"__esModule",{value:!0})})(n),t)r(n,e,{get:t[e],enumerable:!0})})(t,{and:()=>a,ifElse:()=>o,implies:()=>S,inverse:()=>i,nand:()=>v,nor:()=>h,not:()=>c,or:()=>s,placeholder:()=>e,xnor:()=>y,xor:()=>_})},198:(n,t)=>{var r=Object.defineProperty;function e(n,t){if(t in n)return void 0===(r=n[t])?{BS_PRIVATE_NESTED_SOME_NONE:0}:null!==r&&void 0!==r.BS_PRIVATE_NESTED_SOME_NONE?{BS_PRIVATE_NESTED_SOME_NONE:r.BS_PRIVATE_NESTED_SOME_NONE+1|0}:r;var r}((n,t)=>{for(var e in(n=>{r(n,"__esModule",{value:!0})})(n),t)r(n,e,{get:t[e],enumerable:!0})})(t,{deleteKey:()=>j,deleteKeys:()=>C,filter:()=>F,filterWithKey:()=>W,fromPairs:()=>N,get:()=>_,getUnsafe:()=>h,isEmpty:()=>Q,isNotEmpty:()=>Y,keys:()=>b,makeEmpty:()=>v,map:()=>B,mapWithKey:()=>V,merge:()=>A,placeholder:()=>g,prop:()=>y,reject:()=>J,rejectWithKey:()=>K,selectKeys:()=>H,set:()=>P,toPairs:()=>m,update:()=>M,updateUnsafe:()=>I,values:()=>S});var u=function(n,t){delete n[t]};function o(n){for(var t={},r=n.length,e=0;e<r;++e){var u=n[e];t[u[0]]=u[1]}return t}var i=function(n,t){for(var r in n)t(r)};function c(n,t){if(n===t)return!0;var r=typeof n;if("string"===r||"number"===r||"boolean"===r||"undefined"===r||null===n)return!1;var e=typeof t;if("function"===r||"function"===e)throw{RE_EXN_ID:"Invalid_argument",_1:"equal: functional value",Error:new Error};if("number"===e||"undefined"===e||null===t)return!1;var u=0|n.TAG,o=0|t.TAG;if(248===u)return n[1]===t[1];if(251===u)throw{RE_EXN_ID:"Invalid_argument",_1:"equal: abstract value",Error:new Error};if(u!==o)return!1;var f=0|n.length;if(f!==(0|t.length))return!1;if(!Array.isArray(n)){if(n instanceof Date&&t instanceof Date)return!(n>t||n<t);var a={contents:!0};return i(n,(function(n){Object.prototype.hasOwnProperty.call(t,n)||(a.contents=!1)})),a.contents&&i(t,(function(r){Object.prototype.hasOwnProperty.call(n,r)&&c(t[r],n[r])||(a.contents=!1)})),a.contents}for(var l=0;;){var s=l;if(s===f)return!0;if(!c(n[s],t[s]))return!1;l=s+1|0}}function f(n,t){for(var r=n.length,e=new Array(r),u=0;u<r;++u)e[u]=t(n[u]);return e}function a(n,t,r){for(var e=t,u=0,o=n.length;u<o;++u)e=r(e,n[u]);return e}function l(n,t){return function(n,t){for(var r=n.length,e=t.length,u=new Array(r+e|0),o=0;o<r;++o)u[o]=n[o];for(var i=0;i<e;++i)u[r+i|0]=t[i];return u}(n,[t])}function s(){if(1===arguments.length){const n=arguments;return function(t){return l(t,n[0])}}return l(arguments[0],arguments[1])}function g(n){}function v(n){return{}}function d(n,t){return n[t]}function h(){if(1===arguments.length){const n=arguments;return function(t){return d(t,n[0])}}return d(arguments[0],arguments[1])}var p=e;function _(){if(1===arguments.length){const n=arguments;return function(t){return p(t,n[0])}}return p(arguments[0],arguments[1])}function E(n,t){return n[t]}function y(){if(1===arguments.length){const n=arguments;return function(t){return E(t,n[0])}}return E(arguments[0],arguments[1])}function m(n){return Object.entries(n)}var S=function(n){for(var t=Object.keys(n),r=t.length,e=new Array(r),u=0;u<r;++u)e[u]=n[t[u]];return e};function b(n){return Object.keys(n)}var N=o;function T(n,t){return Object.assign({},n,t)}function A(){if(1===arguments.length){const n=arguments;return function(t){return T(t,n[0])}}return T(arguments[0],arguments[1])}function O(n,t,r){var e=A({},n);return e[t]=r,e}function P(){if(2===arguments.length){const n=arguments;return function(t){return O(t,n[0],n[1])}}return O(arguments[0],arguments[1],arguments[2])}function w(n,t,r){return P(n,t,r(e(n,t)))}function M(){if(2===arguments.length){const n=arguments;return function(t){return w(t,n[0],n[1])}}return w(arguments[0],arguments[1],arguments[2])}function R(n,t,r){return P(n,t,r(n[t]))}function I(){if(2===arguments.length){const n=arguments;return function(t){return R(t,n[0],n[1])}}return R(arguments[0],arguments[1],arguments[2])}function D(n,t){var r=A({},n);return u(r,t),r}function j(){if(1===arguments.length){const n=arguments;return function(t){return D(t,n[0])}}return D(arguments[0],arguments[1])}function k(n,t){var r=A({},n);return function(n){for(var t=0,e=n.length;t<e;++t)o=n[t],u(r,o);var o}(t),r}function C(){if(1===arguments.length){const n=arguments;return function(t){return k(t,n[0])}}return k(arguments[0],arguments[1])}function x(n,t){return o(f(Object.keys(n),(function(r){return[r,t(n[r])]})))}function B(){if(1===arguments.length){const n=arguments;return function(t){return x(t,n[0])}}return x(arguments[0],arguments[1])}function G(n,t){return o(f(Object.keys(n),(function(r){return[r,t(r,n[r])]})))}function V(){if(1===arguments.length){const n=arguments;return function(t){return G(t,n[0])}}return G(arguments[0],arguments[1])}function q(n,t){return o(a(Object.keys(n),[],(function(r,e){var u=n[e];return t(u)?s(r,[e,u]):r})))}function F(){if(1===arguments.length){const n=arguments;return function(t){return q(t,n[0])}}return q(arguments[0],arguments[1])}function L(n,t){return o(a(Object.keys(n),[],(function(r,e){var u=n[e];return t(e,u)?s(r,[e,u]):r})))}function W(){if(1===arguments.length){const n=arguments;return function(t){return L(t,n[0])}}return L(arguments[0],arguments[1])}function X(n,t){return F(n,(function(n){return!t(n)}))}function J(){if(1===arguments.length){const n=arguments;return function(t){return X(t,n[0])}}return X(arguments[0],arguments[1])}function z(n,t){return W(n,(function(n,r){return!t(n,r)}))}function K(){if(1===arguments.length){const n=arguments;return function(t){return z(t,n[0])}}return z(arguments[0],arguments[1])}function U(n,t){return W(n,(function(n,r){return t.includes(n)}))}function H(){if(1===arguments.length){const n=arguments;return function(t){return U(t,n[0])}}return U(arguments[0],arguments[1])}function Q(n){return c(n,{})}function Y(n){return!c(n,{})}},654:(n,t)=>{var r=Object.defineProperty;function e(n){return void 0===n?{BS_PRIVATE_NESTED_SOME_NONE:0}:null!==n&&void 0!==n.BS_PRIVATE_NESTED_SOME_NONE?{BS_PRIVATE_NESTED_SOME_NONE:n.BS_PRIVATE_NESTED_SOME_NONE+1|0}:n}function u(n){if(null===n||void 0===n.BS_PRIVATE_NESTED_SOME_NONE)return n;var t=n.BS_PRIVATE_NESTED_SOME_NONE;return 0===t?void 0:{BS_PRIVATE_NESTED_SOME_NONE:t-1|0}}((n,t)=>{for(var e in(n=>{r(n,"__esModule",{value:!0})})(n),t)r(n,e,{get:t[e],enumerable:!0})})(t,{after:()=>tn,allPass:()=>B,always:()=>T,anyPass:()=>V,before:()=>$,both:()=>S,coerce:()=>fn,debounce:()=>H,defaultTo:()=>O,either:()=>N,equals:()=>y,falsy:()=>P,identity:()=>_,ifElse:()=>R,ignore:()=>I,makeControlledDebounce:()=>K,makeControlledThrottle:()=>W,memoize:()=>en,memoizeWithKey:()=>on,once:()=>rn,placeholder:()=>p,tap:()=>F,throttle:()=>J,toMutable:()=>cn,truthy:()=>w,tryCatch:()=>Y,unless:()=>j,when:()=>C});var o={contents:0};function i(n){return o.contents=o.contents+1|0,n+"/"+o.contents}var c=i("Caml_js_exceptions.Error"),f=c,a=function(n,t){for(var r in n)t(r)};function l(n,t,r){return void 0!==n?r(u(n)):t}function s(n){return void 0!==n?n.h:0}function g(n,t,r,e){var u=s(n),o=s(e);return{k:t,v:r,h:u>=o?u+1|0:o+1|0,l:n,r:e}}function v(n,t,r,e){var u=void 0!==n?n.h:0,o=void 0!==e?e.h:0;if(u>(o+2|0)){var i=n.l,c=n.r;return s(i)>=s(c)?g(i,n.k,n.v,g(c,t,r,e)):g(g(i,n.k,n.v,c.l),c.k,c.v,g(c.r,t,r,e))}if(o<=(u+2|0))return{k:t,v:r,h:u>=o?u+1|0:o+1|0,l:n,r:e};var f=e.l,a=e.r;return s(a)>=s(f)?g(g(n,t,r,f),e.k,e.v,a):g(g(n,t,r,f.l),f.k,f.v,g(f.r,e.k,e.v,a))}function d(n,t,r){if(void 0===n)return{k:t,v:r,h:1,l:void 0,r:void 0};var e,u,o=n.k;if(t===o)return u=r,(e=n).v===u?e:{k:e.k,v:u,h:e.h,l:e.l,r:e.r};var i=n.v;return t<o?v(d(n.l,t,r),o,i,n.r):v(n.l,o,i,d(n.r,t,r))}var h=function(n,t){for(;;){var r=n;if(void 0===r)return;var u=r.k;if(t===u)return e(r.v);n=t<u?r.l:r.r}};function p(n){}function _(n){return n}var E=function n(t,r){if(t===r)return!0;var e=typeof t;if("string"===e||"number"===e||"boolean"===e||"undefined"===e||null===t)return!1;var u=typeof r;if("function"===e||"function"===u)throw{RE_EXN_ID:"Invalid_argument",_1:"equal: functional value",Error:new Error};if("number"===u||"undefined"===u||null===r)return!1;var o=0|t.TAG,i=0|r.TAG;if(248===o)return t[1]===r[1];if(251===o)throw{RE_EXN_ID:"Invalid_argument",_1:"equal: abstract value",Error:new Error};if(o!==i)return!1;var c=0|t.length;if(c!==(0|r.length))return!1;if(!Array.isArray(t)){if(t instanceof Date&&r instanceof Date)return!(t>r||t<r);var f={contents:!0};return a(t,(function(n){Object.prototype.hasOwnProperty.call(r,n)||(f.contents=!1)})),f.contents&&a(r,(function(e){Object.prototype.hasOwnProperty.call(t,e)&&n(r[e],t[e])||(f.contents=!1)})),f.contents}for(var l=0;;){var s=l;if(s===c)return!0;if(!n(t[s],r[s]))return!1;l=s+1|0}};function y(){if(1===arguments.length){const n=arguments;return function(t){return E(t,n[0])}}return E(arguments[0],arguments[1])}function m(n,t,r){return!!t(n)&&r(n)}function S(){if(2===arguments.length){const n=arguments;return function(t){return m(t,n[0],n[1])}}return m(arguments[0],arguments[1],arguments[2])}function b(n,t,r){return!!t(n)||r(n)}function N(){if(2===arguments.length){const n=arguments;return function(t){return b(t,n[0],n[1])}}return b(arguments[0],arguments[1],arguments[2])}function T(n){return function(){return n}}function A(n,t){return null==n?t:n}function O(){if(1===arguments.length){const n=arguments;return function(t){return A(t,n[0])}}return A(arguments[0],arguments[1])}function P(){return!1}function w(){return!0}function M(n,t,r,e){return t(n)?r(n):e(n)}function R(){if(3===arguments.length){const n=arguments;return function(t){return M(t,n[0],n[1],n[2])}}return M(arguments[0],arguments[1],arguments[2],arguments[3])}function I(n){}function D(n,t,r){return t(n)?n:r(n)}function j(){if(2===arguments.length){const n=arguments;return function(t){return D(t,n[0],n[1])}}return D(arguments[0],arguments[1],arguments[2])}function k(n,t,r){return t(n)?r(n):n}function C(){if(2===arguments.length){const n=arguments;return function(t){return k(t,n[0],n[1])}}return k(arguments[0],arguments[1],arguments[2])}function x(n,t){return function(t){for(var r=t.length,e=0;;){var u=e;if(u===r)return!0;if(!(0,t[u])(n))return!1;e=u+1|0}}(t)}function B(){if(1===arguments.length){const n=arguments;return function(t){return x(t,n[0])}}return x(arguments[0],arguments[1])}function G(n,t){return function(t){for(var r=t.length,e=0;;){var u=e;if(u===r)return!1;if((0,t[u])(n))return!0;e=u+1|0}}(t)}function V(){if(1===arguments.length){const n=arguments;return function(t){return G(t,n[0])}}return G(arguments[0],arguments[1])}function q(n,t){return t(n),n}function F(){if(1===arguments.length){const n=arguments;return function(t){return q(t,n[0])}}return q(arguments[0],arguments[1])}function L(n,t){var r={contents:!1},u={contents:void 0},o=function(n){l(u.contents,void 0,(function(n){clearTimeout(n)})),u.contents=void 0},i={contents:t.leading};return{cancel:o,invoke:function(...t){return o(),n(...t)},isScheduled:function(n){return r.contents},schedule:function(...c){if(i.contents)return i.contents=!1,n(...c);if(!r.contents){o(),r.contents=!0,n(...c);var f=setTimeout((function(n){r.contents=!1,u.contents=void 0}),t.delay);u.contents=e(f)}}}}function W(){if(1===arguments.length){const n=arguments;return function(t){return L(t,n[0])}}return L(arguments[0],arguments[1])}function X(n,t){return W(n,{delay:t,leading:!1}).schedule}function J(){if(1===arguments.length){const n=arguments;return function(t){return X(t,n[0])}}return X(arguments[0],arguments[1])}function z(n,t){var r={contents:void 0},u=function(n){l(r.contents,void 0,(function(n){clearTimeout(n)})),r.contents=void 0},o={contents:t.leading};return{cancel:u,invoke:function(...t){return u(),n(...t)},isScheduled:function(n){return function(n){return void 0!==n}(r.contents)},schedule:function(...i){if(o.contents)return o.contents=!1,n(...i);u();var c=setTimeout((function(t){n(...i),r.contents=void 0}),t.delay);r.contents=e(c)}}}function K(){if(1===arguments.length){const n=arguments;return function(t){return z(t,n[0])}}return z(arguments[0],arguments[1])}function U(n,t){return K(n,{delay:t,leading:!1}).schedule}function H(){if(1===arguments.length){const n=arguments;return function(t){return U(t,n[0])}}return U(arguments[0],arguments[1])}function Q(n,t){try{return{TAG:0,_0:t(n)}}catch(n){var r=function(n){return null!=n&&"string"==typeof n.RE_EXN_ID}(u=n)?u:{RE_EXN_ID:c,_1:u};if(r.RE_EXN_ID===f){var e=r._1.message;return void 0!==e?{TAG:1,_0:e}:{TAG:1,_0:"F.tryCatch: unknown error"}}throw r}var u}function Y(){if(1===arguments.length){const n=arguments;return function(t){return Q(t,n[0])}}return Q(arguments[0],arguments[1])}function Z(n,t){var r={contents:0},o={contents:void 0};return function(...i){var c=o.contents;if(void 0!==c){if(r.contents>=n)return u(c);var f=t(...i);return o.contents=e(f),r.contents=r.contents+1|0,f}var a=t(...i);return o.contents=e(a),r.contents=r.contents+1|0,a}}function $(){if(1===arguments.length){const n=arguments;return function(t){return Z(t,n[0])}}return Z(arguments[0],arguments[1])}function nn(n,t){var r={contents:0};return function(...u){return r.contents<n?void(r.contents=r.contents+1|0):e(t(...u))}}function tn(){if(1===arguments.length){const n=arguments;return function(t){return nn(t,n[0])}}return nn(arguments[0],arguments[1])}function rn(n){var t={contents:void 0};return function(...r){var o=t.contents;if(void 0!==o)return u(o);var i=n(...r);return t.contents=e(i),i}}var en=rn;function un(n,t){var r={contents:void 0};return function(...e){var o=n(...e),i=h(r.contents,o);if(void 0!==i)return u(i);var c=t(...e);return r.contents=d(r.contents,o,c),c}}function on(){if(1===arguments.length){const n=arguments;return function(t){return un(t,n[0])}}return un(arguments[0],arguments[1])}function cn(n){return n}function fn(n){return n}},564:(n,t)=>{var r=Object.defineProperty;function e(n,t){return typeof n===t}function u(){if(1===arguments.length){const n=arguments;return function(t){return e(t,n[0])}}return e(arguments[0],arguments[1])}function o(n){return"string"==typeof n}function i(n){return"number"==typeof n&&!Number.isNaN(n)}function c(n){return"boolean"==typeof n}((n,t)=>{for(var e in(n=>{r(n,"__esModule",{value:!0})})(n),t)r(n,e,{get:t[e],enumerable:!0})})(t,{is:()=>u,isArray:()=>a,isBoolean:()=>c,isDate:()=>v,isError:()=>g,isFunction:()=>s,isNot:()=>y,isNotNullable:()=>h,isNull:()=>p,isNullable:()=>d,isNumber:()=>i,isObject:()=>l,isPromise:()=>f,isString:()=>o,isUndefined:()=>_});var f=n=>n instanceof Promise;function a(n){return Array.isArray(n)}function l(n){return!(!n||Array.isArray(n))&&"object"==typeof n}function s(n){return"function"==typeof n}var g=n=>n instanceof Error,v=n=>n instanceof Date;function d(n){return null==n}function h(n){return!(null==n)}var p=n=>null===n,_=n=>void 0===n;function E(n,t){return!t(n)}function y(){if(1===arguments.length){const n=arguments;return function(t){return E(t,n[0])}}return E(arguments[0],arguments[1])}},327:(n,t)=>{var r=Object.defineProperty;function e(n,t){if(0===t)throw{RE_EXN_ID:"Division_by_zero",Error:new Error};return n%t}function u(n){}function o(n){return n-1|0}function i(n){return n+1|0}function c(n,t){return n+t}function f(){if(1===arguments.length){const n=arguments;return function(t){return c(t,n[0])}}return c(arguments[0],arguments[1])}function a(n,t){return n-t}function l(){if(1===arguments.length){const n=arguments;return function(t){return a(t,n[0])}}return a(arguments[0],arguments[1])}function s(n,t){return n*t}function g(){if(1===arguments.length){const n=arguments;return function(t){return s(t,n[0])}}return s(arguments[0],arguments[1])}function v(n,t){return n/t}function d(){if(1===arguments.length){const n=arguments;return function(t){return v(t,n[0])}}return v(arguments[0],arguments[1])}((n,t)=>{for(var e in(n=>{r(n,"__esModule",{value:!0})})(n),t)r(n,e,{get:t[e],enumerable:!0})})(t,{add:()=>f,clamp:()=>w,divide:()=>d,divideWithModulo:()=>E,gt:()=>m,gte:()=>b,lt:()=>T,lte:()=>O,modulo:()=>p,multiply:()=>g,placeholder:()=>u,pred:()=>o,subtract:()=>l,succ:()=>i});var h=e;function p(){if(1===arguments.length){const n=arguments;return function(t){return h(t,n[0])}}return h(arguments[0],arguments[1])}function _(n,t){return[n/t,e(0|n,0|t)]}function E(){if(1===arguments.length){const n=arguments;return function(t){return _(t,n[0])}}return _(arguments[0],arguments[1])}function y(n,t){return n>t}function m(){if(1===arguments.length){const n=arguments;return function(t){return y(t,n[0])}}return y(arguments[0],arguments[1])}function S(n,t){return n>=t}function b(){if(1===arguments.length){const n=arguments;return function(t){return S(t,n[0])}}return S(arguments[0],arguments[1])}function N(n,t){return n<t}function T(){if(1===arguments.length){const n=arguments;return function(t){return N(t,n[0])}}return N(arguments[0],arguments[1])}function A(n,t){return n<=t}function O(){if(1===arguments.length){const n=arguments;return function(t){return A(t,n[0])}}return A(arguments[0],arguments[1])}function P(n,t,r){return Math.min(Math.max(n,t),r)}function w(){if(2===arguments.length){const n=arguments;return function(t){return P(t,n[0],n[1])}}return P(arguments[0],arguments[1],arguments[2])}},287:(n,t)=>{var r=Object.defineProperty;function e(n,t,r){for(var e=new Array(r),u=0,o=t;u<r;)e[u]=n[o],u=u+1|0,o=o+1|0;return e}function u(n,t){for(;;){var r=t,o=n,i=o.length,c=0===i?1:i,f=c-r.length|0;if(0===f)return o.apply(null,r);if(f>=0)return function(n,t){return function(r){return u(n,t.concat([r]))}}(o,r);t=e(r,c,0|-f),n=o.apply(null,e(r,0,c))}}function o(n,t){var r=n.length;if(1===r)return n(t);switch(r){case 1:return n(t);case 2:return function(r){return n(t,r)};case 3:return function(r,e){return n(t,r,e)};case 4:return function(r,e,u){return n(t,r,e,u)};case 5:return function(r,e,u,o){return n(t,r,e,u,o)};case 6:return function(r,e,u,o,i){return n(t,r,e,u,o,i)};case 7:return function(r,e,u,o,i,c){return n(t,r,e,u,o,i,c)};default:return u(n,[t])}}((n,t)=>{for(var e in(n=>{r(n,"__esModule",{value:!0})})(n),t)r(n,e,{get:t[e],enumerable:!0})})(t,{None:()=>Q,Some:()=>H,contains:()=>X,filter:()=>M,flatMap:()=>N,fromExecution:()=>E,fromFalsy:()=>h,fromNullable:()=>d,fromPredicate:()=>_,fromPromise:()=>y,getExn:()=>D,getWithDefault:()=>I,isNone:()=>V,isSome:()=>q,map:()=>S,mapNullable:()=>P,mapWithDefault:()=>A,match:()=>G,placeholder:()=>v,tap:()=>L,toNullable:()=>j,toResult:()=>x,toUndefined:()=>k,zip:()=>z,zipWith:()=>U});var i=function(n,t){for(var r in n)t(r)};function c(n,t){if(n===t)return!0;var r=typeof n;if("string"===r||"number"===r||"boolean"===r||"undefined"===r||null===n)return!1;var e=typeof t;if("function"===r||"function"===e)throw{RE_EXN_ID:"Invalid_argument",_1:"equal: functional value",Error:new Error};if("number"===e||"undefined"===e||null===t)return!1;var u=0|n.TAG,o=0|t.TAG;if(248===u)return n[1]===t[1];if(251===u)throw{RE_EXN_ID:"Invalid_argument",_1:"equal: abstract value",Error:new Error};if(u!==o)return!1;var f=0|n.length;if(f!==(0|t.length))return!1;if(!Array.isArray(n)){if(n instanceof Date&&t instanceof Date)return!(n>t||n<t);var a={contents:!0};return i(n,(function(n){Object.prototype.hasOwnProperty.call(t,n)||(a.contents=!1)})),a.contents&&i(t,(function(r){Object.prototype.hasOwnProperty.call(n,r)&&c(t[r],n[r])||(a.contents=!1)})),a.contents}for(var l=0;;){var s=l;if(s===f)return!0;if(!c(n[s],t[s]))return!1;l=s+1|0}}function f(n){return void 0===n?{BS_PRIVATE_NESTED_SOME_NONE:0}:null!==n&&void 0!==n.BS_PRIVATE_NESTED_SOME_NONE?{BS_PRIVATE_NESTED_SOME_NONE:n.BS_PRIVATE_NESTED_SOME_NONE+1|0}:n}function a(n){if(null===n||void 0===n.BS_PRIVATE_NESTED_SOME_NONE)return n;var t=n.BS_PRIVATE_NESTED_SOME_NONE;return 0===t?void 0:{BS_PRIVATE_NESTED_SOME_NONE:t-1|0}}function l(n,t,r){return void 0!==n?r(a(n)):t}function s(n,t){if(void 0!==n)return t(a(n))}function g(n,t){return void 0!==n?a(n):t}function v(n){}function d(n){return null==n?void 0:f(n)}function h(n){if(n)return n}function p(n,t){return s(null==n?void 0:f(n),1===(r=function(n){if(t(n))return f(n)}).length?r:function(n){return o(r,n)});var r}function _(){if(1===arguments.length){const n=arguments;return function(t){return p(t,n[0])}}return p(arguments[0],arguments[1])}function E(n){try{return f(n(void 0))}catch(n){return}}function y(n){return n.then((function(n){return Promise.resolve(f(n))})).catch((function(n){return Promise.resolve(void 0)}))}var m=function(n,t){if(void 0!==n)return f(t(a(n)))};function S(){if(1===arguments.length){const n=arguments;return function(t){return m(t,n[0])}}return m(arguments[0],arguments[1])}var b=s;function N(){if(1===arguments.length){const n=arguments;return function(t){return b(t,n[0])}}return b(arguments[0],arguments[1])}var T=l;function A(){if(2===arguments.length){const n=arguments;return function(t){return T(t,n[0],n[1])}}return T(arguments[0],arguments[1],arguments[2])}function O(n,t){if(void 0!==n)return null==(r=t(a(n)))?void 0:f(r);var r}function P(){if(1===arguments.length){const n=arguments;return function(t){return O(t,n[0])}}return O(arguments[0],arguments[1])}function w(n,t){return s(n,(function(n){if(t(n))return f(n)}))}function M(){if(1===arguments.length){const n=arguments;return function(t){return w(t,n[0])}}return w(arguments[0],arguments[1])}var R=g;function I(){if(1===arguments.length){const n=arguments;return function(t){return R(t,n[0])}}return R(arguments[0],arguments[1])}var D=function(n){if(void 0!==n)return a(n);throw{RE_EXN_ID:"Not_found",Error:new Error}};function j(n){return g(n,null)}function k(n){return g(n,void 0)}function C(n,t){return void 0!==n?{TAG:0,_0:a(n)}:{TAG:1,_0:t}}function x(){if(1===arguments.length){const n=arguments;return function(t){return C(t,n[0])}}return C(arguments[0],arguments[1])}function B(n,t,r){return void 0!==n?t(a(n)):r(void 0)}function G(){if(2===arguments.length){const n=arguments;return function(t){return B(t,n[0],n[1])}}return B(arguments[0],arguments[1],arguments[2])}var V=function(n){return void 0===n},q=function(n){return void 0!==n};function F(n,t){return void 0!==n?(t(a(n)),n):n}function L(){if(1===arguments.length){const n=arguments;return function(t){return F(t,n[0])}}return F(arguments[0],arguments[1])}function W(n,t){return l(n,!1,(function(n){return c(n,t)}))}function X(){if(1===arguments.length){const n=arguments;return function(t){return W(t,n[0])}}return W(arguments[0],arguments[1])}function J(n,t){if(void 0!==n&&void 0!==t)return[a(n),a(t)]}function z(){if(1===arguments.length){const n=arguments;return function(t){return J(t,n[0])}}return J(arguments[0],arguments[1])}function K(n,t,r){if(void 0!==n&&void 0!==t)return f(r(a(n),a(t)))}function U(){if(2===arguments.length){const n=arguments;return function(t){return K(t,n[0],n[1])}}return K(arguments[0],arguments[1],arguments[2])}var H=n=>n,Q=void 0},619:(n,t)=>{var r=Object.defineProperty;function e(n,t,r){for(var e=new Array(r),u=0,o=t;u<r;)e[u]=n[o],u=u+1|0,o=o+1|0;return e}function u(n,t){for(;;){var r=t,o=n,i=o.length,c=0===i?1:i,f=c-r.length|0;if(0===f)return o.apply(null,r);if(f>=0)return function(n,t){return function(r){return u(n,t.concat([r]))}}(o,r);t=e(r,c,0|-f),n=o.apply(null,e(r,0,c))}}function o(n,t){var r=n.length;if(1===r)return n(t);switch(r){case 1:return n(t);case 2:return function(r){return n(t,r)};case 3:return function(r,e){return n(t,r,e)};case 4:return function(r,e,u){return n(t,r,e,u)};case 5:return function(r,e,u,o){return n(t,r,e,u,o)};case 6:return function(r,e,u,o,i){return n(t,r,e,u,o,i)};case 7:return function(r,e,u,o,i,c){return n(t,r,e,u,o,i,c)};default:return u(n,[t])}}((n,t)=>{for(var e in(n=>{r(n,"__esModule",{value:!0})})(n),t)r(n,e,{get:t[e],enumerable:!0})})(t,{Error:()=>Y,Ok:()=>Q,catchError:()=>z,flatMap:()=>O,flip:()=>H,fromExecution:()=>y,fromFalsy:()=>p,fromNullable:()=>d,fromPredicate:()=>E,fromPromise:()=>m,getExn:()=>P,getWithDefault:()=>M,handleError:()=>L,isError:()=>C,isOk:()=>x,map:()=>b,mapError:()=>X,mapWithDefault:()=>T,match:()=>k,placeholder:()=>g,recover:()=>U,tap:()=>G,tapError:()=>q,toNullable:()=>I,toOption:()=>D,toUndefined:()=>R});var i={contents:0};function c(n){return i.contents=i.contents+1|0,n+"/"+i.contents}var f=c("Caml_js_exceptions.Error"),a=f;function l(n,t){return 0===n.TAG?t(n._0):{TAG:1,_0:n._0}}function s(n,t){return 0===n.TAG?n._0:t}function g(n){}function v(n,t){return null==n?{TAG:1,_0:t}:{TAG:0,_0:n}}function d(){if(1===arguments.length){const n=arguments;return function(t){return v(t,n[0])}}return v(arguments[0],arguments[1])}function h(n,t){return n?{TAG:0,_0:n}:{TAG:1,_0:t}}function p(){if(1===arguments.length){const n=arguments;return function(t){return h(t,n[0])}}return h(arguments[0],arguments[1])}function _(n,t,r){return l(d(n,r),1===(e=function(n){return t(n)?{TAG:0,_0:n}:{TAG:1,_0:r}}).length?e:function(n){return o(e,n)});var e}function E(){if(2===arguments.length){const n=arguments;return function(t){return _(t,n[0],n[1])}}return _(arguments[0],arguments[1],arguments[2])}function y(n){try{return{TAG:0,_0:n(void 0)}}catch(n){var t=function(n){return null!=n&&"string"==typeof n.RE_EXN_ID}(r=n)?r:{RE_EXN_ID:f,_1:r};if(t.RE_EXN_ID===a)return{TAG:1,_0:t._1};throw t}var r}function m(n){return n.then((function(n){return Promise.resolve({TAG:0,_0:n})})).catch((function(n){return Promise.resolve({TAG:1,_0:n})}))}var S=function(n,t){return 0===n.TAG?{TAG:0,_0:t(n._0)}:{TAG:1,_0:n._0}};function b(){if(1===arguments.length){const n=arguments;return function(t){return S(t,n[0])}}return S(arguments[0],arguments[1])}var N=function(n,t,r){return 0===n.TAG?r(n._0):t};function T(){if(2===arguments.length){const n=arguments;return function(t){return N(t,n[0],n[1])}}return N(arguments[0],arguments[1],arguments[2])}var A=l;function O(){if(1===arguments.length){const n=arguments;return function(t){return A(t,n[0])}}return A(arguments[0],arguments[1])}var P=function(n){if(0===n.TAG)return n._0;throw{RE_EXN_ID:"Not_found",Error:new Error}},w=s;function M(){if(1===arguments.length){const n=arguments;return function(t){return w(t,n[0])}}return w(arguments[0],arguments[1])}function R(n){return s(n,void 0)}function I(n){return s(n,null)}function D(n){if(0===n.TAG)return void 0===(t=n._0)?{BS_PRIVATE_NESTED_SOME_NONE:0}:null!==t&&void 0!==t.BS_PRIVATE_NESTED_SOME_NONE?{BS_PRIVATE_NESTED_SOME_NONE:t.BS_PRIVATE_NESTED_SOME_NONE+1|0}:t;var t}function j(n,t,r){return 0===n.TAG?t(n._0):r(n._0)}function k(){if(2===arguments.length){const n=arguments;return function(t){return j(t,n[0],n[1])}}return j(arguments[0],arguments[1],arguments[2])}var C=function(n){return 0!==n.TAG},x=function(n){return 0===n.TAG};function B(n,t){return 0!==n.TAG||t(n._0),n}function G(){if(1===arguments.length){const n=arguments;return function(t){return B(t,n[0])}}return B(arguments[0],arguments[1])}function V(n,t){return 0===n.TAG||t(n._0),n}function q(){if(1===arguments.length){const n=arguments;return function(t){return V(t,n[0])}}return V(arguments[0],arguments[1])}function F(n,t){return 0===n.TAG?n:{TAG:0,_0:t(n._0)}}function L(){if(1===arguments.length){const n=arguments;return function(t){return F(t,n[0])}}return F(arguments[0],arguments[1])}function W(n,t){return 0===n.TAG?n:{TAG:1,_0:t(n._0)}}function X(){if(1===arguments.length){const n=arguments;return function(t){return W(t,n[0])}}return W(arguments[0],arguments[1])}function J(n,t){return 0===n.TAG?n:t(n._0)}function z(){if(1===arguments.length){const n=arguments;return function(t){return J(t,n[0])}}return J(arguments[0],arguments[1])}function K(n,t){return z(n,(function(n){return{TAG:0,_0:t}}))}function U(){if(1===arguments.length){const n=arguments;return function(t){return K(t,n[0])}}return K(arguments[0],arguments[1])}function H(n){return 0===n.TAG?{TAG:1,_0:n._0}:{TAG:0,_0:n._0}}var Q=n=>({TAG:0,_0:n}),Y=n=>({TAG:1,_0:n})},59:(n,t)=>{var r=Object.defineProperty;function e(n){return void 0===n?{BS_PRIVATE_NESTED_SOME_NONE:0}:null!==n&&void 0!==n.BS_PRIVATE_NESTED_SOME_NONE?{BS_PRIVATE_NESTED_SOME_NONE:n.BS_PRIVATE_NESTED_SOME_NONE+1|0}:n}function u(n){return void 0===n?void 0:e(n)}function o(n){}function i(n){return String(n)}function c(n){return n.length}function f(n,t){return n.concat(t)}function a(){if(1===arguments.length){const n=arguments;return function(t){return f(t,n[0])}}return f(arguments[0],arguments[1])}function l(n,t){return n.concat(t)}function s(){if(1===arguments.length){const n=arguments;return function(t){return l(t,n[0])}}return l(arguments[0],arguments[1])}function g(n,t){return t.concat(n)}function v(){if(1===arguments.length){const n=arguments;return function(t){return g(t,n[0])}}return g(arguments[0],arguments[1])}function d(n,t,r){return n.slice(t,r)}function h(){if(2===arguments.length){const n=arguments;return function(t){return d(t,n[0],n[1])}}return d(arguments[0],arguments[1],arguments[2])}function p(n,t){return n.slice(t)}function _(){if(1===arguments.length){const n=arguments;return function(t){return p(t,n[0])}}return p(arguments[0],arguments[1])}function E(n){return n.toLowerCase()}function y(n){return n.toUpperCase()}function m(n){return n.trim()}function S(n){return n.trimStart()}function b(n){return n.trimEnd()}function N(n){return 0===n.length}function T(n){return n.length>0}function A(n,t){return n.split(t)}function O(){if(1===arguments.length){const n=arguments;return function(t){return A(t,n[0])}}return A(arguments[0],arguments[1])}function P(n,t){return n.split(t)}function w(){if(1===arguments.length){const n=arguments;return function(t){return P(t,n[0])}}return P(arguments[0],arguments[1])}function M(n,t){return[n.slice(0,t),n.slice(t)]}function R(){if(1===arguments.length){const n=arguments;return function(t){return M(t,n[0])}}return M(arguments[0],arguments[1])}function I(n,t){return n.includes(t)}function D(){if(1===arguments.length){const n=arguments;return function(t){return I(t,n[0])}}return I(arguments[0],arguments[1])}function j(n,t,r){return n.replace(t,r)}function k(){if(2===arguments.length){const n=arguments;return function(t){return j(t,n[0],n[1])}}return j(arguments[0],arguments[1],arguments[2])}function C(n,t,r){var e=n.split(t);return function(n){for(var t="",u=0,o=n.length;u<o;++u)i=t,c=n[u],f=void 0,f=u<(e.length-1|0)?c.concat(r):c,t=i.concat(f);var i,c,f;return t}(e)}function x(){if(2===arguments.length){const n=arguments;return function(t){return C(t,n[0],n[1])}}return C(arguments[0],arguments[1],arguments[2])}function B(n,t,r){return n.replace(t,r)}function G(){if(2===arguments.length){const n=arguments;return function(t){return B(t,n[0],n[1])}}return B(arguments[0],arguments[1],arguments[2])}function V(n,t){return n.replace(t,"")}function q(){if(1===arguments.length){const n=arguments;return function(t){return V(t,n[0])}}return V(arguments[0],arguments[1])}function F(n,t){return x(n,t,"")}function L(){if(1===arguments.length){const n=arguments;return function(t){return F(t,n[0])}}return F(arguments[0],arguments[1])}function W(n,t){var r=n.search(t);return r<0?void 0:r}function X(){if(1===arguments.length){const n=arguments;return function(t){return W(t,n[0])}}return W(arguments[0],arguments[1])}function J(n,t){return null===(r=n.match(t))?void 0:e(r);var r}function z(){if(1===arguments.length){const n=arguments;return function(t){return J(t,n[0])}}return J(arguments[0],arguments[1])}function K(n,t){return n.repeat(t)}function U(){if(1===arguments.length){const n=arguments;return function(t){return K(t,n[0])}}return K(arguments[0],arguments[1])}function H(n,t){var r=n.indexOf(t);return r<0?void 0:r}function Q(){if(1===arguments.length){const n=arguments;return function(t){return H(t,n[0])}}return H(arguments[0],arguments[1])}function Y(n,t){var r=n.lastIndexOf(t);return r<0?void 0:r}function Z(){if(1===arguments.length){const n=arguments;return function(t){return Y(t,n[0])}}return Y(arguments[0],arguments[1])}function $(n,t){return n.endsWith(t)}function nn(){if(1===arguments.length){const n=arguments;return function(t){return $(t,n[0])}}return $(arguments[0],arguments[1])}function tn(n,t){return n.startsWith(t)}function rn(){if(1===arguments.length){const n=arguments;return function(t){return tn(t,n[0])}}return tn(arguments[0],arguments[1])}function en(n,t){return n[t]}function un(){if(1===arguments.length){const n=arguments;return function(t){return en(t,n[0])}}return en(arguments[0],arguments[1])}function on(n,t){return u(n[t])}function cn(){if(1===arguments.length){const n=arguments;return function(t){return on(t,n[0])}}return on(arguments[0],arguments[1])}function fn(n){return function(t){if(t<=0)return[];for(var r=new Array(t),e=0;e<t;++e)r[e]=n[e];return r}(n.length)}function an(n){return u(n[0])}function ln(n){return u(n[n.length-1|0])}((n,t)=>{for(var e in(n=>{r(n,"__esModule",{value:!0})})(n),t)r(n,e,{get:t[e],enumerable:!0})})(t,{append:()=>s,concat:()=>a,endsWith:()=>nn,get:()=>cn,getUnsafe:()=>un,head:()=>an,includes:()=>D,indexOf:()=>Q,isEmpty:()=>N,isNotEmpty:()=>T,last:()=>ln,lastIndexOf:()=>Z,length:()=>c,make:()=>i,match:()=>z,placeholder:()=>o,prepend:()=>v,remove:()=>q,removeAll:()=>L,repeat:()=>U,replace:()=>k,replaceAll:()=>x,replaceByRe:()=>G,search:()=>X,slice:()=>h,sliceToEnd:()=>_,split:()=>O,splitAt:()=>R,splitByRe:()=>w,startsWith:()=>rn,toArray:()=>fn,toLowerCase:()=>E,toUpperCase:()=>y,trim:()=>m,trimEnd:()=>b,trimStart:()=>S})},661:(n,t)=>{var r=Object.defineProperty;function e(){let n=arguments;return function(){let t=n[0].apply(null,arguments);for(let r=1,e=n.length;r<e;r++)t=n[r](t);return t}}((n,t)=>{for(var e in(n=>{r(n,"__esModule",{value:!0})})(n),t)r(n,e,{get:t[e],enumerable:!0})})(t,{flow:()=>e})},93:(n,t,r)=>{var e=Object.create,u=Object.defineProperty,o=Object.getOwnPropertyDescriptor,i=Object.getOwnPropertyNames,c=Object.getPrototypeOf,f=Object.prototype.hasOwnProperty,a=n=>u(n,"__esModule",{value:!0}),l=n=>((n,t,r)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let e of i(t))f.call(n,e)||"default"===e||u(n,e,{get:()=>t[e],enumerable:!(r=o(t,e))||r.enumerable});return n})(a(u(null!=n?e(c(n)):{},"default",n&&n.__esModule&&"default"in n?{get:()=>n.default,enumerable:!0}:{value:n,enumerable:!0})),n);((n,t)=>{for(var r in a(n),t)u(n,r,{get:t[r],enumerable:!0})})(t,{A:()=>d,B:()=>m,D:()=>y,F:()=>v,G:()=>p,N:()=>S,O:()=>_,R:()=>h,S:()=>E,flow:()=>g.flow,pipe:()=>s.pipe});var s=l(r(789)),g=l(r(661)),v=l(r(654)),d=l(r(523)),h=l(r(619)),p=l(r(564)),_=l(r(287)),E=l(r(59)),y=l(r(198)),m=l(r(982)),S=l(r(327))},789:(n,t)=>{var r=Object.defineProperty;function e(){let n=arguments[0];for(let t=1,r=arguments.length;t<r;t++)n=arguments[t](n);return n}((n,t)=>{for(var e in(n=>{r(n,"__esModule",{value:!0})})(n),t)r(n,e,{get:t[e],enumerable:!0})})(t,{pipe:()=>e})},998:(n,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=joplin},143:(n,t)=>{"use strict";var r,e,u,o,i,c,f,a,l,s;Object.defineProperty(t,"__esModule",{value:!0}),t.ContentScriptType=t.SettingStorage=t.AppType=t.SettingItemSubType=t.SettingItemType=t.ToolbarButtonLocation=t.MenuItemLocation=t.ModelType=t.ImportModuleOutputFormat=t.FileSystemItem=void 0,t.isContextMenuItemLocation=function(n){return[o.Context,o.NoteListContextMenu,o.EditorContextMenu,o.FolderContextMenu,o.TagContextMenu].includes(n)},function(n){n.File="file",n.Directory="directory"}(r||(t.FileSystemItem=r={})),function(n){n.Markdown="md",n.Html="html"}(e||(t.ImportModuleOutputFormat=e={})),function(n){n[n.Note=1]="Note",n[n.Folder=2]="Folder",n[n.Setting=3]="Setting",n[n.Resource=4]="Resource",n[n.Tag=5]="Tag",n[n.NoteTag=6]="NoteTag",n[n.Search=7]="Search",n[n.Alarm=8]="Alarm",n[n.MasterKey=9]="MasterKey",n[n.ItemChange=10]="ItemChange",n[n.NoteResource=11]="NoteResource",n[n.ResourceLocalState=12]="ResourceLocalState",n[n.Revision=13]="Revision",n[n.Migration=14]="Migration",n[n.SmartFilter=15]="SmartFilter",n[n.Command=16]="Command"}(u||(t.ModelType=u={})),function(n){n.File="file",n.Edit="edit",n.View="view",n.Note="note",n.Tools="tools",n.Help="help",n.Context="context",n.NoteListContextMenu="noteListContextMenu",n.EditorContextMenu="editorContextMenu",n.FolderContextMenu="folderContextMenu",n.TagContextMenu="tagContextMenu"}(o||(t.MenuItemLocation=o={})),function(n){n.NoteToolbar="noteToolbar",n.EditorToolbar="editorToolbar"}(i||(t.ToolbarButtonLocation=i={})),function(n){n[n.Int=1]="Int",n[n.String=2]="String",n[n.Bool=3]="Bool",n[n.Array=4]="Array",n[n.Object=5]="Object",n[n.Button=6]="Button"}(c||(t.SettingItemType=c={})),function(n){n.FilePathAndArgs="file_path_and_args",n.FilePath="file_path",n.DirectoryPath="directory_path"}(f||(t.SettingItemSubType=f={})),function(n){n.Desktop="desktop",n.Mobile="mobile",n.Cli="cli"}(a||(t.AppType=a={})),function(n){n[n.Database=1]="Database",n[n.File=2]="File"}(l||(t.SettingStorage=l={})),function(n){n.MarkdownItPlugin="markdownItPlugin",n.CodeMirrorPlugin="codeMirrorPlugin"}(s||(t.ContentScriptType=s={}))},928:(n,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.CmExtensionClient=void 0;const e=r(334);class u{static create(n){return new u(n)}constructor(n){this.call=n.call}async ping(){await this.call(e.PingRequest.of())}}t.CmExtensionClient=u},334:(n,t)=>{"use strict";var r,e;Object.defineProperty(t,"__esModule",{value:!0}),t.PingResponse=t.PingRequest=void 0,function(n){n.of=function(){return{kind:"ping"}}}(r||(t.PingRequest=r={})),function(n){n.of=function(){return{kind:"ping"}}}(e||(t.PingResponse=e={}))},959:(n,t)=>{"use strict";var r;Object.defineProperty(t,"__esModule",{value:!0}),t.JoplinCommands=void 0,function(n){n.callCodeMirrorExtension=async function(n,t,r){return await n.execute("editor.execCommand",{name:t,args:[r]})}}(r||(t.JoplinCommands=r={}))},500:(n,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.JoplinSettings=void 0;const e=r(106);var u;!function(n){n.register=async function(n,t,r){await n.registerSection(t,e.Dicts.deleteKey(r,"settings")),await n.registerSettings(e.Dicts.map(r.settings,(n=>({...n,section:t}))))}}(u||(t.JoplinSettings=u={}))},106:(n,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.Strings=t.Results=t.Options=t.Numbers=t.Guards=t.Functions=t.Dicts=t.Bools=t.Arrays=void 0;var e=r(93);Object.defineProperty(t,"Arrays",{enumerable:!0,get:function(){return e.A}}),Object.defineProperty(t,"Bools",{enumerable:!0,get:function(){return e.B}}),Object.defineProperty(t,"Dicts",{enumerable:!0,get:function(){return e.D}}),Object.defineProperty(t,"Functions",{enumerable:!0,get:function(){return e.F}}),Object.defineProperty(t,"Guards",{enumerable:!0,get:function(){return e.G}}),Object.defineProperty(t,"Numbers",{enumerable:!0,get:function(){return e.N}}),Object.defineProperty(t,"Options",{enumerable:!0,get:function(){return e.O}}),Object.defineProperty(t,"Results",{enumerable:!0,get:function(){return e.R}}),Object.defineProperty(t,"Strings",{enumerable:!0,get:function(){return e.S}})},4:(n,t)=>{"use strict";var r,e,u,o;Object.defineProperty(t,"__esModule",{value:!0}),t.PingResponse=t.PingRequest=t.GetSettingsResponse=t.GetSettingsRequest=void 0,function(n){n.of=function(){return{kind:"getSettings"}}}(r||(t.GetSettingsRequest=r={})),function(n){n.of=function(n){return{kind:"getSettings",settings:n}}}(e||(t.GetSettingsResponse=e={})),function(n){n.of=function(){return{kind:"ping"}}}(u||(t.PingRequest=u={})),function(n){n.of=function(){return{kind:"ping"}}}(o||(t.PingResponse=o={}))},192:(n,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.JoplinPlugin=void 0;const e=r(143),u=r(500);var o;!function(n){n.pluginName="BetterCodeBlocks",n.register=async function({cmExtensionClient:t,contentScript:r,joplin:o,requestHandler:i,settingSection:c}){return await o.plugins.register({onStart:async()=>{await u.JoplinSettings.register(o.settings,n.pluginName,c),await o.contentScripts.register(e.ContentScriptType.CodeMirrorPlugin,r.id,r.path),await o.contentScripts.onMessage(r.id,function(n){return t=>n.handle(t)}(i)),await o.settings.onChange(function(n){return()=>{(async()=>{await n.ping()})()}}(t))}})}}(o||(t.JoplinPlugin=o={}))},73:(n,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.RequestHandler=void 0;const e=r(4);class u{static create(n){return new u(n)}constructor(n){this.handle=n=>this[n.kind](n),this.pluginSettingsProvider=n.pluginSettingsProvider}async ping(n){return Promise.resolve(e.PingResponse.of())}async getSettings(n){return e.GetSettingsResponse.of(await this.pluginSettingsProvider.provide())}}t.RequestHandler=u},61:(n,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.PluginSettingSection=void 0;const e=r(143);t.PluginSettingSection={label:"Better Code Blocks",iconName:"fa fa-solid fa-code",settings:{completion:{type:e.SettingItemType.String,public:!0,value:"enabled",isEnum:!0,options:{enabled:"Enabled",disabled:"Disabled"},label:"Autocompletion",description:"Enables/disables automatic completion of code blocks when [Enter] is pressed"},rendering:{type:e.SettingItemType.String,public:!0,value:"enabled",isEnum:!0,options:{enabled:"Enabled",disabled:"Disabled"},label:"Rendering",description:"Enables/disables rendering of code blocks"},selectAllCapturing:{type:e.SettingItemType.String,public:!0,value:"enabled",isEnum:!0,options:{enabled:"Select current code block",disabled:"Select everything"},label:"Behavior of [Select All] inside code blocks",description:"Changes the behavior of [Select All] while the cursor is inside code blocks"},renderLayout:{type:e.SettingItemType.String,public:!0,value:"minimal",isEnum:!0,options:{minimal:"Minimal",standard:"Standard"},label:"Render layout",description:"Changes the layout of rendered code blocks"},cornerStyle:{type:e.SettingItemType.String,public:!0,value:"square",isEnum:!0,options:{square:"Square",round:"Round"},label:"Corner style",description:"Changes the border style of rendered code blocks"},copyFormat:{type:e.SettingItemType.String,public:!0,value:"code",isEnum:!0,options:{code:"Copy code",fencedCode:"Copy code and fences"},label:"Copy button behavior",description:"Controls what's copied when the [Copy Code] button is clicked"},completedLanguages:{type:e.SettingItemType.String,public:!0,advanced:!0,value:["bash","c","c#","c++","clojure","clojurescript","coffeescript","cpp","crystal","cs","csharp","css","d","dart","diff","dockerfile","ecmascript","elm","erlang","f#","forth","fortran","fsharp","gherkin","go","groovy","haskell","haxe","html","http","ini","java","javascript","jinja2","js","json","jsx","julia","kotlin","latex","less","lisp","lua","markdown","mathematica","mysql","node","objc","objc++","objective-c","objective-c++","ocaml","pascal","perl","php","plsql","postgresql","powershell","properties","protobuf","python","r","rake","rb","rscript","rss","ruby","rust","sass","scala","scheme","scss","sh","shell","smalltalk","sql","sqlite","swift","tcl","toml","ts","tsx","typescript","vb.net","vbscript","verilog","xhtml","xml","yaml","yml","zsh"].join(","),label:"Autocompleted languages (comma-separated list)",description:"Enables native autocompletion for the given languages"},excludedLanguages:{type:e.SettingItemType.String,public:!0,advanced:!0,value:"",label:"Excluded languages (comma-separated list)",description:"Disables rendering of code blocks for specific languages"}}}},342:(n,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.PluginSettingsProvider=void 0;class r{static create(n){return new r(n)}constructor(n){this.joplinSettings=n.joplinSettings}async provide(){const n=await this.provideRawSettings();return{completedLanguages:e(n.completedLanguages),completion:n.completion,copyFormat:n.copyFormat,cornerStyle:n.cornerStyle,excludedLanguages:e(n.excludedLanguages),rendering:n.rendering,renderLayout:n.renderLayout,selectAllCapturing:n.selectAllCapturing}}async provideRawSettings(){return{completedLanguages:await this.getRawSetting("completedLanguages"),completion:await this.getRawSetting("completion"),copyFormat:await this.getRawSetting("copyFormat"),cornerStyle:await this.getRawSetting("cornerStyle"),excludedLanguages:await this.getRawSetting("excludedLanguages"),rendering:await this.getRawSetting("rendering"),renderLayout:await this.getRawSetting("renderLayout"),selectAllCapturing:await this.getRawSetting("selectAllCapturing")}}async getRawSetting(n){return await this.joplinSettings.value(n)}}function e(n){return function(n){return n.map((n=>n.trim()))}(function(n){return n.split(",")}(n))}t.PluginSettingsProvider=r}},t={};function r(e){var u=t[e];if(void 0!==u)return u.exports;var o=t[e]={exports:{}};return n[e](o,o.exports,r),o.exports}(()=>{"use strict";const n=r(998),t=r(959),e=r(928),u=r(73),o=r(192),i=r(61),c=r(342);o.JoplinPlugin.register({cmExtensionClient:e.CmExtensionClient.create({call:r=>t.JoplinCommands.callCodeMirrorExtension(n.default.commands,o.JoplinPlugin.pluginName,r)}),contentScript:{id:o.JoplinPlugin.pluginName,path:"./contentScriptDefinition.js"},joplin:{contentScripts:n.default.contentScripts,plugins:n.default.plugins,settings:n.default.settings},requestHandler:u.RequestHandler.create({pluginSettingsProvider:c.PluginSettingsProvider.create({joplinSettings:n.default.settings})}),settingSection:i.PluginSettingSection})})()})();