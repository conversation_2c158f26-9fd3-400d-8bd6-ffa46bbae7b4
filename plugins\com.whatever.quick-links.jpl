contentScript/index.js                                                                              000644                  0000011064  14577127035 012225  0                                                                                                    ustar 00                                                                000000  000000                                                                                                                                                                         (()=>{"use strict";var t={400:function(t,e){var n=this&&this.__awaiter||function(t,e,n,o){return new(n||(n=Promise))((function(i,s){function l(t){try{c(o.next(t))}catch(t){s(t)}}function r(t){try{c(o.throw(t))}catch(t){s(t)}}function c(t){var e;t.done?i(t.value):(e=t.value,e instanceof n?e:new n((function(t){t(e)}))).then(l,r)}c((o=o.apply(t,e||[])).next())}))};Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t,e){function o(e,o){let i="New Note";o&&(i="New Task");return{text:e,hint:(i,s,l)=>n(this,void 0,void 0,(function*(){const n=l.from||s.from;n.ch-=2;const r=yield t.postMessage({command:"createNote",title:e,todo:o});i.replaceRange(`[${e}](:/${r.newNote.id})`,n,i.getCursor(),"complete")})),render:(t,n,o)=>{const s=t.ownerDocument.createElement("div");s.setAttribute("style","width: 100%; display:table;"),t.appendChild(s),s.innerHTML=`\n\t\t\t\t\t<div style="display:table-cell; padding-right: 5px">${e}</div>\n\t\t\t\t\t<div style="display:table-cell; text-align: right;"><small><em>${i}</em></small></div>\n\t\t\t\t\t`}}}const i=e=>n(this,void 0,void 0,(function*(){const i=yield t.postMessage({command:"getNotes",prefix:e});let s=[];const l=i.notes;for(let t=0;t<l.length;t++){const e=l[t],o={text:e.title,hint:(t,o,s)=>n(this,void 0,void 0,(function*(){const n=s.from||o.from;if(n.ch-=2,t.replaceRange(`[${e.title}](:/${e.id})`,n,t.getCursor(),"complete"),i.selectText){const o=Object.assign({},n),i=Object.assign({},n);o.ch+=1,i.ch+=1+e.title.length,t.setSelection(o,i)}}))};if(i.showFolders){const t=e.folder?e.folder:"unknown";o.render=(n,o,i)=>{const s=n.ownerDocument.createElement("div");s.setAttribute("style","width: 100%; display:table;"),n.appendChild(s),s.innerHTML=`\n\t\t\t\t\t<div style="display:table-cell; padding-right: 5px">${e.title}</div>\n\t\t\t\t\t<div style="display:table-cell; text-align: right;"><small><em>In ${t}</em></small></div>\n\t\t\t\t\t`}}else o.displayText=e.title;s.push(o)}return i.allowNewNotes&&e&&(s.push(o(e,!1)),s.push(o(e,!0))),s}));e.defineOption("quickLinks",!1,(function(t,o,s){o&&t.on("inputRead",(function(o,s){return n(this,void 0,void 0,(function*(){if(!o.state.completionActive&&t.getTokenAt(t.getCursor()).string.startsWith("@@")){const n={line:s.from.line,ch:s.from.ch+1},o=function(t,e){const o=t.getCursor();let l=t.getRange(n,o)||"";i(l).then((t=>{e({list:t,from:{line:s.from.line,ch:s.from.ch+1},to:{line:s.to.line,ch:s.to.ch+1}})}))};setTimeout((function(){e.showHint(t,o,{completeSingle:!1,closeOnUnfocus:!0,async:!0,closeCharacters:/[()\[\]{};:>,]/})}),10)}}))}))}))}},36:function(t,e,n){var o=this&&this.__awaiter||function(t,e,n,o){return new(n||(n=Promise))((function(i,s){function l(t){try{c(o.next(t))}catch(t){s(t)}}function r(t){try{c(o.throw(t))}catch(t){s(t)}}function c(t){var e;t.done?i(t.value):(e=t.value,e instanceof n?e:new n((function(t){t(e)}))).then(l,r)}c((o=o.apply(t,e||[])).next())}))};Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t,e){const{autocompletion:i,insertCompletionText:s}=n(808),{EditorSelection:l}=n(864),r=e=>o(this,void 0,void 0,(function*(){var n;const i=e.matchBefore(/[@][@][^()\[\]{};:>,\n]*/);if(!i||i.from===i.to&&!e.explicit)return null;const r=i.text.substring(2),c=yield t.postMessage({command:"getNotes",prefix:r}),a=(t,e)=>(n,o,i,r)=>{const a=`[${t}](:/${e})`;if(n.dispatch(s(n.state,a,i,r)),c.selectText){const e=i+1,o=e+t.length;n.dispatch({selection:l.range(e,o)})}},d=c.notes,u=[];for(const t of d)u.push({apply:a(t.title,t.id),label:t.title,detail:c.showFolders?`In ${null!==(n=t.folder)&&void 0!==n?n:"unknown"}`:void 0});const f=e=>{const n=r,i=e?"New Task":"New Note";u.push({label:i,detail:`"${n}"`,apply:(i,s,l,r)=>o(this,void 0,void 0,(function*(){const o=yield t.postMessage({command:"createNote",title:n,todo:e});a(n,o.newNote.id)(i,s,l,r)}))})};return c.allowNewNotes&&r.length>0&&(f(!0),f(!1)),{from:i.from,options:u,filter:!1}}));let c;c=e.joplinExtensions?e.joplinExtensions.completionSource(r):i({override:[r]}),e.addExtension([c,i({tooltipClass:()=>"quick-links-completions"})])}},424:(t,e,n)=>{Object.defineProperty(e,"__esModule",{value:!0});const o=n(400),i=n(36);t.exports={default:function(t){return{plugin:e=>e.cm6?(0,i.default)(t,e):(0,o.default)(t,e),codeMirrorResources:["addon/hint/show-hint"],codeMirrorOptions:{quickLinks:!0},assets:function(){return[{name:"./show-hint.css"}]}}}}},808:t=>{t.exports=require("@codemirror/autocomplete")},864:t=>{t.exports=require("@codemirror/state")}},e={},n=function n(o){var i=e[o];if(void 0!==i)return i.exports;var s=e[o]={exports:{}};return t[o].call(s.exports,s,s.exports,n),s.exports}(424);exports.default=n.default})();                                                                                                                                                                                                                                                                                                                                                                                                                                                                            contentScript/show-hint.css                                                                         000644                  0000001454  14577127034 013213  0                                                                                                    ustar 00                                                                000000  000000                                                                                                                                                                         .CodeMirror-hints, .quick-links-completions.cm-tooltip {
  position: absolute;
  z-index: 10;
  overflow: hidden;
  list-style: none;

  margin: 0;
  padding: 2px;

  -webkit-box-shadow: 2px 3px 5px rgba(0,0,0,.2);
  -moz-box-shadow: 2px 3px 5px rgba(0,0,0,.2);
  box-shadow: 2px 3px 5px rgba(0,0,0,.2);
  border-radius: 3px;
  border: 1px solid silver;

  background-color: white;
  font-size: 90%;
  font-family: monospace;

  max-height: 20em;
  overflow-y: auto;
}

.quick-links-completions.cm-tooltip li {
  font-family: sans-serif;
}

.CodeMirror-hint, .quick-links-completions li {
  margin: 0;
  padding: 0 4px;
  border-radius: 2px;
  white-space: pre;
  color: black;
  cursor: pointer;
}

li.CodeMirror-hint-active, .quick-links-completions li[aria-selected] {
  background: #08f;
  color: white;
}


                                                                                                                                                                                                                    index.js                                                                                            000644                  0000010706  14577127034 007367  0                                                                                                    ustar 00                                                                000000  000000                                                                                                                                                                         (()=>{"use strict";var t={632:(t,e)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.default=joplin},808:(t,e)=>{var i,o,n,l,r,d;Object.defineProperty(e,"__esModule",{value:!0}),e.ContentScriptType=e.SettingItemType=e.ToolbarButtonLocation=e.isContextMenuItemLocation=e.MenuItemLocation=e.ImportModuleOutputFormat=e.FileSystemItem=void 0,(d=e.FileSystemItem||(e.FileSystemItem={})).File="file",d.Directory="directory",(r=e.ImportModuleOutputFormat||(e.ImportModuleOutputFormat={})).Markdown="md",r.Html="html",function(t){t.File="file",t.Edit="edit",t.View="view",t.Note="note",t.Tools="tools",t.Help="help",t.Context="context",t.NoteListContextMenu="noteListContextMenu",t.EditorContextMenu="editorContextMenu",t.FolderContextMenu="folderContextMenu",t.TagContextMenu="tagContextMenu"}(i=e.MenuItemLocation||(e.MenuItemLocation={})),e.isContextMenuItemLocation=function(t){return[i.Context,i.NoteListContextMenu,i.EditorContextMenu,i.FolderContextMenu,i.TagContextMenu].includes(t)},(l=e.ToolbarButtonLocation||(e.ToolbarButtonLocation={})).NoteToolbar="noteToolbar",l.EditorToolbar="editorToolbar",(n=e.SettingItemType||(e.SettingItemType={}))[n.Int=1]="Int",n[n.String=2]="String",n[n.Bool=3]="Bool",n[n.Array=4]="Array",n[n.Object=5]="Object",n[n.Button=6]="Button",(o=e.ContentScriptType||(e.ContentScriptType={})).MarkdownItPlugin="markdownItPlugin",o.CodeMirrorPlugin="codeMirrorPlugin"},740:function(t,e,i){var o=this&&this.__awaiter||function(t,e,i,o){return new(i||(i=Promise))((function(n,l){function r(t){try{u(o.next(t))}catch(t){l(t)}}function d(t){try{u(o.throw(t))}catch(t){l(t)}}function u(t){var e;t.done?n(t.value):(e=t.value,e instanceof i?e:new i((function(t){t(e)}))).then(r,d)}u((o=o.apply(t,e||[])).next())}))};Object.defineProperty(e,"__esModule",{value:!0});const n=i(632),l=i(808),r=6e4,d="showFolders",u="allowNewNotes",s="selectText";let a=!1,c=!1,f=!1,p={};function y(){return o(this,void 0,void 0,(function*(){a=yield n.default.settings.value(d),a&&(yield v())}))}function m(){return o(this,void 0,void 0,(function*(){c=yield n.default.settings.value(u)}))}function g(){return o(this,void 0,void 0,(function*(){f=yield n.default.settings.value(s)}))}function v(){return o(this,void 0,void 0,(function*(){p=yield function(){return o(this,void 0,void 0,(function*(){let t={};const e={fields:["id","title"],page:1};let i=yield n.default.data.get(["folders"],e);for(i.items.forEach((e=>t[e.id]=e.title));i.has_more;)e.page+=1,i=yield n.default.data.get(["folders"],e),i.items.forEach((e=>t[e.id]=e.title));return t}))}(),setTimeout((()=>{a&&v()}),r)}))}n.default.plugins.register({onStart:function(){return o(this,void 0,void 0,(function*(){yield function(){return o(this,void 0,void 0,(function*(){const t="QuickLinks";yield n.default.settings.registerSection(t,{description:"Quick Links Plugin Settings",label:"Quick Links",iconName:"fas fa-link"}),yield n.default.settings.registerSettings({[d]:{public:!0,section:t,type:l.SettingItemType.Bool,value:a,label:"Show Notebooks"},[u]:{public:!0,section:t,type:l.SettingItemType.Bool,value:c,label:"Allow new notes"},[s]:{public:!0,section:t,type:l.SettingItemType.Bool,value:f,label:"Select link text after inserting"}}),yield y(),yield m(),yield m(),yield g(),yield n.default.settings.onChange((t=>{t.keys.indexOf(d)>=0&&y(),t.keys.indexOf(u)>=0&&m(),t.keys.indexOf(s)>=0&&g()}))}))}(),yield n.default.contentScripts.register(l.ContentScriptType.CodeMirrorPlugin,"quickLinks","./contentScript/index.js"),yield n.default.contentScripts.onMessage("quickLinks",(t=>o(this,void 0,void 0,(function*(){const e=(yield n.default.workspace.selectedNoteIds())[0];if("getNotes"===t.command){const i=t.prefix;let l=yield function(t){return o(this,void 0,void 0,(function*(){return""===t?(yield n.default.data.get(["notes"],{fields:["id","title","parent_id"],order_by:"updated_time",order_dir:"DESC",limit:21})).items:(yield n.default.data.get(["search"],{fields:["id","title","parent_id"],limit:21,query:`title:${t.trimRight()}*`})).items}))}(i);return{notes:l.filter((t=>t.id!==e)).map((t=>({id:t.id,title:t.title,folder:p[t.parent_id]}))),showFolders:a,allowNewNotes:c,selectText:f}}if("createNote"===t.command){const e=yield n.default.workspace.selectedNote(),i=yield n.default.data.get(["folders",e.parent_id]);return{newNote:yield n.default.data.post(["notes"],null,{is_todo:t.todo,title:t.title,parent_id:i.id})}}}))))}))}})}},e={};!function i(o){var n=e[o];if(void 0!==n)return n.exports;var l=e[o]={exports:{}};return t[o].call(l.exports,l,l.exports,i),l.exports}(740)})();                                                          manifest.json                                                                                       000644                  0000000612  14577127034 010416  0                                                                                                    ustar 00                                                                000000  000000                                                                                                                                                                         {
	"manifest_version": 1,
	"id": "com.whatever.quick-links",
	"app_min_version": "1.8.1",
	"version": "1.3.2",
	"name": "Quick Links",
	"description": "Create links to other notes",
	"author": "Roman Musin",
	"homepage_url": "https://discourse.joplinapp.org/t/quick-links-plugin/14214",
	"repository_url": "https://github.com/roman-r-m/joplin-plugin-quick-links",
	"keywords": []
}
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      